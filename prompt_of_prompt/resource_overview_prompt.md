 Azure Resource Overview Implementation

## Overview

Implement a new webpage to show all Azure resources across all subscriptions, organized in a hierarchical structure grouped by subscription and resource group.

## Hierarchy Structure

The resources should be displayed in the following hierarchical format, **sorted by resource count in descending order** at each level:

```
📁 Subscription-01 (25)
    📂 Resource Group1 (15)
      📊 Resource type 1 (8)
        • Resource 01
        • Resource 02
        • Resource 03
        • Resource 04
        • Resource 05
        • Resource 06
        • Resource 07
        • Resource 08
      📊 Resource type 2 (7)
        • Resource 01
        • Resource 02
        • Resource 03
        • Resource 04
        • Resource 05
        • Resource 06
        • Resource 07
    📂 Resource Group2 (10)
      📊 Resource type 1 (6)
        • Resource 01
        • Resource 02
        • Resource 03
        • Resource 04
        • Resource 05
        • Resource 06
      📊 Resource type 2 (4)
        • Resource 01
        • Resource 02
        • Resource 03
        • Resource 04
```

## Requirements

### Design Patterns
- Follow the **exact same design pattern** as the `dns-private` subcommand
- Adhere to Python code best practices
- Adhere to JavaScript code best practices
- Use tailwind.css style in dark mode, add a button to switch between dark and light mode
- The list should be foldable and all the components should be well-aligned according to the page size
- When selecting subscription, resource group or resource type in the dropdown menu, the counts of them in the summary section should update to reflect the counts based on the filtered resources
- **Sort hierarchy by resource count**: All levels (subscriptions, resource groups, resource types) should be sorted in descending order by their resource count


### Features
1. **Summary Section**: Display overview statistics at the top with count bubbles
2. **Filter Controls**: Add dropdown buttons for filtering by:
   - Subscription
   - Resource Group
   - Resource Type
3. **Interactive Web Interface**: Generate HTML with embedded JavaScript for dynamic filtering
4. **Sorting**: Sort all hierarchy levels by resource count in descending order
5. **Count Indicators**: Visual count indicators using bubbles for better visual hierarchy

### Command Integration
- Add as a sub-command for Azure provider
- Command syntax: `./cloudqx.py azure resource-overview -web`
- Generate HTML file directly under `./html/` folder, don't put it in sub-folder
- Start web server and launch browser automatically

## Implementation Steps

1. **Create PRD**: Write Product Requirements Document as `implementation_azure_resource_overview.md` in `implementation_prompts/` folder
2. **Implement Core Logic**: Follow existing patterns from codebase
3. **Reuse Utilities**: Leverage existing common utilities and avoid reinventing the wheel

## Sample Resource Fetching Code

```python
from azure.identity import DefaultAzureCredential
from azure.mgmt.resource.subscriptions import SubscriptionClient
from azure.mgmt.resourcegraph import ResourceGraphClient
from azure.mgmt.resourcegraph.models import QueryRequest, QueryRequestOptions

def get_all_resources():
    # Authenticate using environment or managed identity
    credential = DefaultAzureCredential()

    # Step 1: Retrieve all subscription IDs accessible by the credential
    subs_client = SubscriptionClient(credential)
    subs = [sub.subscription_id for sub in subs_client.subscriptions.list()]

    if not subs:
        print("No subscriptions found or missing proper permissions.")
        return

    # Step 2: Initialize Resource Graph client
    rg_client = ResourceGraphClient(credential)

    # Step 3: Define the query to fetch all resources
    kusto_query = "Resources | project id, name, type, location, resourceGroup, subscriptionId"

    # Step 4: Prepare the query request with options (objectArray for easier parsing)
    request = QueryRequest(
        subscriptions=subs,
        query=kusto_query,
        options=QueryRequestOptions(result_format="objectArray")
    )

    # Step 5: Execute the query
    response = rg_client.resources(request)

    # Step 6: Handle pagination using skip_token
    all_data = response.data
    skip_token = getattr(response, 'skip_token', None)

    while skip_token:
        request.options.skip_token = skip_token
        response = rg_client.resources(request)
        all_data.extend(response.data)
        skip_token = getattr(response, 'skip_token', None)

    # Step 7: Print or process the results
    print(f"Total resources retrieved: {len(all_data)}")
    for item in all_data:
        print(f"{item['subscriptionId']} | {item['resourceGroup']} | {item['type']} | {item['name']}")

    return all_data

if __name__ == "__main__":
    get_all_resources()
```

## Notes

- **Security**: Follow secure coding practices for input validation and output encoding
- **Performance**: Implement efficient data processing for large resource sets
- **Usability**: Ensure responsive design and intuitive user interface
- **Sorting Logic**: Implement consistent sorting by count across all hierarchy levels (subscriptions → resource groups → resource types)
- **Count Indicators**: Implement dynamic bubble assignment for better visual hierarchy
- **Accessibility**: Ensure bubbles have appropriate contrast and include text labels for screen readers

## Critical Implementation Warnings - MUST READ BEFORE CODING! ⚠️

### 🚨 AVOID DESTRUCTIVE JAVASCRIPT FILTERING - COMMON MISTAKE!
**This is a very common bug pattern that you MUST avoid when implementing filtering!**

#### ❌ WRONG Implementation (WILL BREAK FILTER RESET):
```javascript
getFilteredData() {
    // ❌ DANGER: This pattern will break filter reset functionality!
    const clonedData = JSON.parse(JSON.stringify(this.data));
    return clonedData.filter(subscription => {
        // ❌ DANGER: Modifying arrays in place permanently destroys original data
        subscription.resource_groups = subscription.resource_groups.filter(...);
        return true;
    });
}
```

#### ✅ CORRECT Implementation (PRESERVES FILTER RESET):
```javascript
getFilteredData() {
    // ✅ SAFE: Create fresh result without modifying original data
    const result = [];
    for (const subscription of this.data) {
        if (this.filters.subscription && subscription.subscription_name !== this.filters.subscription) {
            continue; // Skip, don't modify
        }
        // ✅ SAFE: Build new objects instead of modifying existing ones
        const filteredSubscription = {
            ...subscription,
            resource_groups: [],
            resource_count: 0
        };
        // Continue building hierarchy without destroying original data...
    }
    return result;
}
```

#### Why This Pattern is Critical:
- **Potential Problem**: Users apply filters then can't reset to "All" - table shows stale data
- **Root Cause**: Original data structure gets permanently modified and can't be restored
- **Prevention**: Never modify original data - always build fresh filtered results

### Implementation Guidelines to Prevent This Bug:
- **Never Modify Original Data**: Keep `this.data` unchanged throughout the application lifecycle
- **Build Fresh Results**: Always create new objects/arrays when filtering
- **Test Filter Reset**: Ensure "Clear All Filters" restores complete dataset without page refresh
- **Implement Proper Reset**: Include comprehensive filter state reset and dropdown re-population

<!-- Contains AI-generated edits. -->