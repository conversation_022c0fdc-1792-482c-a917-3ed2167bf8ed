---
mode: 'agent'
---

# Combined Prompt for Code Generation

This combined prompt includes all coding standards, best practices, and guidelines for this OAuth Flow Learning project. Follow these standards when generating new features, making changes, or writing tests.

## Core Guidelines

#file:./coding_rules_and_guidelines.prompt.md

## Implementation Guidelines

#file:./implementation_guidelines.prompt.md

## Language-Specific Best Practices

### Python Development
#file:./python_best_practices.prompt.md

### Python Project Guidelines
#file:./python_project_guidelines.prompt.md

### CSS Development
#file:./css_best_practices.prompt.md

## Security & Caching

### Security Best Practices
#file:./security_best_practices.prompt.md

### Secret Detection Guidelines
#file:./check-for-secrets.prompt.md

### Cache Implementation Guidelines
#file:./cache_implementation_guidelines.prompt.md

---

## Your Goal
Follow all the standards, guidelines, and best practices above when:
- Generating new features or components
- Writing or updating tests
- Refactoring existing code
- Implementing security measures
- Working with caching mechanisms
- Making any code changes to this OAuth flow learning project

Remember to always test your changes by starting up a new server and ensure all existing functionality continues to work properly.

<!-- Contains AI-generated edits. -->
