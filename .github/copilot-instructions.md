# 🤖 CloudQX AI Coding Instructions

CloudQX is a multi-cloud infrastructure management CLI tool built with Python, focusing on Azure support with HTML report generation.

## 🏗️ Architecture Overview

**Provider Pattern**: Each cloud provider (`azure/`, `aws/`, `gcp/`) has its own module structure:
- `providers/azure/__main__.py` - Entry point router supporting: `organization`, `user`, `vnet`, `dns-private`, `resource-overview`
- `providers/azure/lib/common/` - Shared utilities (auth, config, token management)
- `providers/azure/lib/{module}/` - Module-specific logic (user, vnet, etc.)

**Dual Authentication**: Supports both Global Azure (`AzureCloud`) and China Azure (`AzureChinaCloud`) with auto-detection via `--cloud` parameter.

**HTML-First Output**: All modules generate rich HTML reports in `html/` directory with embedded CSS/JS for interactive features.

## 🔧 Development Patterns

**CLI Structure**: Uses `typer` with nested sub-commands:
```python
# Main app delegates to subprocess calls for provider modules
app.add_typer(azure_app, name="azure")
subprocess.run([sys.executable, "-m", "providers.azure", module_name])
```

**Command Execution**: When running terminal commands, always append `echo $?` to check exit status and verify command success.

**Authentication Singleton**: `providers/azure/lib/common/auth.py` implements credential caching with thread-safe singleton pattern for `DefaultAzureCredential` and `ChinaAzureCliCredential`.

**Module Entry Points**: Each provider module (user.py, vnet.py) can run standalone via `python -m providers.azure user` or through main CLI.

**Testing**: Uses `unittest` with mocking patterns. Test files follow `test_{module}.py` naming. Run with `pytest` (configured in `pytest.ini`).

## 🚀 Key Commands

**Development**:
```bash
python cloudqx.py azure user --debug --cloud global
./scripts/generate_reports.sh  # Batch HTML report generation
python -m providers.azure user  # Direct module execution
```

**Testing**: `pytest tests/` (uses conftest.py for path setup)

## 💡 CloudQX Conventions

- **Cloud Detection**: Always support `--cloud` parameter with validation for `['global', 'china']`
- **HTML Reports**: Store in `html/` with timestamp naming: `{module}_{timestamp}.html`
- **Logging**: Use module-level loggers with filename:line format from `lib/common/config.py`
- **Token Caching**: Leverage `lib/common/token_manager.py` for credential reuse across operations
- **Error Handling**: Catch Azure SDK exceptions and provide user-friendly messages with `typer.echo()`

## 🔐 Security Practices

- **Azure Credentials**: Use `DefaultAzureCredential` with appropriate authority hosts via `lib/common/auth.py`
- **Input Validation**: Validate cloud environment parameters: `['global', 'china']` only
- **Token Security**: Never log Azure tokens; use token manager for secure caching
- **Secret Management**: Never store credentials in code - rely on Azure CLI or managed identity
- **Data Privacy**: Default to privacy-preserving data handling — redact PII from logs by default

## 🐍 Python (CloudQX Specific)

- **Azure SDK**: Always use parameterized queries with Azure Resource Graph - never string concatenation
- **Input Validation**: Validate cloud environment and resource parameters using `typer.Option` constraints
- **Error Handling**: Catch `ClientAuthenticationError` and provide user-friendly messages via `typer.echo()`
- **Logging**: Use module-level loggers from `config.py` - never log sensitive Azure resource data
- **File Operations**: HTML reports go to `html/` directory with timestamp naming for security
- **Token Management**: Use singleton pattern in `auth.py` - never create multiple credential instances

## 🚫 CloudQX Do Not Suggest

### Python
- Do not build SQL queries with string concat, f-strings, or `.format()` — always use parameterized queries
- Do not use `eval`, `exec`, or dynamic imports on user input — these are unsafe unless tightly sandboxed
- Do not log sensitive values (e.g. API keys, passwords) or full stack traces with PII
- Do not load pickle or YAML files from untrusted sources without safe loaders and validation
- Do not use insecure hash functions like `md5` or `sha1` for password storage — use a modern password hashing lib
- Do not commit `.env` files or hardcode secrets — use secrets management infrastructure

### CloudQX Specific
- Do not create multiple Azure credential instances — use the singleton from `auth.py`
- Do not hardcode cloud environment values — always validate against `['global', 'china']`
- Do not generate HTML files outside the `html/` directory structure
- Do not bypass the module entry point pattern in `__main__.py` — use proper routing
- Do not log Azure tokens or sensitive resource information in debug output

## 💡 Developer Tips

- If you're working with Azure resources, always use the established auth patterns from `lib/common/auth.py`
- For new provider modules, follow the `__main__.py` routing pattern and create dedicated lib subdirectories
- HTML reports should use timestamp naming and include embedded CSS/JS for portability
- Test with both `--cloud global` and `--cloud china` to ensure cross-cloud compatibility
- Use `typer.echo()` for user-facing messages and proper logging for debugging

## 🟨 JavaScript (CloudQX HTML Reports)

- **Embedded Scripts**: All JavaScript is embedded directly in HTML files for portability - no external dependencies
- **Class-based Architecture**: Use ES6 classes for report managers (e.g., `DNSPrivateReport`, `ResourceHierarchy`)
- **DOM Ready Pattern**: Always check `document.readyState` and use `DOMContentLoaded` for initialization
- **Hash Navigation**: Implement client-side routing using `window.location.hash` for single-page report navigation
- **Table Interactions**: Provide search, sort, and filter functionality with `sortDirections` state management
- **Data Visualization**: Use vanilla JavaScript for charts and hierarchical displays - avoid external chart libraries
- **Browser Compatibility**: Write ES6+ code that works in modern browsers without transpilation

## 🚫 JavaScript Do Not Suggest

- Do not use external JavaScript libraries or CDNs - reports must be self-contained HTML files
- Do not use `document.write()` or `innerHTML` with unsanitized data - use safe DOM methods
- Do not implement complex build processes - JavaScript should be directly embeddable
- Do not rely on external CSS frameworks - use embedded styles for portability

## ⚛️ React.js Framework (CloudQX Web Components)

- **Component Architecture**: Use functional components with hooks for CloudQX dashboard and web interfaces
- **State Management**: Prefer `useState` and `useContext` for local state; consider Redux Toolkit for complex state
- **TypeScript Integration**: Always use TypeScript with React for type safety in CloudQX components
- **Azure Integration**: Create reusable components for Azure resource displays (ResourceCard, MetricsChart, etc.)
- **Error Boundaries**: Implement error boundaries for graceful handling of Azure API failures
- **Accessibility**: Follow ARIA guidelines and ensure keyboard navigation for all interactive elements
- **Performance**: Use `React.memo`, `useMemo`, and `useCallback` for optimizing re-renders with large Azure datasets
- **Testing**: Use React Testing Library with Jest for component testing

## 🎨 Tailwind CSS Framework (CloudQX Styling)

- **Utility-First Approach**: Use Tailwind utility classes for consistent CloudQX design system
- **Responsive Design**: Implement mobile-first responsive layouts with Tailwind's breakpoint system
- **Custom Theme**: Configure `tailwind.config.js` with CloudQX brand colors and Azure-specific color schemes
- **Component Patterns**: Create reusable component classes for Azure resource cards, status indicators, and dashboards
- **Dark Mode**: Implement dark mode support using Tailwind's `dark:` variant for better user experience
- **Performance**: Use Tailwind's purge/content configuration to minimize CSS bundle size
- **Consistent Spacing**: Use Tailwind's spacing scale for consistent margins, padding, and layout
- **Hover & Focus States**: Implement consistent interactive states using Tailwind's state variants

## 🚫 React.js & Tailwind CSS Do Not Suggest

### React.js
- Do not use class components - prefer functional components with hooks
- Do not mutate state directly - always use state setters or reducers
- Do not use deprecated lifecycle methods or legacy context API
- Do not inline complex logic in JSX - extract to custom hooks or utility functions
- Do not use `any` type in TypeScript - properly type Azure SDK responses and component props

### Tailwind CSS
- Do not use traditional CSS files alongside Tailwind unless absolutely necessary
- Do not use `!important` - leverage Tailwind's specificity and utility composition
- Do not create overly complex custom CSS - prefer Tailwind utilities and component patterns
- Do not ignore responsive design - always consider mobile-first approach for CloudQX interfaces

<!-- Contains AI-generated edits. -->
