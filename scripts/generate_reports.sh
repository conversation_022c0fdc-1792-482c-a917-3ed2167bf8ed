#!/bin/bash

# CloudQX HTML Report Generator Script
# This script helps generate HTML reports for various Azure resources using CloudQX

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
CLOUDQX_SCRIPT="${PROJECT_DIR}/cloudqx.py"
HTML_DIR="${PROJECT_DIR}/html"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display help
show_help() {
    cat << EOF
CloudQX HTML Report Generator

USAGE:
    $0 [COMMAND] [OPTIONS]

COMMANDS:
    user                    Generate Azure user report
    organization            Generate Azure organization report
    vnet                    Generate Azure virtual network report
    dns-private             Generate Azure DNS private resources report
    resource-overview       Generate Azure resource overview report
    all                     Generate all reports
    help                    Show this help message

OPTIONS FOR ORGANIZATION:
    -r, --include-resources Include all resources within each resource group
    -f, --full-list         Show full list of resources (use with -r)

GLOBAL OPTIONS:
    -c, --cloud CLOUD       Azure cloud environment (global|china|all) [default: all]
    -d, --debug             Enable debug logging
    -w, --web               Launch web server and open report in browser (HTML only)
    -o, --output-type TYPE  Output format (html|json|csv|yaml|table|console) [default: html]
    --output-file FILE      Output file path (optional, uses default naming if not specified)
    --no-console            Disable console output (only write to file)
    -h, --help              Show this help message

EXAMPLES:
    # Generate user report (HTML)
    $0 user

    # Generate organization report with resources (HTML)
    $0 organization -r

    # Generate organization report with full resource list (HTML)
    $0 organization -r -f

    # Generate resource overview report (HTML)
    $0 resource-overview

    # Generate all reports with web server (HTML)
    $0 all -w

    # Generate organization report for China cloud (HTML)
    $0 organization -c china -r -f

    # Generate resource overview report for global cloud (HTML)
    $0 resource-overview -c global -w

    # Generate user report in JSON format
    $0 user -o json

    # Generate organization report in CSV format with custom file
    $0 organization -o csv --output-file org_report.csv

    # Generate all reports in YAML format
    $0 all -o yaml

    # Generate user report as table in console only
    $0 user -o table --no-console

NOTES:
    - Reports are generated in the '${HTML_DIR}' directory
    - Use -w/--web to automatically open reports in your browser
    - The organization command supports -r and -f flags for enhanced reporting
    - All commands support debug mode with -d/--debug
    - Azure CLI must be installed and you must be logged in (az login)
    - When no -c/--cloud flag is specified, reports are generated for both global and china clouds
    - When using 'all' command, reports are generated for all modules and all specified clouds
    - The script automatically sets the Azure cloud environment using 'az cloud set'
    - Available modules: user, organization, vnet, dns-private, resource-overview

EOF
}

# Validate CloudQX installation
validate_cloudqx() {
    if [[ ! -f "${CLOUDQX_SCRIPT}" ]]; then
        log_error "CloudQX script not found at ${CLOUDQX_SCRIPT}"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    if ! command -v az &> /dev/null; then
        log_error "Azure CLI (az) is required but not installed"
        exit 1
    fi
    
    # Test if CloudQX can run
    if ! python3 "${CLOUDQX_SCRIPT}" --help &> /dev/null; then
        log_error "CloudQX script is not executable or has dependency issues"
        exit 1
    fi
}

# Parse command line arguments
parse_args() {
    COMMAND=""
    CLOUD="all"
    DEBUG=false
    WEB=false
    INCLUDE_RESOURCES=false
    FULL_LIST=false
    OUTPUT_TYPE="html"
    OUTPUT_FILE=""
    NO_CONSOLE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            user|organization|vnet|dns-private|resource-overview|all)
                COMMAND="$1"
                shift
                ;;
            -c|--cloud)
                CLOUD="$2"
                if [[ "$CLOUD" != "global" && "$CLOUD" != "china" && "$CLOUD" != "all" ]]; then
                    log_error "Invalid cloud environment. Must be 'global', 'china', or 'all'"
                    exit 1
                fi
                shift 2
                ;;
            -d|--debug)
                DEBUG=true
                shift
                ;;
            -w|--web)
                WEB=true
                shift
                ;;
            -r|--include-resources)
                INCLUDE_RESOURCES=true
                shift
                ;;
            -f|--full-list)
                FULL_LIST=true
                shift
                ;;
            -o|--output-type)
                OUTPUT_TYPE="$2"
                if [[ "$OUTPUT_TYPE" != "html" && "$OUTPUT_TYPE" != "json" && "$OUTPUT_TYPE" != "csv" && "$OUTPUT_TYPE" != "yaml" && "$OUTPUT_TYPE" != "table" && "$OUTPUT_TYPE" != "console" ]]; then
                    log_error "Invalid output type. Must be 'html', 'json', 'csv', 'yaml', 'table', or 'console'"
                    exit 1
                fi
                shift 2
                ;;
            --output-file)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            --no-console)
                NO_CONSOLE=true
                shift
                ;;
            -h|--help|help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    if [[ -z "$COMMAND" ]]; then
        log_error "No command specified"
        show_help
        exit 1
    fi
    
    # Validate flag combinations
    if [[ "$FULL_LIST" == true && "$INCLUDE_RESOURCES" == false ]]; then
        log_warning "-f/--full-list requires -r/--include-resources, enabling it automatically"
        INCLUDE_RESOURCES=true
    fi

    if [[ "$COMMAND" != "organization" && ("$INCLUDE_RESOURCES" == true || "$FULL_LIST" == true) ]]; then
        log_warning "-r and -f flags are only supported for the organization command"
        INCLUDE_RESOURCES=false
        FULL_LIST=false
    fi

    # Validate web option only works with HTML
    if [[ "$WEB" == true && "$OUTPUT_TYPE" != "html" ]]; then
        log_error "--web option is only supported with HTML output format"
        exit 1
    fi
}

# Build CloudQX command arguments
build_cloudqx_args() {
    local subcommand="$1"
    # Allow overriding cloud for this invocation; default to global CLOUD variable
    local target_cloud="${2:-$CLOUD}"
    local args=()
    
    args+=("azure" "$subcommand")
    
    # Add global options
    if [[ "$DEBUG" == true ]]; then
        args+=("--debug")
    fi
    
    # Add cloud option only for China environment
    if [[ "$target_cloud" == "china" ]]; then
        args+=("--cloud" "$target_cloud")
    fi
    
    if [[ "$WEB" == true ]]; then
        args+=("--web")
    fi
    
    # Add organization-specific options
    if [[ "$subcommand" == "organization" ]]; then
        if [[ "$INCLUDE_RESOURCES" == true ]]; then
            args+=("--include-resources")
        fi
        
        if [[ "$FULL_LIST" == true ]]; then
            args+=("--full-list")
        fi
    fi
    
    echo "${args[@]}"
}

# Set Azure cloud environment
set_azure_cloud() {
    local cloud="$1"
    local cloud_name=""
    
    case "$cloud" in
        china)
            cloud_name="AzureChinaCloud"
            ;;
        global)
            cloud_name="AzureCloud"
            ;;
        *)
            log_error "Unknown cloud environment: $cloud"
            exit 1
            ;;
    esac
    
    log_info "Setting Azure cloud to: $cloud_name"
    
    if ! az cloud set --name "$cloud_name" &> /dev/null; then
        log_error "Failed to set Azure cloud to $cloud_name"
        log_error "Make sure you are logged in with 'az login'"
        exit 1
    fi
    
    # Verify the cloud setting
    local current_cloud
    current_cloud=$(az cloud show --query name -o tsv 2>/dev/null)
    if [[ "$current_cloud" == "$cloud_name" ]]; then
        log_success "Azure cloud set to: $cloud_name"
    else
        log_warning "Azure cloud setting verification failed. Current: $current_cloud, Expected: $cloud_name"
    fi
}

# Execute CloudQX command
run_cloudqx() {
    local subcommand="$1"
    local cloud="${2:-$CLOUD}"
    local start_time
    local end_time
    local duration
    
    log_info "Generating $subcommand report for $cloud cloud..."
    
    # Set Azure cloud environment
    set_azure_cloud "$cloud"
    
    if [[ "$DEBUG" == true ]]; then
        log_info "Running: python3 ${CLOUDQX_SCRIPT} $(build_cloudqx_args "$subcommand")"
    fi
    
    start_time=$(date +%s)
    
    # Change to project directory to ensure proper module imports
    cd "${PROJECT_DIR}"
    
    # Execute the command
    if python3 "${CLOUDQX_SCRIPT}" $(build_cloudqx_args "$subcommand"); then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        log_success "$subcommand report for $cloud cloud generated successfully in ${duration}s"
        return 0
    else
        log_error "Failed to generate $subcommand report for $cloud cloud"
        return 1
    fi
}

# Generate all reports
generate_all_reports() {
    local commands=("user" "organization" "vnet" "dns-private" "resource-overview")
    # Determine which clouds to process based on CLOUD variable
    local clouds=()
    if [[ "$CLOUD" == "global" ]]; then
        clouds=("global")
    elif [[ "$CLOUD" == "china" ]]; then
        clouds=("china")
    else
        # Default for 'all' or any other value - process both clouds
        clouds=("global" "china")
    fi
    local failed_commands=()
    local total_start_time
    local total_end_time
    local total_duration
    
    log_info "Generating all Azure reports for clouds: ${clouds[*]}"
    total_start_time=$(date +%s)
    
    for cloud in "${clouds[@]}"; do
        log_info "=== Processing $cloud cloud ==="
        for cmd in "${commands[@]}"; do
            log_info "--- Generating $cmd report for $cloud cloud ---"
            if ! run_cloudqx "$cmd" "$cloud"; then
                failed_commands+=("$cmd ($cloud)")
            fi
            echo
        done
        echo
    done
    
    total_end_time=$(date +%s)
    total_duration=$((total_end_time - total_start_time))
    
    # Summary
    echo
    log_info "=== GENERATION SUMMARY ==="
    if [[ ${#failed_commands[@]} -eq 0 ]]; then
        log_success "All reports generated successfully in ${total_duration}s"
    else
        log_warning "Some reports failed to generate:"
        for cmd in "${failed_commands[@]}"; do
            log_error "  - $cmd"
        done
        log_info "Total time: ${total_duration}s"
    fi
    
    # Show generated files
    if [[ -d "$HTML_DIR" ]]; then
        echo
        log_info "Generated files in $HTML_DIR:"
        ls -la "$HTML_DIR"/*.html 2>/dev/null || log_warning "No HTML files found"
    fi
}

# Main function
main() {
    log_info "CloudQX HTML Report Generator"
    log_info "Project directory: $PROJECT_DIR"
    
    # Validate environment
    validate_cloudqx
    
    # Parse arguments
    parse_args "$@"
    
    # Show configuration if debug mode
    if [[ "$DEBUG" == true ]]; then
        log_info "Configuration:"
        log_info "  Command: $COMMAND"
        log_info "  Cloud: $CLOUD"
        log_info "  Debug: $DEBUG"
        log_info "  Web: $WEB"
        log_info "  Include Resources: $INCLUDE_RESOURCES"
        log_info "  Full List: $FULL_LIST"
    fi
    
    # Create HTML directory if it doesn't exist
    if [[ ! -d "$HTML_DIR" ]]; then
        log_info "Creating HTML directory: $HTML_DIR"
        mkdir -p "$HTML_DIR"
    fi
    
    # Execute command
    case "$COMMAND" in
        all)
            generate_all_reports
            ;;
        *)
            # If 'all' clouds option specified for single command, run for both environments
            if [[ "$CLOUD" == "all" ]]; then
                for env in global china; do
                    run_cloudqx "$COMMAND" "$env"
                    echo
                done
            else
                run_cloudqx "$COMMAND" "$CLOUD"
            fi
            ;;
    esac
    
    echo
    log_info "Report generation completed!"
    
    if [[ "$WEB" == false ]]; then
        log_info "Tip: Use -w/--web flag to automatically open reports in your browser"
    fi
}

# Run main function with all arguments
main "$@"

# Contains AI-generated edits.
