#!/usr/bin/env python3
"""
Test script to verify account filtering functionality
"""

def test_account_filtering():
    """Test the account filtering logic"""
    
    # Mock data similar to what would be returned from Alibaba API
    test_resources = [
        {
            'resource_id': 'res-1',
            'resource_type': 'ECS::Instance',
            'account_name': 'blz-p-ow-game',
            'region_id': 'cn-beijing'
        },
        {
            'resource_id': 'res-2', 
            'resource_type': 'RDS::Database',
            'account_name': 'blz-p-ow-game',
            'region_id': 'cn-beijing'
        },
        {
            'resource_id': 'res-3',
            'resource_type': 'OSS::Bucket', 
            'account_name': 'other-account',
            'region_id': 'cn-beijing'
        },
        {
            'resource_id': 'res-4',
            'resource_type': 'ECS::Instance',
            'account_name': 'blz-p-diablo4-prod',
            'region_id': 'cn-beijing'
        }
    ]
    
    # Test account filtering logic
    account_filter = 'blz-p-ow-game'
    
    print(f"Original resources: {len(test_resources)}")
    print("Resources before filtering:")
    for res in test_resources:
        print(f"  - {res['resource_id']}: {res['account_name']}")
    
    # Apply the same filtering logic as in data.py
    filtered_results = []
    for res in test_resources:
        account_name = res.get('account_name', '')
        # Check if the account name contains the filter string (case-insensitive)
        if account_filter.lower() in account_name.lower():
            filtered_results.append(res)
    
    print(f"\nFiltered resources (account='{account_filter}'): {len(filtered_results)}")
    print("Resources after filtering:")
    for res in filtered_results:
        print(f"  - {res['resource_id']}: {res['account_name']}")
    
    # Verify expected results
    expected_count = 2  # Should match 'blz-p-ow-game' exactly
    if len(filtered_results) == expected_count:
        print(f"\n✅ Test PASSED: Expected {expected_count} resources, got {len(filtered_results)}")
    else:
        print(f"\n❌ Test FAILED: Expected {expected_count} resources, got {len(filtered_results)}")
    
    # Test partial matching
    print(f"\n--- Testing partial matching ---")
    partial_filter = 'blz-p'
    partial_filtered = []
    for res in test_resources:
        account_name = res.get('account_name', '')
        if partial_filter.lower() in account_name.lower():
            partial_filtered.append(res)
    
    print(f"Partial filter '{partial_filter}' matches: {len(partial_filtered)} resources")
    for res in partial_filtered:
        print(f"  - {res['resource_id']}: {res['account_name']}")

if __name__ == "__main__":
    test_account_filtering()
