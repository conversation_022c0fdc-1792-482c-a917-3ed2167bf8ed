#!/usr/bin/env python3
"""
Tests for Azure Resource Overview data collection module.
"""
import unittest
from unittest.mock import MagicMock, patch, call
from providers.azure.lib.resource_overview.data import (
    get_all_resources,
    _organize_resources_hierarchically,
    calculate_overview_statistics
)


class TestResourceOverviewData(unittest.TestCase):
    """Test cases for resource overview data collection."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_credential = MagicMock()
        
        # Sample resource data
        self.sample_resources = [
            {
                'id': '/subscriptions/sub1/resourceGroups/rg1/providers/Microsoft.Storage/storageAccounts/storage1',
                'name': 'storage1',
                'type': 'Microsoft.Storage/storageAccounts',
                'location': 'eastus',
                'resourceGroup': 'rg1',
                'subscriptionId': 'sub1'
            },
            {
                'id': '/subscriptions/sub1/resourceGroups/rg1/providers/Microsoft.Compute/virtualMachines/vm1',
                'name': 'vm1',
                'type': 'Microsoft.Compute/virtualMachines',
                'location': 'eastus',
                'resourceGroup': 'rg1',
                'subscriptionId': 'sub1'
            },
            {
                'id': '/subscriptions/sub1/resourceGroups/rg2/providers/Microsoft.Storage/storageAccounts/storage2',
                'name': 'storage2',
                'type': 'Microsoft.Storage/storageAccounts',
                'location': 'westus',
                'resourceGroup': 'rg2',
                'subscriptionId': 'sub1'
            },
            {
                'id': '/subscriptions/sub2/resourceGroups/rg3/providers/Microsoft.Network/virtualNetworks/vnet1',
                'name': 'vnet1',
                'type': 'Microsoft.Network/virtualNetworks',
                'location': 'centralus',
                'resourceGroup': 'rg3',
                'subscriptionId': 'sub2'
            }
        ]
        
        self.subscription_map = {
            'sub1': 'Subscription 1',
            'sub2': 'Subscription 2'
        }

    @patch('providers.azure.lib.resource_overview.data.ResourceGraphClient')
    @patch('providers.azure.lib.resource_overview.data.SubscriptionClient')
    def test_get_all_resources_success(self, mock_subscription_client, mock_resource_graph_client):
        """Test successful resource retrieval."""
        # Mock subscription client
        mock_sub_instance = MagicMock()
        mock_subscription_client.return_value = mock_sub_instance
        
        mock_subscription1 = MagicMock()
        mock_subscription1.subscription_id = 'sub1'
        mock_subscription1.display_name = 'Subscription 1'
        
        mock_subscription2 = MagicMock()
        mock_subscription2.subscription_id = 'sub2'
        mock_subscription2.display_name = 'Subscription 2'
        
        mock_sub_instance.subscriptions.list.return_value = [mock_subscription1, mock_subscription2]
        
        # Mock resource graph client
        mock_rg_instance = MagicMock()
        mock_resource_graph_client.return_value = mock_rg_instance
        
        mock_response = MagicMock()
        mock_response.data = self.sample_resources
        mock_response.skip_token = None
        mock_rg_instance.resources.return_value = mock_response
        
        # Execute test
        result = get_all_resources(self.mock_credential)
        
        # Verify results
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)  # 2 subscriptions
        
        # Verify subscription ordering (by resource count descending)
        self.assertEqual(result[0]['subscription_id'], 'sub1')  # 3 resources
        self.assertEqual(result[1]['subscription_id'], 'sub2')  # 1 resource
        
        # Verify resource counts
        self.assertEqual(result[0]['resource_count'], 3)
        self.assertEqual(result[1]['resource_count'], 1)

    @patch('providers.azure.lib.resource_overview.data.ResourceGraphClient')
    @patch('providers.azure.lib.resource_overview.data.SubscriptionClient')
    def test_get_all_resources_with_pagination(self, mock_subscription_client, mock_resource_graph_client):
        """Test resource retrieval with pagination."""
        # Mock subscription client
        mock_sub_instance = MagicMock()
        mock_subscription_client.return_value = mock_sub_instance
        
        mock_subscription = MagicMock()
        mock_subscription.subscription_id = 'sub1'
        mock_subscription.display_name = 'Subscription 1'
        mock_sub_instance.subscriptions.list.return_value = [mock_subscription]
        
        # Mock resource graph client with pagination
        mock_rg_instance = MagicMock()
        mock_resource_graph_client.return_value = mock_rg_instance
        
        # First response with skip_token
        mock_response1 = MagicMock()
        mock_response1.data = self.sample_resources[:2]
        mock_response1.skip_token = 'token123'
        
        # Second response without skip_token
        mock_response2 = MagicMock()
        mock_response2.data = self.sample_resources[2:]
        mock_response2.skip_token = None
        
        mock_rg_instance.resources.side_effect = [mock_response1, mock_response2]
        
        # Execute test
        result = get_all_resources(self.mock_credential)
        
        # Verify pagination was handled
        self.assertEqual(mock_rg_instance.resources.call_count, 2)
        self.assertIsInstance(result, list)

    @patch('providers.azure.lib.resource_overview.data.SubscriptionClient')
    def test_get_all_resources_no_subscriptions(self, mock_subscription_client):
        """Test handling when no subscriptions are found."""
        # Mock subscription client with no subscriptions
        mock_sub_instance = MagicMock()
        mock_subscription_client.return_value = mock_sub_instance
        mock_sub_instance.subscriptions.list.return_value = []
        
        # Execute test
        result = get_all_resources(self.mock_credential)
        
        # Verify empty result
        self.assertEqual(result, [])

    def test_organize_resources_hierarchically(self):
        """Test hierarchical organization of resources."""
        result = _organize_resources_hierarchically(self.sample_resources, self.subscription_map)
        
        # Verify structure
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)  # 2 subscriptions
        
        # Verify sorting by resource count (sub1 has 3, sub2 has 1)
        self.assertEqual(result[0]['subscription_id'], 'sub1')
        self.assertEqual(result[0]['resource_count'], 3)
        self.assertEqual(result[1]['subscription_id'], 'sub2')
        self.assertEqual(result[1]['resource_count'], 1)
        
        # Verify subscription 1 structure
        sub1 = result[0]
        self.assertEqual(len(sub1['resource_groups']), 2)  # rg1, rg2
        
        # Verify resource group sorting (rg1 has 2, rg2 has 1)
        self.assertEqual(sub1['resource_groups'][0]['name'], 'rg1')
        self.assertEqual(sub1['resource_groups'][0]['resource_count'], 2)
        self.assertEqual(sub1['resource_groups'][1]['name'], 'rg2')
        self.assertEqual(sub1['resource_groups'][1]['resource_count'], 1)
        
        # Verify resource type sorting within rg1
        rg1 = sub1['resource_groups'][0]
        self.assertEqual(len(rg1['resource_types']), 2)  # Storage and Compute
        
        # Should be sorted by count (both have 1, so by type name)
        self.assertIn('Microsoft.Compute/virtualMachines', [rt['type'] for rt in rg1['resource_types']])
        self.assertIn('Microsoft.Storage/storageAccounts', [rt['type'] for rt in rg1['resource_types']])

    def test_calculate_overview_statistics(self):
        """Test statistics calculation."""
        hierarchical_data = _organize_resources_hierarchically(self.sample_resources, self.subscription_map)
        stats = calculate_overview_statistics(hierarchical_data)
        
        # Verify basic stats
        self.assertEqual(stats['total_resources'], 4)
        self.assertEqual(stats['total_subscriptions'], 2)
        self.assertEqual(stats['total_resource_groups'], 3)
        self.assertEqual(stats['total_resource_types'], 3)
        self.assertEqual(stats['total_locations'], 3)  # eastus, westus, centralus
        
        # Verify resource type counts
        expected_types = {
            'Microsoft.Storage/storageAccounts': 2,
            'Microsoft.Compute/virtualMachines': 1,
            'Microsoft.Network/virtualNetworks': 1
        }
        self.assertEqual(stats['resource_type_counts'], expected_types)
        
        # Verify top resource types
        self.assertEqual(len(stats['top_resource_types']), 3)
        self.assertEqual(stats['top_resource_types'][0], ('Microsoft.Storage/storageAccounts', 2))

    def test_organize_resources_empty_input(self):
        """Test organizing empty resource list."""
        result = _organize_resources_hierarchically([], {})
        self.assertEqual(result, [])

    def test_organize_resources_missing_fields(self):
        """Test organizing resources with missing fields."""
        incomplete_resources = [
            {
                'id': '/subscriptions/sub1/resourceGroups/rg1/providers/Microsoft.Storage/storageAccounts/storage1',
                'name': 'storage1',
                # Missing type, location, resourceGroup, subscriptionId
            }
        ]
        subscription_map = {'sub1': 'Subscription 1'}
        
        result = _organize_resources_hierarchically(incomplete_resources, subscription_map)
        
        # Should handle missing fields gracefully
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Verify default values are used
        resource = result[0]['resource_groups'][0]['resource_types'][0]['resources'][0]
        self.assertEqual(resource['type'], 'Unknown')
        self.assertEqual(resource['location'], '')
        self.assertEqual(resource['resource_group'], 'Unknown')

    def test_calculate_statistics_empty_data(self):
        """Test statistics calculation with empty data."""
        stats = calculate_overview_statistics([])
        
        expected_stats = {
            'total_resources': 0,
            'total_subscriptions': 0,
            'total_resource_groups': 0,
            'total_resource_types': 0,
            'locations': 0,
            'resource_type_counts': {},
            'top_resource_groups': [],
            'top_resource_types': []
        }
        
        # Check all required keys exist
        for key in expected_stats:
            self.assertIn(key, stats)
        
        # Check specific values
        self.assertEqual(stats['total_resources'], 0)
        self.assertEqual(stats['total_subscriptions'], 0)


if __name__ == '__main__':
    unittest.main()

# Contains AI-generated edits.
