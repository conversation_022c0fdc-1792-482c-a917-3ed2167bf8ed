import unittest
from unittest.mock import patch, MagicMock
import unittest
from unittest.mock import patch, MagicMock
from providers.azure.lib.user.display import display_users_with_roles

class TestDisplay(unittest.TestCase):

    @patch('builtins.print')
    def test_display_users_with_roles(self, mock_print):
        """Test the display_users_with_roles function."""
        users = [{'displayName': 'Test User', 'roles': ['Reader']}]
        
        display_users_with_roles(users)
        
        self.assertTrue(mock_print.called)
        # More specific assertions can be added here
        # For example, check if the user's name is in the output
        self.assertIn('Test User', " ".join(call[0][0] for call in mock_print.call_args_list if call[0]))


if __name__ == '__main__':
    unittest.main()
