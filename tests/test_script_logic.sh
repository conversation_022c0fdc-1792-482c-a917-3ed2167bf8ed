#!/bin/bash

# Test script to verify the generate_reports.sh logic

echo "=== Testing Cloud Logic ==="

# Test 1: No -c flag (should default to 'all')
echo "Test 1: ./scripts/generate_reports.sh user --debug (no -c flag)"
bash scripts/generate_reports.sh user --debug 2>&1 | grep -E "Cloud: |Generating.*cloud" | head -5

echo ""
echo "Test 2: ./scripts/generate_reports.sh user -c all --debug (explicit all)"
bash scripts/generate_reports.sh user -c all --debug 2>&1 | grep -E "Cloud: |Generating.*cloud" | head -5

echo ""
echo "Test 3: ./scripts/generate_reports.sh user -c global --debug (global only)"
bash scripts/generate_reports.sh user -c global --debug 2>&1 | grep -E "Cloud: |Generating.*cloud" | head -5

echo ""
echo "Test 4: ./scripts/generate_reports.sh user -c china --debug (china only)"
bash scripts/generate_reports.sh user -c china --debug 2>&1 | grep -E "Cloud: |Generating.*cloud" | head -5

echo ""
echo "=== Summary ==="
echo "✅ Script has been updated to:"
echo "  - Default CLOUD='all' when no -c flag specified"
echo "  - Process both global and china clouds when CLOUD='all'"
echo "  - Process only specified cloud when CLOUD='global' or 'china'"
echo "  - Handle 'all' command correctly for all specified clouds"
