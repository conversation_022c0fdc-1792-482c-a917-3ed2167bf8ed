import unittest
from unittest.mock import patch, mock_open
import json
from providers.azure.lib.user.export import export_users_rbac_to_json

class TestExport(unittest.TestCase):

    def setUp(self):
        self.users = [
            {'displayName': 'User 1', 'mail': '<EMAIL>'},
            {'displayName': 'User 2', 'mail': '<EMAIL>'}
        ]

    @patch('builtins.open', new_callable=mock_open)
    @patch('json.dump')
    def test_export_users_rbac_to_json(self, mock_json_dump, mock_file):
        """Test exporting users to a JSON file."""
        export_users_rbac_to_json(self.users, 'test.json')
        
        mock_file.assert_called_with('test.json', 'w', encoding='utf-8')
        self.assertTrue(mock_json_dump.called)


if __name__ == '__main__':
    unittest.main()
