import unittest
from unittest.mock import patch, mock_open
from providers.azure.lib.user.html_export import generate_html_report

class TestHtmlExport(unittest.TestCase):

    @patch('builtins.open', new_callable=mock_open)
    @patch('providers.azure.lib.user.html_export.webbrowser.open')
    @patch('providers.azure.lib.user.html_export.start_web_server')
    def test_generate_html_report(self, mock_start_web_server, mock_webbrowser_open, mock_file):
        """Test generating an HTML report from user data."""
        user_data = [{'displayName': 'Test User'}]
        
        generate_html_report(user_data, 'test.html', use_web_server=False)
        
        self.assertTrue(mock_file.called)
        self.assertTrue(mock_file().write.called)
        self.assertIn('Test User', mock_file().write.call_args[0][0])
        self.assertFalse(mock_webbrowser_open.called)
        self.assertFalse(mock_start_web_server.called)


if __name__ == '__main__':
    unittest.main()
