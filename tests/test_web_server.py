import unittest
from unittest.mock import patch, MagicMock
from http.server import HTTPServer
from providers.azure.lib.common.web_server import find_available_port, start_web_server

class TestWebServer(unittest.TestCase):

    def test_find_available_port(self):
        """Test finding an available port."""
        # The function now returns 0 to let the OS choose an available port
        # This is more reliable than trying to test port availability
        port = find_available_port(start_port=8000, max_attempts=10)
        self.assertIsInstance(port, int)
        self.assertEqual(port, 0)  # Should return 0 for OS to choose

    @patch.object(HTTPServer, 'serve_forever')
    def test_start(self, mock_serve_forever):
        """Test the start method of the web server."""
        from providers.azure.lib.common.web_server import SimpleWebServer
        server = SimpleWebServer()
        server.start()
        mock_serve_forever.assert_called_once()

    @patch.object(HTTPServer, 'shutdown')
    def test_stop(self, mock_shutdown):
        """Test the stop method of the web server."""
        from providers.azure.lib.common.web_server import SimpleWebServer
        server = SimpleWebServer()
        server.stop()
        mock_shutdown.assert_called_once()

if __name__ == '__main__':
    unittest.main()
