import sys
import unittest
from unittest.mock import patch, MagicMock
import typer
import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cloudqx import azure_user, azure_organization
import cloudqx

runner = CliRunner()

class TestCloudQXCLI(unittest.TestCase):
    def setUp(self):
        self.orig_argv = sys.argv.copy()
    def tearDown(self):
        sys.argv = self.orig_argv

    @patch('cloudqx.subprocess.run')
    def test_azure_user_success(self, mock_run):
        mock_run.return_value = MagicMock(returncode=0)
        # Should not raise an Exit
        azure_user(debug=False, cloud=None, web=False)

    @patch('cloudqx.subprocess.run')
    def test_azure_user_failure(self, mock_run):
        mock_run.return_value = MagicMock(returncode=2)
        with self.assertRaises(typer.Exit) as cm:
            azure_user(debug=False, cloud=None, web=False)
        # Inner Exit raised with the original return code
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run', side_effect=FileNotFoundError)
    def test_azure_user_file_not_found(self, mock_run):
        with self.assertRaises(typer.Exit) as cm:
            azure_user(debug=False, cloud=None, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_azure_organization_success(self, mock_run):
        mock_run.return_value = MagicMock(returncode=0)
        azure_organization(debug=True, cloud='global', include_resources=False, full_list=False, web=False)

    @patch('cloudqx.subprocess.run')
    def test_azure_organization_subprocess_error(self, mock_run):
        mock_run.side_effect = typer.Exit(3)
        with self.assertRaises(typer.Exit) as cm:
            azure_organization(debug=False, cloud=None, include_resources=False, full_list=False, web=False)
        # Inner exit is caught and re-raised with exit code 1
        self.assertEqual(cm.exception.exit_code, 1)
    
    @patch('cloudqx.subprocess.run')
    def test_azure_vnet_success(self, mock_run):
        mock_run.return_value = MagicMock(returncode=0)
        # Should not raise an Exit
        from cloudqx import azure_vnet
        azure_vnet(debug=False, cloud=None, web=False)

    @patch('cloudqx.subprocess.run')
    def test_azure_vnet_failure(self, mock_run):
        mock_run.return_value = MagicMock(returncode=2)
        from cloudqx import azure_vnet
        with self.assertRaises(typer.Exit) as cm:
            azure_vnet(debug=False, cloud=None, web=False)
        # Non-zero return triggers Exit, re-raised with code 1
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run', side_effect=typer.Exit(4))
    def test_azure_vnet_subprocess_error(self, mock_run):
        from cloudqx import azure_vnet
        with self.assertRaises(typer.Exit) as cm:
            azure_vnet(debug=False, cloud=None, web=False)
        # Subprocess exception is caught and re-raised with code 1
        self.assertEqual(cm.exception.exit_code, 1)
    
    @patch('cloudqx.subprocess.run')
    def test_azure_dns_private_success(self, mock_run):
        mock_run.return_value = MagicMock(returncode=0)
        from cloudqx import azure_dns_private
        # Should not raise an Exit
        azure_dns_private(debug=False, cloud=None, web=False)

    @patch('cloudqx.subprocess.run')
    def test_azure_dns_private_failure(self, mock_run):
        mock_run.return_value = MagicMock(returncode=2)
        from cloudqx import azure_dns_private
        with self.assertRaises(typer.Exit) as cm:
            azure_dns_private(debug=False, cloud=None, web=False)
        # Non-zero return triggers Exit, re-raised with code 1
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.typer.echo')
    def test_aws_info(self, mock_echo):
        from cloudqx import aws_info
        aws_info()
        # First call should indicate coming soon
        mock_echo.assert_any_call("🚧 AWS provider is coming soon!")

    @patch('cloudqx.typer.echo')
    def test_gcp_info(self, mock_echo):
        from cloudqx import gcp_info
        gcp_info()
        mock_echo.assert_any_call("🚧 Google Cloud Platform provider is coming soon!")

    @patch('cloudqx.typer.echo')
    def test_version(self, mock_echo):
        from cloudqx import version, __version__, __author__
        version()
        # Check version and author are echoed
        mock_echo.assert_any_call(f"CloudQX v{__version__} - Multi-Cloud Infrastructure Management Tool")
        mock_echo.assert_any_call(f"Author: {__author__}")

if __name__ == '__main__':
    unittest.main()
# ---------------------------------------
# CLI integration tests for cloudqx.app
@patch('cloudqx.subprocess.run')
def test_cli_azure_user_defaults(mock_run):
    mock_run.return_value = MagicMock(returncode=0)
    result = runner.invoke(cloudqx.app, ['azure', 'user'])
    assert result.exit_code == 0
    cmd = mock_run.call_args[0][0]
    assert '-m' in cmd and 'providers.azure.user' in cmd

@patch('cloudqx.subprocess.run')
def test_cli_azure_user_flags(mock_run):
    mock_run.return_value = MagicMock(returncode=0)
    result = runner.invoke(cloudqx.app, ['azure', 'user', '--debug', '--cloud', 'china', '--web'])
    assert result.exit_code == 0
    cmd = mock_run.call_args[0][0]
    for flag in ['--debug', '--cloud', 'china', '--web']:
        assert flag in cmd

@patch('cloudqx.subprocess.run')
def test_cli_azure_user_invalid_cloud(mock_run):
    result = runner.invoke(cloudqx.app, ['azure', 'user', '--cloud', 'mars'])
    assert result.exit_code == 1
    assert "Invalid cloud environment 'mars'" in result.stdout

@patch('cloudqx.subprocess.run')
def test_cli_azure_org_and_params(mock_run):
    mock_run.return_value = MagicMock(returncode=0)
    result = runner.invoke(cloudqx.app, ['azure', 'organization', '-d', '-c', 'global', '-r', '-f', '-w'])
    assert result.exit_code == 0
    cmd = mock_run.call_args[0][0]
    for flag in ['--debug', '--cloud', 'global', '--include-resources', '--full-list', '--web']:
        assert flag in cmd

@patch('cloudqx.subprocess.run')
def test_cli_azure_vnet_and_params(mock_run):
    mock_run.return_value = MagicMock(returncode=0)
    result = runner.invoke(cloudqx.app, ['azure', 'vnet', '--debug', '--cloud', 'global', '--web'])
    assert result.exit_code == 0
    cmd = mock_run.call_args[0][0]
    for flag in ['--debug', '--cloud', 'global', '--web']:
        assert flag in cmd

@patch('cloudqx.subprocess.run')
def test_cli_azure_dns_private_and_params(mock_run):
    mock_run.return_value = MagicMock(returncode=0)
    result = runner.invoke(cloudqx.app, ['azure', 'dns-private', '--debug', '--cloud', 'china', '--web'])
    assert result.exit_code == 0
    cmd = mock_run.call_args[0][0]
    for flag in ['--debug', '--cloud', 'china', '--web']:
        assert flag in cmd

def test_cli_aws_info():
    result = runner.invoke(cloudqx.app, ['aws', 'info'])
    assert result.exit_code == 0
    assert 'AWS provider is coming soon' in result.stdout

def test_cli_gcp_info():
    result = runner.invoke(cloudqx.app, ['gcp', 'info'])
    assert result.exit_code == 0
    assert 'Google Cloud Platform provider is coming soon' in result.stdout

def test_cli_version():
    result = runner.invoke(cloudqx.app, ['version'])
    assert result.exit_code == 0
    assert f"CloudQX v{cloudqx.__version__}" in result.stdout
    assert f"Author: {cloudqx.__author__}" in result.stdout

@pytest.mark.parametrize('subcmd', [
    ['azure', 'user'],
    ['azure', 'organization'],
    ['azure', 'vnet'],
    ['azure', 'dns-private'],
])
@patch('cloudqx.subprocess.run')
def test_cli_non_zero_exit_codes(mock_run, subcmd):
    mock_run.return_value = MagicMock(returncode=5)
    result = runner.invoke(cloudqx.app, subcmd)
    assert result.exit_code == 1
