import sys
import unittest
from unittest.mock import patch

from providers.azure.organization import parse_organization_arguments

class TestOrganizationArguments(unittest.TestCase):
    @patch.object(sys, 'argv', ['prog'])
    def test_default_arguments(self):
        args = parse_organization_arguments()
        self.assertFalse(args.debug)
        self.assertIsNone(args.cloud)
        self.assertFalse(args.include_resources)
        self.assertFalse(args.full_list)
        self.assertFalse(args.web)

    @patch.object(sys, 'argv', ['prog', '-d', '-c', 'china', '-r', '-f', '-w'])
    def test_all_flags(self):
        args = parse_organization_arguments()
        self.assertTrue(args.debug)
        self.assertEqual(args.cloud, 'china')
        self.assertTrue(args.include_resources)
        self.assertTrue(args.full_list)
        self.assertTrue(args.web)

if __name__ == '__main__':
    unittest.main()