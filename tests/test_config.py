import unittest
from unittest.mock import patch, MagicMock
import sys

from providers.azure.lib.common.config import (setup_logging, parse_arguments, 
                                        get_current_cloud_info, get_cloud_config)

class TestConfig(unittest.TestCase):

    @patch('os.path.exists', return_value=True)
    @patch('os.makedirs')
    @patch('logging.basicConfig')
    def test_setup_logging(self, mock_basic_config, mock_makedirs, mock_exists):
        """Test logging setup."""
        # Act
        setup_logging()
        
        # Assert
        self.assertTrue(mock_basic_config.called)

    @patch('sys.argv', ['test_config.py', '--debug', '--cloud', 'china', '--include-resources', '--full-list'])
    def test_parse_arguments(self):
        """Test argument parsing."""
        # Act
        args = parse_arguments()
        
        # Assert
        self.assertTrue(args.debug)
        self.assertEqual(args.cloud, 'china')
        self.assertTrue(args.include_resources)
        self.assertTrue(args.full_list)

    @patch('providers.azure.lib.common.config.detect_azure_cloud', return_value='global')
    def test_get_current_cloud_info(self, mock_detect_cloud):
        """Test getting current cloud info."""
        # Act
        cloud_name, cloud_config = get_current_cloud_info()
        
        # Assert
        self.assertEqual(cloud_name, 'global')
        self.assertEqual(cloud_config['name'], 'Azure Global')

    def test_get_cloud_config(self):
        """Test getting cloud configuration for a specific cloud."""
        # Act
        cloud_config = get_cloud_config('china')
        
        # Assert
        self.assertEqual(cloud_config['name'], 'Azure China')

    @patch('providers.azure.lib.common.config.detect_azure_cloud', return_value='government')
    def test_get_cloud_config_detected(self, mock_detect_cloud):
        """Test getting cloud configuration via detection."""
        # Act
        cloud_config = get_cloud_config()
        
        # Assert
        self.assertEqual(cloud_config['name'], 'Azure Government')


if __name__ == '__main__':
    unittest.main()
