import pytest
pytest.skip("Skipping debug_resources smoke tests", allow_module_level=True)
import importlib
import sys
import unittest
from unittest.mock import patch, MagicMock

class TestDebugResourcesScript(unittest.TestCase):
    def setUp(self):
        sys.modules.pop('debug_resources', None)

    def test_script_runs_without_error(self):
        sys.modules.pop('debug_resources', None)
        with patch('builtins.print') as mock_print, \
             patch('providers.azure.lib.common.config.get_current_cloud_info', return_value=('global', {'name':'Azure','management_endpoint':'','management_scope':''})), \
             patch('providers.azure.lib.common.auth.get_azure_credential', return_value=MagicMock()), \
             patch('providers.azure.lib.common.azure_resources.get_subscriptions', return_value=[]), \
             patch('providers.azure.lib.common.azure_resources.get_resource_groups', return_value=[]), \
             patch('providers.azure.lib.common.azure_resources.get_resources_in_resource_group', return_value=[]), \
             patch('providers.azure.lib.common.azure_resources.get_all_resources_by_subscription', return_value={}):
            importlib.import_module('debug_resources')
        mock_print.assert_any_call("🔍 Testing resource collection functionality...")

    def test_script_handles_exception(self):
        sys.modules.pop('debug_resources', None)
        with patch('providers.azure.lib.common.config.get_current_cloud_info', return_value=('global', {'name':'Azure','management_endpoint':'','management_scope':''})), \
             patch('providers.azure.lib.common.auth.get_azure_credential', side_effect=Exception('fail auth')), \
             patch('builtins.print') as mock_print:
            importlib.import_module('debug_resources')
        calls = [str(c) for c in mock_print.call_args_list]
        self.assertTrue(any('Error:' in ''.join(c) or '❌ Error' in ''.join(c) for c in calls))