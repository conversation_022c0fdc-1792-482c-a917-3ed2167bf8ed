#!/usr/bin/env python3
"""
Integration test to verify the complete refactoring works correctly.
"""
import sys
import os
import json
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from providers.azure.lib.dns_private.data import build_resolver_hierarchy

def test_integration():
    """Test the complete refactoring integration."""
    print("🔧 Testing DNS Private Hierarchy Refactoring Integration")
    
    # Mock subscription data structure as it would come from the main module
    mock_subscription_data = {
        'subscription_id': 'sub-123',
        'subscription_name': 'Test Subscription',
        'resolvers': [
            {
                'name': 'my-dns-resolver',
                'resource_guid': 'resolver-guid-123',
                'subscription': 'Test Subscription',
                'resource_group': 'rg-dns',
                'location': 'eastus',
                'provisioning_state': 'Succeeded',
                'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsResolvers/my-dns-resolver'
            }
        ],
        'forwarding_rules': [
            {
                'name': 'ruleset-my-dns-resolver',
                'subscription': 'Test Subscription',
                'resource_group': 'rg-dns',
                'location': 'eastus',
                'provisioning_state': 'Succeeded',
                'outbound_endpoints': [
                    {
                        'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsResolvers/my-dns-resolver/outboundEndpoints/endpoint1'
                    }
                ],
                'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsForwardingRulesets/ruleset-my-dns-resolver'
            }
        ],
        'forwarding_rules_detail': [
            {
                'name': 'example-rule',
                'ruleset_name': 'ruleset-my-dns-resolver',
                'domain_name': 'example.com',
                'forwarding_rule_state': 'Enabled',
                'provisioning_state': 'Succeeded',
                'target_dns_servers': [
                    {
                        'ip_address': '*******',
                        'port': 53
                    }
                ]
            },
            {
                'name': 'contoso-rule',
                'ruleset_name': 'ruleset-my-dns-resolver',
                'domain_name': 'contoso.com',
                'forwarding_rule_state': 'Enabled',
                'provisioning_state': 'Succeeded',
                'target_dns_servers': [
                    {
                        'ip_address': '*******',
                        'port': 53
                    }
                ]
            }
        ]
    }
    
    # Build hierarchy as would be done in the main module
    hierarchy = build_resolver_hierarchy(
        mock_subscription_data['resolvers'],
        mock_subscription_data['forwarding_rules'],
        mock_subscription_data['forwarding_rules_detail']
    )
    
    # Add hierarchy to subscription data
    mock_subscription_data['resolver_hierarchy'] = hierarchy
    
    # Test the hierarchy structure
    print(f"✅ Built hierarchy with {len(hierarchy)} entries")
    
    # Verify structure
    assert len(hierarchy) == 1, f"Expected 1 hierarchy entry, got {len(hierarchy)}"
    
    resolver_entry = hierarchy[0]
    assert resolver_entry['resolver'] is not None, "Resolver should not be None"
    assert resolver_entry['resolver']['name'] == 'my-dns-resolver', "Resolver name mismatch"
    assert len(resolver_entry['rulesets']) == 1, f"Expected 1 ruleset, got {len(resolver_entry['rulesets'])}"
    
    ruleset_entry = resolver_entry['rulesets'][0]
    assert ruleset_entry['ruleset']['name'] == 'ruleset-my-dns-resolver', "Ruleset name mismatch"
    assert len(ruleset_entry['rules']) == 2, f"Expected 2 rules, got {len(ruleset_entry['rules'])}"
    
    # Verify rule details
    rule_names = [rule['name'] for rule in ruleset_entry['rules']]
    assert 'example-rule' in rule_names, "example-rule not found in rules"
    assert 'contoso-rule' in rule_names, "contoso-rule not found in rules"
    
    print("✅ Hierarchy structure is correct")
    
    # Test JSON serialization (as would be done in HTML report generation)
    try:
        json_data = json.dumps(hierarchy, indent=2)
        print(f"✅ JSON serialization successful ({len(json_data)} characters)")
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        return False
    
    # Test deserialization (as would be done in JavaScript)
    try:
        deserialized = json.loads(json_data)
        assert len(deserialized) == 1, "Deserialized data length mismatch"
        assert deserialized[0]['resolver']['name'] == 'my-dns-resolver', "Deserialized resolver name mismatch"
        print("✅ JSON deserialization successful")
    except Exception as e:
        print(f"❌ JSON deserialization failed: {e}")
        return False
    
    print("\n📊 Integration Test Summary:")
    print(f"   • Resolvers: {len(mock_subscription_data['resolvers'])}")
    print(f"   • Rulesets: {len(mock_subscription_data['forwarding_rules'])}")
    print(f"   • Rules: {len(mock_subscription_data['forwarding_rules_detail'])}")
    print(f"   • Hierarchy Entries: {len(hierarchy)}")
    print(f"   • JSON Size: {len(json_data):,} characters")
    
    print("\n🎯 Refactoring Benefits:")
    print("   • ✅ Relationship logic moved from JavaScript to Python")
    print("   • ✅ Reduced client-side complexity")
    print("   • ✅ Improved maintainability")
    print("   • ✅ Better error handling and logging")
    print("   • ✅ Consistent data structure")
    
    print("\n🎉 Integration test completed successfully!")
    return True

if __name__ == '__main__':
    success = test_integration()
    sys.exit(0 if success else 1)

# Contains AI-generated edits.
