import unittest
from unittest.mock import patch, MagicMock
import sys
import os

class TestDnsPrivateMain(unittest.TestCase):

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.lib.dns_private.html_report.find_available_port')
    @patch('providers.azure.lib.dns_private.html_report.start_web_server')
    @patch('providers.azure.lib.dns_private.html_report.webbrowser.open')
    @patch('sys.exit')
    def test_main_success(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                          mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                          mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                          mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                          mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                          mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Mock web server functions
        mock_find_available_port.return_value = 8000
        mock_start_web_server.return_value = MagicMock()

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_parse_args.assert_called_once()
        mock_setup_logging.assert_called_once_with(False)
        mock_get_current_cloud_info.assert_called_once()
        mock_get_azure_credential.assert_called_once()
        mock_check_authentication.assert_called_once()
        mock_get_subscriptions.assert_called_once()
        mock_list_private_dns_zones.assert_called_once()
        mock_list_virtual_network_links.assert_called_once()
        mock_list_private_endpoints.assert_called_once()
        mock_list_private_resolvers.assert_called_once()
        mock_list_dns_forwarding_rulesets.assert_called_once()
        mock_list_forwarding_rules.assert_called_once()
        mock_build_resolver_hierarchy.assert_called_once()
        mock_generate_html_report.assert_called_once()
        mock_sys_exit.assert_not_called()
        mock_webbrowser_open.assert_not_called() # Because args.web is False
        mock_start_web_server.assert_not_called() # Because args.web is False

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('sys.exit')
    def test_main_auth_failure(self, mock_sys_exit, mock_get_subscriptions, mock_check_authentication,
                               mock_get_azure_credential, mock_get_current_cloud_info, mock_setup_logging,
                               mock_parse_args):
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})
        mock_check_authentication.return_value = False # Simulate auth failure
        mock_get_azure_credential.return_value = MagicMock()

        from providers.azure.dns_private import main
        main()

        mock_sys_exit.assert_called_once_with(1)

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('sys.exit')
    def test_main_no_subscriptions(self, mock_sys_exit, mock_get_subscriptions, mock_check_authentication,
                                   mock_get_azure_credential, mock_get_current_cloud_info, mock_setup_logging,
                                   mock_parse_args):
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()
        mock_get_subscriptions.return_value = [] # Simulate no subscriptions

        from providers.azure.dns_private import main
        main()

        mock_sys_exit.assert_called_once_with(0)

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.lib.dns_private.html_report.find_available_port')
    @patch('providers.azure.lib.dns_private.html_report.start_web_server')
    @patch('providers.azure.lib.dns_private.html_report.webbrowser.open')
    @patch('sys.exit')
    def test_main_web_server_success(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                     mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                     mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                     mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                     mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                     mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = True # Enable web server
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Mock web server functions
        mock_find_available_port.return_value = 8000
        mock_start_web_server.return_value = MagicMock()

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_parse_args.assert_called_once()
        mock_setup_logging.assert_called_once_with(False)
        mock_get_current_cloud_info.assert_called_once()
        mock_get_azure_credential.assert_called_once()
        mock_check_authentication.assert_called_once()
        mock_get_subscriptions.assert_called_once()
        mock_list_private_dns_zones.assert_called_once()
        mock_list_virtual_network_links.assert_called_once()
        mock_list_private_endpoints.assert_called_once()
        mock_list_private_resolvers.assert_called_once()
        mock_list_dns_forwarding_rulesets.assert_called_once()
        mock_list_forwarding_rules.assert_called_once()
        mock_build_resolver_hierarchy.assert_called_once()
        mock_generate_html_report.assert_called_once()
        mock_find_available_port.assert_called_once()
        mock_start_web_server.assert_called_once()
        mock_webbrowser_open.assert_called_once()
        mock_sys_exit.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.lib.dns_private.html_report.find_available_port')
    @patch('providers.azure.lib.dns_private.html_report.start_web_server')
    @patch('providers.azure.lib.dns_private.html_report.webbrowser.open')
    @patch('sys.exit')
    def test_main_web_server_failure(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                     mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                     mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                     mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                     mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                     mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = True # Enable web server
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Simulate web server failure
        mock_thread = MagicMock()
        mock_thread.join.side_effect = Exception("Web server failed")
        mock_start_web_server.return_value = mock_thread

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_not_called() # Should not exit on web server failure, but log warning
        mock_generate_html_report.assert_called_once()
        mock_webbrowser_open.assert_not_called()
        mock_start_web_server.assert_called_once()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_resource_listing_error(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                        mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                        mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                        mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                        mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                        mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Simulate resource listing error
        mock_list_private_dns_zones.side_effect = Exception("Resource listing failed")

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_not_called() # Should not exit on resource listing failure

        mock_webbrowser_open.assert_not_called()
        mock_start_web_server.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_resolver_hierarchy_error(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                          mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                          mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                          mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                          mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                          mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []

        # Simulate resolver hierarchy error
        mock_build_resolver_hierarchy.side_effect = Exception("Resolver hierarchy failed")

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_not_called() # Should not exit on resolver hierarchy failure

        mock_webbrowser_open.assert_not_called()
        mock_start_web_server.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_html_report_generation_error(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                              mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                              mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                              mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                              mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                              mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Simulate HTML report generation error
        mock_generate_html_report.side_effect = Exception("HTML report generation failed")

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_called_once_with(1) # Should exit on HTML report generation failure
        mock_webbrowser_open.assert_not_called()
        mock_start_web_server.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_debug_mode_logging(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                    mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                    mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                    mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                    mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                    mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = True # Enable debug mode
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_setup_logging.assert_called_once_with(True) # Should be called with True for debug
        mock_sys_exit.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_cloud_selection(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                 mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                 mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                 mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                 mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                 mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = "china" # Specify china cloud
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("china", {"name": "AzureChinaCloud"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_get_current_cloud_info.assert_called_once() # Should be called once to get cloud info
        mock_get_azure_credential.assert_called_once_with("china") # Should be called with "china"
        mock_check_authentication.assert_called_once()
        mock_sys_exit.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_web_server_keyboard_interrupt(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                                mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                                mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                                mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                                mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                                mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = True # Enable web server
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Simulate KeyboardInterrupt during server_thread.join()
        mock_thread = MagicMock()
        mock_thread.join.side_effect = KeyboardInterrupt
        mock_start_web_server.return_value = mock_thread

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_not_called() # Should not exit on KeyboardInterrupt
        mock_webbrowser_open.assert_called_once()
        mock_start_web_server.assert_called_once()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_exception_handling(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                     mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                     mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                     mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                     mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                     mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Simulate a general exception
        mock_get_current_cloud_info.side_effect = Exception("General error")

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_sys_exit.assert_called_once_with(1) # Should exit on any unhandled exception
        mock_setup_logging.assert_called_once_with(False) # Should still setup logging
        mock_webbrowser_open.assert_not_called()
        mock_start_web_server.assert_not_called()
        mock_generate_html_report.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_debug_mode_logging(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                    mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                    mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                    mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                    mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                    mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = True # Enable debug mode
        mock_args.cloud = None
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("global", {"name": "AzureGlobal"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_setup_logging.assert_called_once_with(True) # Should be called with True for debug
        mock_sys_exit.assert_not_called()

    @patch('providers.azure.dns_private.parse_args')
    @patch('providers.azure.dns_private.setup_logging')
    @patch('providers.azure.dns_private.get_current_cloud_info')
    @patch('providers.azure.dns_private.get_azure_credential')
    @patch('providers.azure.dns_private.check_authentication')
    @patch('providers.azure.dns_private.get_subscriptions')
    @patch('providers.azure.dns_private.list_private_dns_zones')
    @patch('providers.azure.dns_private.list_virtual_network_links')
    @patch('providers.azure.dns_private.list_private_endpoints')
    @patch('providers.azure.dns_private.list_private_resolvers')
    @patch('providers.azure.dns_private.list_dns_forwarding_rulesets')
    @patch('providers.azure.dns_private.list_forwarding_rules')
    @patch('providers.azure.dns_private.build_resolver_hierarchy')
    @patch('providers.azure.dns_private.generate_dns_private_html_report')
    @patch('providers.azure.dns_private.find_available_port')
    @patch('providers.azure.dns_private.start_web_server')
    @patch('providers.azure.dns_private.webbrowser.open')
    @patch('sys.exit')
    def test_main_cloud_selection(self, mock_sys_exit, mock_webbrowser_open, mock_start_web_server,
                                 mock_find_available_port, mock_generate_html_report, mock_build_resolver_hierarchy,
                                 mock_list_forwarding_rules, mock_list_dns_forwarding_rulesets, mock_list_private_resolvers,
                                 mock_list_private_endpoints, mock_list_virtual_network_links, mock_list_private_dns_zones,
                                 mock_get_subscriptions, mock_check_authentication, mock_get_azure_credential,
                                 mock_get_current_cloud_info, mock_setup_logging, mock_parse_args):
        
        # Mock parse_args
        mock_args = MagicMock()
        mock_args.debug = False
        mock_args.cloud = "china" # Specify china cloud
        mock_args.web = False
        mock_parse_args.return_value = mock_args

        # Mock get_current_cloud_info
        mock_get_current_cloud_info.return_value = ("china", {"name": "AzureChinaCloud"})

        # Mock authentication
        mock_check_authentication.return_value = True
        mock_get_azure_credential.return_value = MagicMock()

        # Mock subscriptions
        mock_get_subscriptions.return_value = [{"subscriptionId": "sub1", "displayName": "Sub1"}]

        # Mock resource listing functions
        mock_list_private_dns_zones.return_value = []
        mock_list_virtual_network_links.return_value = []
        mock_list_private_endpoints.return_value = []
        mock_list_private_resolvers.return_value = []
        mock_list_dns_forwarding_rulesets.return_value = []
        mock_list_forwarding_rules.return_value = []
        mock_build_resolver_hierarchy.return_value = {}

        # Mock HTML report generation
        mock_generate_html_report.return_value = "html/report.html"

        # Call main
        from providers.azure.dns_private import main
        main()

        # Assertions
        mock_get_current_cloud_info.assert_called_once() # Should be called once to get cloud info
        mock_get_azure_credential.assert_called_once_with("china") # Should be called with "china"
        mock_check_authentication.assert_called_once()
        mock_sys_exit.assert_not_called()