import unittest
from unittest.mock import patch, MagicMock

from providers.azure.lib.common import azure_resources

class TestAzureResourcesAdditional(unittest.TestCase):
    def setUp(self):
        # Common dummy cloud info and token
        self.cloud_info = ('global', {
            'name': 'Azure Global',
            'management_endpoint': 'https://management.azure.com',
            'management_scope': '.default'
        })
        self.mock_token = MagicMock(token='fake-token')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_management_groups_success(self, mock_client, mock_get_token, mock_cloud):
        mock_cloud.return_value = self.cloud_info
        mock_get_token.return_value = self.mock_token
        # prepare response
        resp = MagicMock()
        resp.status_code = 200
        resp.json.return_value = {'value': [{'id': 'mg1'}, {'id': 'mg2'}]}
        # client.get returns resp
        mock_client.return_value.__enter__.return_value.get.return_value = resp
        # call
        mgs = azure_resources.get_management_groups(MagicMock())
        self.assertEqual(len(mgs), 2)
        self.assertEqual(mgs[0]['id'], 'mg1')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_management_groups_http_error(self, mock_client, mock_get_token, mock_cloud):
        mock_cloud.return_value = self.cloud_info
        mock_get_token.return_value = self.mock_token
        resp = MagicMock(status_code=500)
        mock_client.return_value.__enter__.return_value.get.return_value = resp
        mgs = azure_resources.get_management_groups(MagicMock())
        self.assertEqual(mgs, [])

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_resources_in_resource_group_success(self, mock_client, mock_get_token, mock_cloud):
        mock_cloud.return_value = self.cloud_info
        mock_get_token.return_value = self.mock_token
        resp = MagicMock()
        resp.status_code = 200
        resp.json.return_value = {'value': [{'name': 'rg1-res'}]}
        mock_client.return_value.__enter__.return_value.get.return_value = resp
        resources = azure_resources.get_resources_in_resource_group(MagicMock(), 'sub1', 'rg1')
        self.assertEqual(len(resources), 1)
        self.assertEqual(resources[0]['name'], 'rg1-res')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_resources_in_resource_group_error(self, mock_client, mock_get_token, mock_cloud):
        mock_cloud.return_value = self.cloud_info
        mock_get_token.return_value = self.mock_token
        # simulate exception
        mock_client.return_value.__enter__.side_effect = Exception('fail')
        resources = azure_resources.get_resources_in_resource_group(MagicMock(), 'sub1', 'rg1')
        self.assertEqual(resources, [])

    @patch('providers.azure.lib.common.azure_resources.get_resource_groups')
    @patch('providers.azure.lib.common.azure_resources.get_resources_in_resource_group')
    def test_get_all_resources_by_subscription(self, mock_rg_resources, mock_get_rgs):
        # two resource groups
        mock_get_rgs.return_value = [{'name': 'rgA'}, {'name': 'rgB'}]
        # rgA has one item, rgB empty
        mock_rg_resources.side_effect = [[{'name': 'res1'}], []]
        result = azure_resources.get_all_resources_by_subscription(MagicMock(), 'sub1')
        self.assertIn('rgA', result)
        self.assertIn('rgB', result)
        self.assertEqual(len(result['rgA']), 1)
        self.assertEqual(result['rgB'], [])

    @patch('providers.azure.lib.common.azure_resources.get_resource_groups')
    def test_get_all_resources_empty(self, mock_get_rgs):
        mock_get_rgs.return_value = []
        result = azure_resources.get_all_resources_by_subscription(MagicMock(), 'sub1')
        self.assertEqual(result, {})

if __name__ == '__main__':
    unittest.main()