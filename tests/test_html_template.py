import unittest
import unittest
from providers.azure.lib.user.html_template import HTML_TEMPLATE

class TestHtmlTemplate(unittest.TestCase):

    def test_html_template_exists(self):
        """Test that the HTML_TEMPLATE variable exists and is a string."""
        self.assertIsInstance(HTML_TEMPLATE, str)

    def test_html_template_content(self):
        """Test for key placeholders in the HTML template."""
        self.assertIn('{generation_date}', HTML_TEMPLATE)
        self.assertIn('{total_users}', HTML_TEMPLATE)
        self.assertIn('{user_rows}', HTML_TEMPLATE)


if __name__ == '__main__':
    unittest.main()
