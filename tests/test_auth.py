
import unittest
from unittest.mock import patch, MagicMock

from providers.azure.lib.common.auth import get_azure_credential, check_authentication

class TestAuth(unittest.TestCase):

    @patch('providers.azure.lib.common.auth._global_credential', None)
    @patch('providers.azure.lib.common.auth._current_cloud_env', None)
    @patch('providers.azure.lib.common.auth.DefaultAzureCredential')
    def test_get_azure_credential_success(self, mock_default_credential):
        """Test successful Azure credential retrieval."""
        # Arrange
        mock_instance = MagicMock()
        mock_default_credential.return_value = mock_instance
        
        # Act
        credential = get_azure_credential()
        
        # Assert
        self.assertIsNotNone(credential)
        mock_default_credential.assert_called_once()
        self.assertEqual(credential, mock_instance)

    @patch('providers.azure.lib.common.auth._global_credential', None)
    @patch('providers.azure.lib.common.auth._current_cloud_env', None)
    @patch('providers.azure.lib.common.auth.DefaultAzureCredential')
    def test_get_azure_credential_failure(self, mock_default_credential):
        """Test Azure credential retrieval failure."""
        # Arrange
        mock_default_credential.side_effect = Exception("Authentication failed")
        
        # Act & Assert
        with self.assertRaises(Exception) as context:
            get_azure_credential()
        self.assertIn("Authentication failed", str(context.exception))

    @patch('providers.azure.lib.common.auth.get_azure_credential')
    @patch('providers.azure.lib.common.auth.SubscriptionClient')
    @patch('providers.azure.lib.common.token_manager.get_token_manager') # Patch token_manager to prevent actual token calls
    def test_test_authentication_success(self, mock_get_token_manager, mock_subscription_client, mock_get_credential):
        """Test successful authentication test."""
        # Arrange
        mock_credential = MagicMock()
        mock_get_credential.return_value = mock_credential
        
        mock_sub_client_instance = mock_subscription_client.return_value
        mock_sub_client_instance.subscriptions.list.return_value = [MagicMock()] # Ensure list returns an iterable
        
        # Act
        result = check_authentication()
        
        # Assert
        self.assertTrue(result)
        mock_get_credential.assert_called_once()
        mock_subscription_client.assert_called_once_with(mock_credential)
        mock_sub_client_instance.subscriptions.list.assert_called_once()
        mock_get_token_manager.assert_not_called() # Ensure token manager is not called if SubscriptionClient succeeds

    @patch('providers.azure.lib.common.auth.get_azure_credential')
    @patch('providers.azure.lib.common.auth.SubscriptionClient')
    def test_test_authentication_failure(self, mock_subscription_client, mock_get_credential):
        """Test authentication test failure."""
        # Arrange
        mock_credential = MagicMock()
        mock_get_credential.return_value = mock_credential
        
        mock_subscription_client.side_effect = Exception("Invalid credentials")
        
        # Act
        result = check_authentication()
        
        # Assert
        self.assertFalse(result)
        mock_get_credential.assert_called_once()
        mock_subscription_client.assert_called_once_with(mock_credential)

if __name__ == '__main__':
    unittest.main()
