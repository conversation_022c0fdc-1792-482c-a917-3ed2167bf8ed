
import unittest
from unittest.mock import patch, MagicMock, ANY

from providers.azure.lib.common.azure_resources import (
    get_subscriptions, get_resource_groups, get_management_groups,
    get_resources_in_resource_group, get_all_resources_by_subscription
)

class TestAzureResources(unittest.TestCase):

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_subscriptions_success(self, mock_client, mock_get_token, mock_get_cloud_info):
        """Test successful retrieval of subscriptions."""
        # Arrange
        mock_get_cloud_info.return_value = ('global', {'name': 'Azure Global', 'management_endpoint': 'https://management.azure.com', 'management_scope': '.default'})
        mock_get_token.return_value = MagicMock(token='fake-token')
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'value': [{'id': 'sub1'}]}
        mock_client.return_value.__enter__.return_value.get.return_value = mock_response

        # Act
        subscriptions = get_subscriptions(MagicMock())

        # Assert
        self.assertEqual(len(subscriptions), 1)
        self.assertEqual(subscriptions[0]['id'], 'sub1')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_resource_groups_success(self, mock_client, mock_get_token, mock_get_cloud_info):
        """Test successful retrieval of resource groups."""
        # Arrange
        mock_get_cloud_info.return_value = ('global', {'name': 'Azure Global', 'management_endpoint': 'https://management.azure.com', 'management_scope': '.default'})
        mock_get_token.return_value = MagicMock(token='fake-token')

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'value': [{'name': 'rg1'}]}
        mock_client.return_value.__enter__.return_value.get.return_value = mock_response

        # Act
        rgs = get_resource_groups(MagicMock(), 'sub1')

        # Assert
        self.assertEqual(len(rgs), 1)
        self.assertEqual(rgs[0]['name'], 'rg1')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_management_groups_success(self, mock_client, mock_get_token, mock_get_cloud_info):
        """Test successful retrieval of management groups."""
        # Arrange
        mock_get_cloud_info.return_value = ('global', {'name': 'Azure Global', 'management_endpoint': 'https://management.azure.com', 'management_scope': '.default'})
        mock_get_token.return_value = MagicMock(token='fake-token')

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'value': [{'name': 'mg1'}]}
        mock_client.return_value.__enter__.return_value.get.return_value = mock_response

        # Act
        mgs = get_management_groups(MagicMock())

        # Assert
        self.assertEqual(len(mgs), 1)
        self.assertEqual(mgs[0]['name'], 'mg1')

    @patch('providers.azure.lib.common.azure_resources.get_current_cloud_info')
    @patch('providers.azure.lib.common.azure_resources.get_cached_token')
    @patch('httpx.Client')
    def test_get_resources_in_resource_group_success(self, mock_client, mock_get_token, mock_get_cloud_info):
        """Test successful retrieval of resources in a resource group."""
        # Arrange
        mock_get_cloud_info.return_value = ('global', {'name': 'Azure Global', 'management_endpoint': 'https://management.azure.com', 'management_scope': '.default'})
        mock_get_token.return_value = MagicMock(token='fake-token')

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'value': [{'name': 'resource1'}]}
        mock_client.return_value.__enter__.return_value.get.return_value = mock_response

        # Act
        resources = get_resources_in_resource_group(MagicMock(), 'sub1', 'rg1')

        # Assert
        self.assertEqual(len(resources), 1)
        self.assertEqual(resources[0]['name'], 'resource1')

    @patch('providers.azure.lib.common.azure_resources.get_resource_groups')
    @patch('providers.azure.lib.common.azure_resources.get_resources_in_resource_group')
    def test_get_all_resources_by_subscription(self, mock_get_resources, mock_get_rgs):
        """Test aggregation of all resources by subscription."""
        # Arrange
        mock_get_rgs.return_value = [{'name': 'rg1'}, {'name': 'rg2'}]
        mock_get_resources.side_effect = [
            [{'name': 'resource1'}],
            [{'name': 'resource2'}, {'name': 'resource3'}]
        ]

        # Act
        resources_by_rg = get_all_resources_by_subscription(MagicMock(), 'sub1')

        # Assert
        self.assertEqual(len(resources_by_rg), 2)
        self.assertEqual(len(resources_by_rg['rg1']), 1)
        self.assertEqual(len(resources_by_rg['rg2']), 2)
        self.assertEqual(mock_get_resources.call_count, 2)

if __name__ == '__main__':
    unittest.main()
