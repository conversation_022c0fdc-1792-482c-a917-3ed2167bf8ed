import unittest
from unittest.mock import patch, MagicMock
import time
from providers.azure.lib.common.token_manager import TokenManager

class TestTokenManager(unittest.TestCase):

    def setUp(self):
        """Set up for the tests."""
        # Initialize without credential for testing cache behavior
        self.token_manager = TokenManager(credential=None)

    def test_get_token_not_cached(self):
        """Test getting a token that is not in the cache."""
        self.assertIsNone(self.token_manager.get_token('scope1'))

    def test_set_and_get_token(self):
        """Test setting and then getting a token."""
        mock_token = MagicMock()
        mock_token.expires_on = time.time() + 3600  # Expires in 1 hour
        mock_token.token = "test_token_string"
        
        self.token_manager.set_token('scope1', mock_token)
        retrieved_token = self.token_manager.get_token('scope1')
        
        self.assertEqual(retrieved_token, mock_token)

    def test_get_token_expired(self):
        """Test that an expired token is not returned."""
        mock_token = MagicMock()
        mock_token.expires_on = time.time() - 1  # Expired 1 second ago
        mock_token.token = "expired_token_string"
        
        self.token_manager.set_token('scope2', mock_token)
        
        self.assertIsNone(self.token_manager.get_token('scope2'))

    def test_clear_cache(self):
        """Test clearing the token cache."""
        mock_token = MagicMock()
        mock_token.expires_on = time.time() + 3600
        mock_token.token = "test_token_string"
        
        self.token_manager.set_token('scope1', mock_token)
        self.token_manager.clear_cache()
        
        self.assertIsNone(self.token_manager.get_token('scope1'))

    def test_multiple_scopes(self):
        """Test caching tokens for multiple scopes."""
        mock_token1 = MagicMock()
        mock_token1.expires_on = time.time() + 3600
        mock_token1.token = "token_scope1"

        mock_token2 = MagicMock()
        mock_token2.expires_on = time.time() + 3600
        mock_token2.token = "token_scope2"

        self.token_manager.set_token('scope1', mock_token1)
        self.token_manager.set_token('scope2', mock_token2)

        self.assertEqual(self.token_manager.get_token('scope1'), mock_token1)
        self.assertEqual(self.token_manager.get_token('scope2'), mock_token2)

if __name__ == '__main__':
    unittest.main()
