#!/usr/bin/env python3
"""
Comprehensive tests for token manager functionality to cover missing test cases.
"""

import unittest
import time
import threading
from unittest.mock import patch, MagicMock
from azure.core.credentials import AccessToken
from azure.core.exceptions import ClientAuthenticationError

from providers.azure.lib.common.token_manager import (
    TokenManager, 
    get_token_manager, 
    get_cached_token,
    clear_all_token_caches,
    get_global_cache_stats,
    log_authentication_efficiency,
    get_optimization_recommendations
)


class TestTokenManagerComprehensive(unittest.TestCase):
    """Comprehensive tests for token manager functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Clear any existing token managers
        from providers.azure.lib.common import token_manager
        token_manager._token_managers.clear()
        
        self.mock_credential = MagicMock()
        self.mock_token = MagicMock()
        self.mock_token.token = "test_token_123"
        self.mock_token.expires_on = time.time() + 3600  # 1 hour from now

    def test_token_manager_with_credential_fetch_success(self):
        """Test token manager fetching new token successfully."""
        self.mock_credential.get_token.return_value = self.mock_token
        
        token_manager = TokenManager(self.mock_credential)
        result = token_manager.get_token('https://graph.microsoft.com/.default')
        
        self.assertEqual(result, self.mock_token)
        self.mock_credential.get_token.assert_called_once_with('https://graph.microsoft.com/.default')

    def test_token_manager_with_credential_fetch_failure(self):
        """Test token manager handling credential fetch failure."""
        self.mock_credential.get_token.side_effect = ClientAuthenticationError("Auth failed")
        
        token_manager = TokenManager(self.mock_credential)
        result = token_manager.get_token('https://graph.microsoft.com/.default')
        
        self.assertIsNone(result)

    def test_token_manager_with_credential_unexpected_error(self):
        """Test token manager handling unexpected errors."""
        self.mock_credential.get_token.side_effect = Exception("Unexpected error")
        
        token_manager = TokenManager(self.mock_credential)
        result = token_manager.get_token('https://graph.microsoft.com/.default')
        
        self.assertIsNone(result)

    def test_token_manager_refresh_threshold(self):
        """Test token refresh based on expiration threshold."""
        # Token expires in 200 seconds
        self.mock_token.expires_on = time.time() + 200
        self.mock_credential.get_token.return_value = self.mock_token
        
        token_manager = TokenManager(self.mock_credential)
        
        # First call should fetch and cache
        result1 = token_manager.get_token('https://graph.microsoft.com/.default', refresh_threshold_seconds=300)
        self.assertEqual(result1, self.mock_token)
        self.assertEqual(self.mock_credential.get_token.call_count, 1)
        
        # Second call should refresh because token expires within threshold (300s > 200s)
        new_mock_token = MagicMock()
        new_mock_token.token = "new_test_token_456"
        new_mock_token.expires_on = time.time() + 3600
        self.mock_credential.get_token.return_value = new_mock_token
        
        result2 = token_manager.get_token('https://graph.microsoft.com/.default', refresh_threshold_seconds=300)
        self.assertEqual(result2, new_mock_token)
        self.assertEqual(self.mock_credential.get_token.call_count, 2)

    def test_token_manager_within_refresh_threshold(self):
        """Test token reuse when within refresh threshold."""
        # Token expires in 400 seconds
        self.mock_token.expires_on = time.time() + 400
        
        token_manager = TokenManager(self.mock_credential)
        token_manager.set_token('https://graph.microsoft.com/.default', self.mock_token)
        
        # Should reuse cached token (400s > 300s threshold)
        result = token_manager.get_token('https://graph.microsoft.com/.default', refresh_threshold_seconds=300)
        self.assertEqual(result, self.mock_token)
        self.mock_credential.get_token.assert_not_called()

    def test_token_manager_cache_stats_valid_tokens(self):
        """Test cache statistics with valid tokens."""
        token_manager = TokenManager()
        
        # Add some valid tokens
        valid_token1 = MagicMock(spec=AccessToken)
        valid_token1.expires_on = time.time() + 1800  # 30 minutes
        valid_token2 = MagicMock(spec=AccessToken)
        valid_token2.expires_on = time.time() + 3600  # 1 hour
        
        token_manager.set_token('scope1', valid_token1)
        token_manager.set_token('scope2', valid_token2)
        
        stats = token_manager.get_cache_stats()
        
        self.assertEqual(stats['total_cached'], 2)
        self.assertEqual(stats['valid_tokens'], 2)
        self.assertEqual(stats['expired_tokens'], 0)
        self.assertEqual(stats['cache_hit_ratio'], '100.0%')
        self.assertIn('scope_details', stats)

    def test_token_manager_cache_stats_expired_tokens(self):
        """Test cache statistics with expired tokens."""
        token_manager = TokenManager()
        
        # Add expired tokens
        expired_token1 = MagicMock(spec=AccessToken)
        expired_token1.expires_on = time.time() - 1800  # 30 minutes ago
        expired_token2 = MagicMock(spec=AccessToken)
        expired_token2.expires_on = time.time() - 3600  # 1 hour ago
        
        token_manager.set_token('scope1', expired_token1)
        token_manager.set_token('scope2', expired_token2)
        
        stats = token_manager.get_cache_stats()
        
        self.assertEqual(stats['total_cached'], 2)
        self.assertEqual(stats['valid_tokens'], 0)
        self.assertEqual(stats['expired_tokens'], 2)
        self.assertEqual(stats['cache_hit_ratio'], '0.0%')

    def test_token_manager_cache_stats_mixed_tokens(self):
        """Test cache statistics with mix of valid and expired tokens."""
        token_manager = TokenManager()
        
        # Add valid token
        valid_token = MagicMock(spec=AccessToken)
        valid_token.expires_on = time.time() + 1800
        
        # Add expired token
        expired_token = MagicMock(spec=AccessToken)
        expired_token.expires_on = time.time() - 1800
        
        token_manager.set_token('valid_scope', valid_token)
        token_manager.set_token('expired_scope', expired_token)
        
        stats = token_manager.get_cache_stats()
        
        self.assertEqual(stats['total_cached'], 2)
        self.assertEqual(stats['valid_tokens'], 1)
        self.assertEqual(stats['expired_tokens'], 1)
        self.assertEqual(stats['cache_hit_ratio'], '50.0%')

    def test_token_manager_cache_stats_no_expiration_info(self):
        """Test cache statistics with tokens without expiration info."""
        token_manager = TokenManager()
        
        # Add token without expiration info
        token_no_expiry = MagicMock(spec=AccessToken)
        token_no_expiry.expires_on = None
        
        token_manager.set_token('no_expiry_scope', token_no_expiry)
        
        stats = token_manager.get_cache_stats()
        
        self.assertEqual(stats['total_cached'], 1)
        self.assertEqual(stats['valid_tokens'], 0)
        self.assertEqual(stats['expired_tokens'], 1)
        self.assertIn('no_expiry_scope', stats['scope_details'])
        self.assertEqual(stats['scope_details']['no_expiry_scope']['expired_since'], 'unknown')

    def test_get_token_manager_singleton_behavior(self):
        """Test that get_token_manager returns the same instance for same credential."""
        credential1 = MagicMock()
        credential2 = MagicMock()
        
        manager1a = get_token_manager(credential1)
        manager1b = get_token_manager(credential1)
        manager2 = get_token_manager(credential2)
        
        # Same credential should return same manager
        self.assertIs(manager1a, manager1b)
        
        # Different credentials should return different managers
        self.assertIsNot(manager1a, manager2)

    def test_get_cached_token_convenience_function(self):
        """Test the convenience function for getting cached tokens."""
        credential = MagicMock()
        credential.get_token.return_value = self.mock_token
        
        result = get_cached_token(credential, 'https://management.azure.com/.default')
        
        self.assertEqual(result, self.mock_token)
        credential.get_token.assert_called_once_with('https://management.azure.com/.default')

    def test_clear_all_token_caches(self):
        """Test clearing all token caches across managers."""
        credential1 = MagicMock()
        credential2 = MagicMock()
        
        manager1 = get_token_manager(credential1)
        manager2 = get_token_manager(credential2)
        
        # Add tokens to both managers
        manager1.set_token('scope1', self.mock_token)
        manager2.set_token('scope2', self.mock_token)
        
        # Clear all caches
        clear_all_token_caches()
        
        # Verify both caches are cleared
        self.assertIsNone(manager1.get_token('scope1'))
        self.assertIsNone(manager2.get_token('scope2'))

    def test_get_global_cache_stats(self):
        """Test global cache statistics across multiple managers."""
        credential1 = MagicMock()
        credential2 = MagicMock()
        
        manager1 = get_token_manager(credential1)
        manager2 = get_token_manager(credential2)
        
        # Add tokens to both managers
        valid_token = MagicMock(spec=AccessToken)
        valid_token.expires_on = time.time() + 1800
        
        expired_token = MagicMock(spec=AccessToken)
        expired_token.expires_on = time.time() - 1800
        
        manager1.set_token('scope1', valid_token)
        manager1.set_token('scope2', expired_token)
        manager2.set_token('scope3', valid_token)
        
        stats = get_global_cache_stats()
        
        self.assertEqual(stats['total_managers'], 2)
        self.assertEqual(stats['total_cached'], 3)
        self.assertEqual(stats['valid_tokens'], 2)
        self.assertEqual(stats['expired_tokens'], 1)
        self.assertEqual(stats['overall_efficiency'], '66.7%')
        self.assertIn('manager_details', stats)

    def test_get_global_cache_stats_no_managers(self):
        """Test global cache statistics with no managers."""
        # Clear any existing managers
        from providers.azure.lib.common import token_manager
        token_manager._token_managers.clear()
        
        stats = get_global_cache_stats()
        
        self.assertEqual(stats['total_managers'], 0)
        self.assertEqual(stats['total_cached'], 0)
        self.assertEqual(stats['valid_tokens'], 0)
        self.assertEqual(stats['expired_tokens'], 0)
        self.assertEqual(stats['overall_efficiency'], '0%')

    @patch('providers.azure.lib.common.token_manager.logger')
    def test_log_authentication_efficiency_with_tokens(self, mock_logger):
        """Test logging authentication efficiency with cached tokens."""
        credential = MagicMock()
        manager = get_token_manager(credential)
        
        valid_token = MagicMock(spec=AccessToken)
        valid_token.expires_on = time.time() + 1800
        manager.set_token('scope1', valid_token)
        
        log_authentication_efficiency()
        
        # Verify info logs were called
        mock_logger.info.assert_any_call('🎯 Authentication Efficiency: 100.0% cache hit rate')
        mock_logger.info.assert_any_call('📊 Token Cache Stats: 1 valid, 0 expired, 1 managers')

    @patch('providers.azure.lib.common.token_manager.logger')
    def test_log_authentication_efficiency_no_tokens(self, mock_logger):
        """Test logging authentication efficiency with no tokens."""
        # Clear any existing managers
        from providers.azure.lib.common import token_manager
        token_manager._token_managers.clear()
        
        log_authentication_efficiency()
        
        # Verify debug log was called
        mock_logger.debug.assert_any_call('📊 No tokens cached yet')

    @patch('providers.azure.lib.common.auth_monitor.optimize_authentication_settings')
    def test_get_optimization_recommendations_with_monitor(self, mock_optimize):
        """Test getting optimization recommendations when auth monitor is available."""
        expected_recommendations = {
            'stats': {'efficiency': '85%'},
            'recommendations': ['Increase cache timeout'],
            'overall_efficiency': 'good'
        }
        mock_optimize.return_value = expected_recommendations
        
        result = get_optimization_recommendations()
        
        self.assertEqual(result, expected_recommendations)
        mock_optimize.assert_called_once()

    def test_get_optimization_recommendations_without_monitor(self):
        """Test getting optimization recommendations when auth monitor is not available."""
        # Mock import error
        with patch('providers.azure.lib.common.auth_monitor.optimize_authentication_settings', 
                  side_effect=ImportError("auth_monitor not available")):
            
            result = get_optimization_recommendations()
            
            self.assertIn('stats', result)
            self.assertIn('recommendations', result)
            self.assertIn('overall_efficiency', result)
            self.assertEqual(result['recommendations'], [])
            self.assertEqual(result['overall_efficiency'], 'unknown')

    @patch('providers.azure.lib.common.auth_monitor.record_authentication')
    def test_token_manager_with_auth_monitor_cache_hit(self, mock_record):
        """Test token manager recording cache hits with auth monitor."""
        token_manager = TokenManager()
        
        # Set a valid token
        valid_token = MagicMock(spec=AccessToken)
        valid_token.expires_on = time.time() + 3600
        token_manager.set_token('scope1', valid_token)
        
        # Get the token (should be cache hit)
        result = token_manager.get_token('scope1')
        
        self.assertEqual(result, valid_token)
        mock_record.assert_called_once_with(0.001, cache_hit=True)

    @patch('providers.azure.lib.common.auth_monitor.record_authentication')
    def test_token_manager_with_auth_monitor_cache_miss(self, mock_record):
        """Test token manager recording cache misses with auth monitor."""
        self.mock_credential.get_token.return_value = self.mock_token
        
        token_manager = TokenManager(self.mock_credential)
        
        # Get a token (should be cache miss)
        result = token_manager.get_token('scope1')
        
        self.assertEqual(result, self.mock_token)
        # Verify record_authentication was called with cache_hit=False
        mock_record.assert_called_once()
        call_args = mock_record.call_args[1]
        self.assertFalse(call_args['cache_hit'])

    def test_token_manager_thread_safety(self):
        """Test token manager thread safety with concurrent access."""
        token_manager = TokenManager()
        
        # Create tokens for concurrent testing
        tokens = []
        for i in range(5):
            token = MagicMock(spec=AccessToken)
            token.expires_on = time.time() + 3600
            token.token = f"token_{i}"
            tokens.append(token)
        
        results = []
        errors = []
        
        def worker(token_index):
            try:
                scope = f'scope_{token_index}'
                token = tokens[token_index]
                
                # Set and get token concurrently
                token_manager.set_token(scope, token)
                retrieved_token = token_manager.get_token(scope)
                results.append((scope, retrieved_token))
            except Exception as e:
                errors.append(e)
        
        # Create and start threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0)
        self.assertEqual(len(results), 5)
        
        # Verify all tokens were set and retrieved correctly
        for scope, retrieved_token in results:
            expected_index = int(scope.split('_')[1])
            self.assertEqual(retrieved_token, tokens[expected_index])

    def test_token_manager_long_scope_name_truncation(self):
        """Test token manager handling of very long scope names."""
        long_scope = 'https://very-long-domain-name-that-exceeds-fifty-characters.microsoft.com/.default'
        self.mock_credential.get_token.return_value = self.mock_token
        
        token_manager = TokenManager(self.mock_credential)
        
        # Should handle long scope names gracefully
        result = token_manager.get_token(long_scope)
        
        self.assertEqual(result, self.mock_token)
        self.mock_credential.get_token.assert_called_once_with(long_scope)

    def test_token_manager_empty_token_response(self):
        """Test token manager handling empty or invalid token response."""
        # Mock credential returning token with no actual token value
        empty_token = MagicMock(spec=AccessToken)
        empty_token.token = ""
        empty_token.expires_on = time.time() + 3600
        
        self.mock_credential.get_token.return_value = empty_token
        
        token_manager = TokenManager(self.mock_credential)
        result = token_manager.get_token('scope1')
        
        # Should return None for empty token
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main()
