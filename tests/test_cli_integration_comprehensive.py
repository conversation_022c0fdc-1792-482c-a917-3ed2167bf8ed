#!/usr/bin/env python3
"""
Comprehensive tests for CLI integration and edge cases to ensure complete coverage.
"""

import unittest
import sys
import subprocess
from unittest.mock import patch, MagicMock
import typer
import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON>ner
import cloudqx

runner = CliRunner()


class TestCLIIntegrationComprehensive(unittest.TestCase):
    """Comprehensive CLI integration tests covering edge cases and error scenarios."""

    def setUp(self):
        """Set up test fixtures."""
        self.orig_argv = sys.argv.copy()

    def tearDown(self):
        """Clean up test fixtures."""
        sys.argv = self.orig_argv

    @patch('cloudqx.subprocess.run')
    def test_azure_user_with_all_parameters(self, mock_run):
        """Test Azure user command with all possible parameters."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_user(debug=True, cloud='china', web=True)
        
        # Verify subprocess was called with all flags
        cmd = mock_run.call_args[0][0]
        self.assertIn('--debug', cmd)
        self.assertIn('--cloud', cmd)
        self.assertIn('china', cmd)
        self.assertIn('--web', cmd)

    @patch('cloudqx.subprocess.run')
    def test_azure_organization_with_all_parameters(self, mock_run):
        """Test Azure organization command with all possible parameters."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_organization(
            debug=True, 
            cloud='global', 
            include_resources=True, 
            full_list=True, 
            web=True
        )
        
        cmd = mock_run.call_args[0][0]
        self.assertIn('--debug', cmd)
        self.assertIn('--cloud', cmd)
        self.assertIn('global', cmd)
        self.assertIn('--include-resources', cmd)
        self.assertIn('--full-list', cmd)
        self.assertIn('--web', cmd)

    @patch('cloudqx.subprocess.run')
    def test_azure_vnet_with_all_parameters(self, mock_run):
        """Test Azure vnet command with all possible parameters."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_vnet(debug=True, cloud='global', web=True)
        
        cmd = mock_run.call_args[0][0]
        self.assertIn('--debug', cmd)
        self.assertIn('--cloud', cmd)
        self.assertIn('global', cmd)
        self.assertIn('--web', cmd)

    @patch('cloudqx.subprocess.run')
    def test_azure_dns_private_with_all_parameters(self, mock_run):
        """Test Azure DNS private command with all possible parameters."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_dns_private(debug=True, cloud='china', web=True)
        
        cmd = mock_run.call_args[0][0]
        self.assertIn('--debug', cmd)
        self.assertIn('--cloud', cmd)
        self.assertIn('china', cmd)
        self.assertIn('--web', cmd)

    def test_azure_user_invalid_cloud_values(self):
        """Test Azure user command with various invalid cloud values."""
        invalid_clouds = ['invalid', 'aws', 'gcp', 'azure', 'microsoft', '']
        
        for invalid_cloud in invalid_clouds:
            with self.assertRaises(typer.Exit) as cm:
                cloudqx.azure_user(debug=False, cloud=invalid_cloud, web=False)
            self.assertEqual(cm.exception.exit_code, 1)

    def test_azure_organization_invalid_cloud_values(self):
        """Test Azure organization command with various invalid cloud values."""
        invalid_clouds = ['invalid', 'aws', 'gcp', 'GLOBAL', 'CHINA']
        
        for invalid_cloud in invalid_clouds:
            with self.assertRaises(typer.Exit) as cm:
                cloudqx.azure_organization(
                    debug=False, 
                    cloud=invalid_cloud, 
                    include_resources=False, 
                    full_list=False, 
                    web=False
                )
            self.assertEqual(cm.exception.exit_code, 1)

    def test_azure_vnet_invalid_cloud_values(self):
        """Test Azure vnet command with various invalid cloud values."""
        invalid_clouds = ['mars', '123', 'global-china', 'china-global']
        
        for invalid_cloud in invalid_clouds:
            with self.assertRaises(typer.Exit) as cm:
                cloudqx.azure_vnet(debug=False, cloud=invalid_cloud, web=False)
            self.assertEqual(cm.exception.exit_code, 1)

    def test_azure_dns_private_invalid_cloud_values(self):
        """Test Azure DNS private command with various invalid cloud values."""
        invalid_clouds = ['local', 'development', 'staging', 'production']
        
        for invalid_cloud in invalid_clouds:
            with self.assertRaises(typer.Exit) as cm:
                cloudqx.azure_dns_private(debug=False, cloud=invalid_cloud, web=False)
            self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_timeout_error(self, mock_run):
        """Test handling of subprocess timeout errors."""
        mock_run.side_effect = subprocess.TimeoutExpired('cmd', 30)
        
        with self.assertRaises(typer.Exit) as cm:
            cloudqx.azure_user(debug=False, cloud=None, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_permission_error(self, mock_run):
        """Test handling of subprocess permission errors."""
        mock_run.side_effect = PermissionError("Permission denied")
        
        with self.assertRaises(typer.Exit) as cm:
            cloudqx.azure_organization(debug=False, cloud=None, include_resources=False, full_list=False, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_os_error(self, mock_run):
        """Test handling of OS errors during subprocess execution."""
        mock_run.side_effect = OSError("System error")
        
        with self.assertRaises(typer.Exit) as cm:
            cloudqx.azure_vnet(debug=False, cloud=None, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_memory_error(self, mock_run):
        """Test handling of memory errors during subprocess execution."""
        mock_run.side_effect = MemoryError("Out of memory")
        
        with self.assertRaises(typer.Exit) as cm:
            cloudqx.azure_dns_private(debug=False, cloud=None, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_keyboard_interrupt(self, mock_run):
        """Test handling of keyboard interrupt during subprocess execution."""
        mock_run.side_effect = KeyboardInterrupt()
        
        with self.assertRaises(typer.Exit) as cm:
            cloudqx.azure_user(debug=False, cloud=None, web=False)
        self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_various_exit_codes(self, mock_run):
        """Test handling of various subprocess exit codes."""
        exit_codes = [1, 2, 3, 5, 10, 127, 255]
        
        for exit_code in exit_codes:
            mock_run.return_value = MagicMock(returncode=exit_code)
            
            with self.assertRaises(typer.Exit) as cm:
                cloudqx.azure_user(debug=False, cloud=None, web=False)
            
            # All non-zero exit codes should result in exit code 1
            self.assertEqual(cm.exception.exit_code, 1)

    @patch('cloudqx.subprocess.run')
    def test_cloud_parameter_case_insensitive(self, mock_run):
        """Test that cloud parameter is properly case-normalized."""
        mock_run.return_value = MagicMock(returncode=0)
        
        # Test various case combinations
        test_cases = [
            ('GLOBAL', 'global'),
            ('Global', 'global'),
            ('CHINA', 'china'),
            ('China', 'china'),
            ('china', 'china'),
            ('global', 'global')
        ]
        
        for input_cloud, expected_cloud in test_cases:
            cloudqx.azure_user(debug=False, cloud=input_cloud, web=False)
            
            cmd = mock_run.call_args[0][0]
            self.assertIn('--cloud', cmd)
            self.assertIn(expected_cloud, cmd)

    @patch('cloudqx.subprocess.run')
    def test_debug_flag_in_exception_handling(self, mock_run):
        """Test that debug flag enables detailed error reporting."""
        mock_run.side_effect = subprocess.SubprocessError("Test error")
        
        # Test with debug=True should include traceback
        with patch('cloudqx.typer.echo') as mock_echo:
            with self.assertRaises(typer.Exit):
                cloudqx.azure_user(debug=True, cloud=None, web=False)
            
            # Should have error message
            error_calls = [call for call in mock_echo.call_args_list if 'Error:' in str(call)]
            self.assertTrue(len(error_calls) > 0)

    @patch('cloudqx.subprocess.run')
    def test_project_root_path_handling(self, mock_run):
        """Test that subprocess is run with correct project root."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_user(debug=False, cloud=None, web=False)
        
        # Verify cwd parameter
        call_kwargs = mock_run.call_args[1]
        self.assertIn('cwd', call_kwargs)
        # Should be string representation of project root
        self.assertIsInstance(call_kwargs['cwd'], str)

    def test_main_callback_context_handling(self):
        """Test main callback function handles context properly."""
        ctx = MagicMock()
        
        # Should not raise any exceptions
        try:
            result = cloudqx.main(ctx, None)
            self.assertIsNone(result)
        except Exception as e:
            self.fail(f"main callback raised unexpected exception: {e}")

    def test_version_callback_edge_cases(self):
        """Test version callback with various input values."""
        should_exit_values = [True, 1, 'true', 'false']
        should_not_exit_values = [False, None, 0, '']

        for value in should_exit_values:
            with patch('cloudqx.typer.echo') as mock_echo:
                with self.assertRaises(typer.Exit):
                    cloudqx.version_callback(value)
                mock_echo.assert_called_once()

        for value in should_not_exit_values:
            with patch('cloudqx.typer.echo') as mock_echo: # Patch echo to prevent actual output
                result = cloudqx.version_callback(value)
                self.assertIsNone(result)
                mock_echo.assert_not_called() # Should not echo anything

    @patch('cloudqx.sys.executable', new='/custom/path/to/python')
    def test_python_executable_path_handling(self):
        """Test handling of different Python executable paths."""
        with patch('cloudqx.subprocess.run') as mock_run:
            mock_run.return_value = MagicMock(returncode=0)
            cloudqx.azure_user(debug=False, cloud=None, web=False)
            cmd = mock_run.call_args[0][0]
            self.assertEqual(cmd[0], '/custom/path/to/python')

    @patch('cloudqx.subprocess.run')
    def test_cli_parameter_order(self, mock_run):
        """Test that CLI parameters are passed in correct order."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_organization(
            debug=True,
            cloud='china',
            include_resources=True,
            full_list=True,
            web=True
        )
        
        cmd = mock_run.call_args[0][0]
        
        # Verify base command structure
        self.assertEqual(cmd[0], sys.executable)
        self.assertEqual(cmd[1], '-m')
        self.assertEqual(cmd[2], 'providers.azure.organization')
        
        # Verify flags are present (order may vary)
        expected_flags = ['--debug', '--cloud', 'china', '--include-resources', '--full-list', '--web']
        for flag in expected_flags:
            self.assertIn(flag, cmd)

    def test_azure_commands_module_paths(self):
        """Test that Azure commands use correct module paths."""
        test_cases = [
            (cloudqx.azure_user, 'providers.azure.user'),
            (cloudqx.azure_organization, 'providers.azure.organization'),
            (cloudqx.azure_vnet, 'providers.azure.vnet'),
            (cloudqx.azure_dns_private, 'providers.azure.dns_private')
        ]
        
        for command_func, expected_module in test_cases:
            with patch('cloudqx.subprocess.run') as mock_run:
                mock_run.return_value = MagicMock(returncode=0)
                
                # Call with minimal parameters
                if command_func == cloudqx.azure_organization:
                    command_func(debug=False, cloud=None, include_resources=False, full_list=False, web=False)
                else:
                    command_func(debug=False, cloud=None, web=False)
                
                cmd = mock_run.call_args[0][0]
                
                # Check for -m flag and module name
                if '-m' in cmd:
                    module_index = cmd.index('-m') + 1
                    self.assertEqual(cmd[module_index], expected_module)

    @patch('cloudqx.subprocess.run')
    def test_subprocess_run_parameters(self, mock_run):
        """Test that subprocess.run is called with correct parameters."""
        mock_run.return_value = MagicMock(returncode=0)
        
        cloudqx.azure_user(debug=False, cloud=None, web=False)
        
        # Verify call parameters
        call_kwargs = mock_run.call_args[1]
        self.assertIn('cwd', call_kwargs)
        self.assertIn('check', call_kwargs)
        self.assertIn('capture_output', call_kwargs)
        
        # Verify specific parameter values
        self.assertFalse(call_kwargs['check'])  # Should not raise on non-zero exit
        self.assertFalse(call_kwargs['capture_output'])  # Should let output go to terminal


class TestCLICommandLineParsing:
    """Test CLI command line parsing using typer.testing.CliRunner."""

    def test_cli_help_messages(self):
        """Test that help messages are displayed correctly."""
        help_commands = [
            ['--help'],
            ['azure', '--help'],
            ['azure', 'user', '--help'],
            ['azure', 'organization', '--help'],
            ['azure', 'vnet', '--help'],
            ['azure', 'dns-private', '--help'],
            ['aws', '--help'],
            ['gcp', '--help']
        ]
        
        for cmd in help_commands:
            result = runner.invoke(cloudqx.app, cmd)
            assert result.exit_code == 0
            assert len(result.stdout) > 0

    def test_cli_version_commands(self):
        """Test version commands and flags."""
        version_commands = [
            ['--version'],
            ['-v'],
            ['version']
        ]
        
        for cmd in version_commands:
            result = runner.invoke(cloudqx.app, cmd)
            assert result.exit_code == 0
            assert f"CloudQX v{cloudqx.__version__}" in result.stdout

    def test_cli_unsupported_commands(self):
        """Test handling of unsupported or invalid commands."""
        invalid_commands = [
            ['invalid'],
            ['azure', 'invalid'],
            ['aws', 'user'],  # AWS not implemented yet
            ['gcp', 'organization'],  # GCP not implemented yet
        ]
        
        for cmd in invalid_commands:
            result = runner.invoke(cloudqx.app, cmd)
            # Should either show help or indicate command not found
            assert result.exit_code != 0 or "Usage:" in result.stdout

    @patch('cloudqx.subprocess.run')
    def test_cli_flag_combinations(self, mock_run):
        """Test various flag combinations through CLI."""
        mock_run.return_value = MagicMock(returncode=0)
        
        test_combinations = [
            ['azure', 'user', '-d'],
            ['azure', 'user', '--debug'],
            ['azure', 'user', '-c', 'global'],
            ['azure', 'user', '--cloud', 'china'],
            ['azure', 'user', '-w'],
            ['azure', 'user', '--web'],
            ['azure', 'user', '-d', '-c', 'global', '-w'],
            ['azure', 'organization', '-r', '-f'],
            ['azure', 'organization', '--include-resources', '--full-list'],
        ]
        
        for cmd in test_combinations:
            result = runner.invoke(cloudqx.app, cmd)
            assert result.exit_code == 0

    def test_cli_error_messages(self):
        """Test that appropriate error messages are shown."""
        error_cases = [
            (['azure', 'user', '--cloud', 'invalid'], "Invalid cloud environment"),
            (['azure', 'organization', '--cloud', 'invalid'], "Invalid cloud environment"),
            (['azure', 'vnet', '--cloud', 'invalid'], "Invalid cloud environment"),
            (['azure', 'dns-private', '--cloud', 'invalid'], "Invalid cloud environment"),
        ]
        
        for cmd, expected_error in error_cases:
            result = runner.invoke(cloudqx.app, cmd)
            assert result.exit_code == 1
            assert expected_error in result.stdout


if __name__ == '__main__':
    # Run unittest tests
    unittest.main(verbosity=2, exit=False)
    
    # Run pytest tests
    pytest.main([__file__, '-v'])
