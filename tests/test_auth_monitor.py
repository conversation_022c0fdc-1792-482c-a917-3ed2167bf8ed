import unittest
from unittest.mock import patch, MagicMock
from providers.azure.lib.common.auth_monitor import AuthenticationMonitor, get_auth_monitor, record_authentication, log_auth_efficiency, optimize_authentication_settings

class TestAuthenticationMonitor(unittest.TestCase):

    def setUp(self):
        # Reset the global monitor instance before each test
        from providers.azure.lib.common import auth_monitor
        auth_monitor._global_monitor = AuthenticationMonitor()
        self.monitor = get_auth_monitor()
        self.monitor.auth_attempts = 0
        self.monitor.cache_hits = 0
        self.monitor.cache_misses = 0
        self.monitor.total_auth_time = 0.0
        # Mock start time for consistent uptime
        self.monitor.start_time = 0

    def test_record_auth_attempt(self):
        """Test recording an authentication attempt."""
        record_authentication(duration=1.5, cache_hit=True)
        self.assertEqual(self.monitor.auth_attempts, 1)
        self.assertEqual(self.monitor.total_auth_time, 1.5)
        self.assertEqual(self.monitor.cache_hits, 1)
        self.assertEqual(self.monitor.cache_misses, 0)

        record_authentication(duration=0.5, cache_hit=False)
        self.assertEqual(self.monitor.auth_attempts, 2)
        self.assertEqual(self.monitor.total_auth_time, 2.0)
        self.assertEqual(self.monitor.cache_hits, 1)
        self.assertEqual(self.monitor.cache_misses, 1)

    def test_get_efficiency_stats(self):
        """Test retrieving efficiency statistics."""
        with patch('time.time', return_value=10.0):
            record_authentication(duration=1.0, cache_hit=True)
            record_authentication(duration=1.0, cache_hit=False)
            stats = self.monitor.get_efficiency_stats()
            self.assertAlmostEqual(stats['uptime_seconds'], 10.0)
            self.assertEqual(stats['total_auth_attempts'], 2)
            self.assertEqual(stats['cache_hits'], 1)
            self.assertEqual(stats['cache_misses'], 1)
            self.assertEqual(stats['cache_hit_rate'], '50.0%')
            self.assertEqual(stats['avg_auth_time'], '1.000s')
            self.assertEqual(stats['total_auth_time'], '2.000s')

    @patch('providers.azure.lib.common.auth_monitor.logger')
    def test_log_efficiency_report(self, mock_logger):
        """Test logging the efficiency report."""
        with patch('time.time', return_value=10.0):
            record_authentication(duration=1.0, cache_hit=True)
            log_auth_efficiency()
            mock_logger.info.assert_any_call('🎯 Authentication Efficiency Report:')
            mock_logger.info.assert_any_call('   📊 Cache Hit Rate: 100.0%')

    def test_optimize_authentication_settings_high_priority(self):
        """Test optimization suggestions for low cache hit rate and many attempts."""
        with patch('time.time', return_value=10.0):
            for _ in range(60):
                record_authentication(duration=3.0, cache_hit=False) # Simulate many slow cache misses
            
            recommendations = optimize_authentication_settings()
            self.assertGreater(len(recommendations['recommendations']), 0)
            self.assertEqual(recommendations['overall_efficiency'], 'needs_improvement')
            self.assertTrue(any('Low cache hit rate' in r['issue'] for r in recommendations['recommendations']))
            self.assertTrue(any('Slow authentication' in r['issue'] for r in recommendations['recommendations']))
            self.assertTrue(any('Too many authentication attempts' in r['issue'] for r in recommendations['recommendations']))

    def test_optimize_authentication_settings_good_priority(self):
        """Test optimization suggestions for good cache hit rate and average attempts."""
        with patch('time.time', return_value=10.0):
            for _ in range(10):
                record_authentication(duration=0.5, cache_hit=True) # Simulate fast cache hits
            
            recommendations = optimize_authentication_settings()
            self.assertEqual(len(recommendations['recommendations']), 0)
            self.assertEqual(recommendations['overall_efficiency'], 'excellent')

if __name__ == '__main__':
    unittest.main()
