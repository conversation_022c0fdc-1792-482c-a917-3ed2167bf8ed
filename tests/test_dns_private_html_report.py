import os
import pathlib
import pytest

from providers.azure.lib.dns_private.html_report import generate_dns_private_html_report

@pytest.fixture(autouse=True)
def cwd_tmp_path(tmp_path, monkeypatch):
    # Run generate in isolated tmp path
    monkeypatch.chdir(tmp_path)
    return tmp_path

def test_generate_dns_private_html_report_includes_all_types(cwd_tmp_path):
    subs_data = [{
        'subscription_name': 'sub1',
        'zones': [
            {'id': '/subs/id/rg/providers/Microsoft.Network/privateDnsZones/zone1',
             'name': 'zone1', 'location': 'loc1'}
        ],
        'vnet_links': [
            {'id': '/subs/id/rg/providers/Microsoft.Network/privateDnsZones/zone1/virtualNetworkLinks/link1',
             'name': 'link1', 'location': 'loc1', 'zoneName': 'zone1'}
        ],
        'endpoints': [
            {'id': 'epid', 'name': 'ep1', 'location': 'loc1'}
        ],
        'resolvers': [
            {'id': 'resid', 'name': 'res1', 'location': 'loc1'}
        ],
        'forwarding_rules': [
            {'id': 'fwdid', 'name': 'fwd1', 'location': 'loc1'}
        ]
    }]
    output = generate_dns_private_html_report(subs_data,
                                             output_file="dns_report.html",
                                             cloud_region="test")
    # Expect html file under html directory
    expected = cwd_tmp_path / "html" / "dns_report_test.html"
    assert pathlib.Path(output) == expected
    assert expected.exists()
    content = expected.read_text(encoding='utf-8')
    # Should include container and header CSS reference
    assert '<div class="container">' in content
    assert 'dns_private/dns_private_report.css' in content
    assert 'dns_private/dns_private_report.js' in content
    # Should include all resource names in the embedded JSON data
    for name in ('zone1', 'link1', 'ep1', 'res1', 'fwd1'):
        assert name in content
    # Should include the subscription name
    assert 'sub1' in content
    # Should include window.dnsPrivateData for JavaScript
    assert 'window.dnsPrivateData' in content