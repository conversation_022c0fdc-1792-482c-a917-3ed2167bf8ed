import sys
import unittest
from unittest.mock import patch

from providers.azure.user import parse_user_arguments

class TestUserArguments(unittest.TestCase):
    @patch.object(sys, 'argv', ['prog'])
    def test_default_user_args(self):
        args = parse_user_arguments()
        self.assertFalse(args.debug)
        self.assertIsNone(args.cloud)
        self.assertFalse(args.web)

    @patch.object(sys, 'argv', ['prog', '-d', '-c', 'global', '-w'])
    def test_user_args_all_flags(self):
        args = parse_user_arguments()
        self.assertTrue(args.debug)
        self.assertEqual(args.cloud, 'global')
        self.assertTrue(args.web)

if __name__ == '__main__':
    unittest.main()