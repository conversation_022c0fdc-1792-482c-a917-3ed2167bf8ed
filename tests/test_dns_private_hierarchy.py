#!/usr/bin/env python3
"""
Test cases for DNS Private resolver hierarchy building functionality.
"""
import pytest
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from providers.azure.lib.dns_private.data import build_resolver_hierarchy


class TestDNSPrivateHierarchy:
    """Test cases for DNS Private resolver hierarchy building."""

    def test_build_resolver_hierarchy_basic(self):
        """Test basic hierarchy building with resolver, ruleset, and rules."""
        # Mock data
        resolvers = [
            {
                'name': 'test-resolver',
                'resource_guid': 'resolver-guid-123',
                'subscription': 'sub-123',
                'resource_group': 'rg-dns',
                'location': 'eastus',
                'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsResolvers/test-resolver'
            }
        ]
        
        rulesets = [
            {
                'name': 'test-ruleset',
                'subscription': 'sub-123',
                'resource_group': 'rg-dns',
                'location': 'eastus',
                'outbound_endpoints': [
                    {
                        'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsResolvers/test-resolver/outboundEndpoints/endpoint1'
                    }
                ],
                'id': '/subscriptions/sub-123/resourceGroups/rg-dns/providers/Microsoft.Network/dnsForwardingRulesets/test-ruleset'
            }
        ]
        
        rules = [
            {
                'name': 'test-rule',
                'ruleset_name': 'test-ruleset',
                'domain_name': 'example.com',
                'target_dns_servers': [{'ip_address': '*******', 'port': 53}]
            }
        ]
        
        # Build hierarchy
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        # Assertions
        assert len(hierarchy) == 1
        assert hierarchy[0]['resolver']['name'] == 'test-resolver'
        assert len(hierarchy[0]['rulesets']) == 1
        assert hierarchy[0]['rulesets'][0]['ruleset']['name'] == 'test-ruleset'
        assert len(hierarchy[0]['rulesets'][0]['rules']) == 1
        assert hierarchy[0]['rulesets'][0]['rules'][0]['name'] == 'test-rule'

    def test_build_resolver_hierarchy_naming_pattern(self):
        """Test hierarchy building with naming pattern matching."""
        resolvers = [
            {
                'name': 'my-resolver',
                'resource_guid': 'resolver-guid-456',
                'subscription': 'sub-456',
                'resource_group': 'rg-dns',
                'location': 'westus'
            }
        ]
        
        rulesets = [
            {
                'name': 'ruleset-my-resolver',  # Naming pattern match
                'subscription': 'sub-456',
                'resource_group': 'rg-dns',
                'location': 'westus',
                'outbound_endpoints': []  # No endpoint matching
            }
        ]
        
        rules = [
            {
                'name': 'pattern-rule',
                'ruleset_name': 'ruleset-my-resolver',
                'domain_name': 'pattern.com'
            }
        ]
        
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        assert len(hierarchy) == 1
        assert hierarchy[0]['resolver']['name'] == 'my-resolver'
        assert len(hierarchy[0]['rulesets']) == 1
        assert hierarchy[0]['rulesets'][0]['ruleset']['name'] == 'ruleset-my-resolver'
        assert len(hierarchy[0]['rulesets'][0]['rules']) == 1

    def test_build_resolver_hierarchy_resource_group_matching(self):
        """Test hierarchy building with resource group matching."""
        resolvers = [
            {
                'name': 'rg-resolver',
                'resource_guid': 'resolver-guid-789',
                'subscription': 'sub-789',
                'resource_group': 'rg-dns-test',
                'location': 'centralus'
            }
        ]
        
        rulesets = [
            {
                'name': 'random-ruleset',  # No naming pattern
                'subscription': 'sub-789',
                'resource_group': 'rg-dns-test',  # Same RG and subscription
                'location': 'centralus',
                'outbound_endpoints': []  # No endpoint matching
            }
        ]
        
        rules = [
            {
                'name': 'rg-rule',
                'ruleset_name': 'random-ruleset',
                'domain_name': 'resourcegroup.com'
            }
        ]
        
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        assert len(hierarchy) == 1
        assert hierarchy[0]['resolver']['name'] == 'rg-resolver'
        assert len(hierarchy[0]['rulesets']) == 1
        assert hierarchy[0]['rulesets'][0]['ruleset']['name'] == 'random-ruleset'

    def test_build_resolver_hierarchy_standalone_ruleset(self):
        """Test hierarchy building with standalone rulesets."""
        resolvers = [
            {
                'name': 'isolated-resolver',
                'resource_guid': 'resolver-guid-isolated',
                'subscription': 'sub-isolated',
                'resource_group': 'rg-isolated',
                'location': 'northeurope'
            }
        ]
        
        rulesets = [
            {
                'name': 'standalone-ruleset',
                'subscription': 'sub-different',  # Different subscription
                'resource_group': 'rg-different',  # Different RG
                'location': 'westeurope',
                'outbound_endpoints': []  # No endpoints
            }
        ]
        
        rules = [
            {
                'name': 'standalone-rule',
                'ruleset_name': 'standalone-ruleset',
                'domain_name': 'standalone.com'
            }
        ]
        
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        # Should have 2 entries: 1 resolver + 1 standalone ruleset
        assert len(hierarchy) == 2
        
        # Find the resolver entry
        resolver_entry = next(h for h in hierarchy if h['resolver'] is not None)
        assert resolver_entry['resolver']['name'] == 'isolated-resolver'
        assert len(resolver_entry['rulesets']) == 0  # No associated rulesets
        
        # Find the standalone entry
        standalone_entry = next(h for h in hierarchy if h['resolver'] is None)
        assert standalone_entry['resolver'] is None
        assert len(standalone_entry['rulesets']) == 1
        assert standalone_entry['rulesets'][0]['ruleset']['name'] == 'standalone-ruleset'
        assert len(standalone_entry['rulesets'][0]['rules']) == 1

    def test_build_resolver_hierarchy_empty_data(self):
        """Test hierarchy building with empty data."""
        hierarchy = build_resolver_hierarchy([], [], [])
        assert len(hierarchy) == 0

    def test_build_resolver_hierarchy_multiple_resolvers(self):
        """Test hierarchy building with multiple resolvers and complex relationships."""
        resolvers = [
            {
                'name': 'resolver-1',
                'resource_guid': 'guid-1',
                'subscription': 'sub-1',
                'resource_group': 'rg-1',
                'location': 'eastus'
            },
            {
                'name': 'resolver-2',
                'resource_guid': 'guid-2',
                'subscription': 'sub-2',
                'resource_group': 'rg-2',
                'location': 'westus'
            }
        ]
        
        rulesets = [
            {
                'name': 'ruleset-resolver-1',  # Naming pattern for resolver-1
                'subscription': 'sub-1',
                'resource_group': 'rg-1',
                'outbound_endpoints': []
            },
            {
                'name': 'other-ruleset',  # Resource group match for resolver-2
                'subscription': 'sub-2',
                'resource_group': 'rg-2',
                'outbound_endpoints': []
            }
        ]
        
        rules = [
            {
                'name': 'rule-1',
                'ruleset_name': 'ruleset-resolver-1',
                'domain_name': 'rule1.com'
            },
            {
                'name': 'rule-2',
                'ruleset_name': 'other-ruleset',
                'domain_name': 'rule2.com'
            }
        ]
        
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        assert len(hierarchy) == 2
        
        # Check both resolvers have their associated rulesets
        for entry in hierarchy:
            assert entry['resolver'] is not None
            assert len(entry['rulesets']) == 1
            assert len(entry['rulesets'][0]['rules']) == 1

    def test_build_resolver_hierarchy_orphaned_rules(self):
        """Test hierarchy building with rules that don't match any ruleset."""
        resolvers = [
            {
                'name': 'test-resolver',
                'resource_guid': 'guid-test',
                'subscription': 'sub-test',
                'resource_group': 'rg-test',
                'location': 'eastus'
            }
        ]
        
        rulesets = [
            {
                'name': 'test-ruleset',
                'subscription': 'sub-test',
                'resource_group': 'rg-test',
                'outbound_endpoints': []
            }
        ]
        
        rules = [
            {
                'name': 'matched-rule',
                'ruleset_name': 'test-ruleset',
                'domain_name': 'matched.com'
            },
            {
                'name': 'orphaned-rule',
                'ruleset_name': 'nonexistent-ruleset',  # This ruleset doesn't exist
                'domain_name': 'orphaned.com'
            }
        ]
        
        hierarchy = build_resolver_hierarchy(resolvers, rulesets, rules)
        
        assert len(hierarchy) == 1
        assert hierarchy[0]['resolver']['name'] == 'test-resolver'
        assert len(hierarchy[0]['rulesets']) == 1
        # Only the matched rule should be included
        assert len(hierarchy[0]['rulesets'][0]['rules']) == 1
        assert hierarchy[0]['rulesets'][0]['rules'][0]['name'] == 'matched-rule'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])

# Contains AI-generated edits.
