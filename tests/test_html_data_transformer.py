import unittest
import unittest
from providers.azure.lib.user.html_data_transformer import (
    create_user_rows,
    get_report_stats
)

class TestHtmlDataTransformer(unittest.TestCase):

    def setUp(self):
        self.users_data = [
            {
                'displayName': 'Test User 1', 
                'userPrincipalName': '<EMAIL>',
                'entra_roles': ['Reader'],
                'rbac_roles': {
                    'subscriptions': {'sub1': ['Contributor']}
                }
            },
            {
                'displayName': 'Test User 2', 
                'userPrincipalName': '<EMAIL>',
                'entra_roles': [],
                'rbac_roles': {}
            }
        ]

    def test_create_user_rows(self):
        """Test creating user rows for the HTML report."""
        html = create_user_rows(self.users_data)
        self.assertIn('Test User 1', html)
        self.assertIn('<EMAIL>', html)
        self.assertIn('Reader', html)
        self.assertIn('Contributor', html)
        self.assertIn('Test User 2', html)

    def test_get_report_stats(self):
        """Test calculating report statistics."""
        stats = get_report_stats(self.users_data)
        self.assertEqual(stats['total_users'], 2)
        self.assertEqual(stats['users_with_entra_roles'], 1)
        self.assertEqual(stats['users_with_rbac_roles'], 1)
        self.assertEqual(stats['users_with_both'], 1)
        self.assertEqual(stats['unique_rbac_roles'], 1)


if __name__ == '__main__':
    unittest.main()
