#!/usr/bin/env python3
"""
Comprehensive tests for HTML export functionality to cover missing test cases.
"""

import unittest
import os
import tempfile
import threading
import time
from unittest.mock import patch, mock_open, MagicMock, call
from providers.azure.lib.user.html_export import generate_html_report, _create_html_content


class TestHtmlExportComprehensive(unittest.TestCase):
    """Comprehensive tests for HTML export functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.sample_user_data = [
            {
                'displayName': 'Test User 1',
                'userPrincipalName': '<EMAIL>',
                'entra_roles': ['Global Administrator'],
                'rbac_roles': [{'role': 'Contributor', 'scope': '/subscriptions/123'}]
            },
            {
                'displayName': 'Test User 2', 
                'userPrincipalName': '<EMAIL>',
                'entra_roles': [],
                'rbac_roles': []
            }
        ]

    @patch('os.path.exists', return_value=False)
    @patch('os.makedirs')
    @patch('builtins.open', new_callable=mock_open)
    @patch('providers.azure.lib.user.html_export.webbrowser.open')
    @patch('providers.azure.lib.user.html_export.start_web_server')
    @patch('providers.azure.lib.user.html_export.find_available_port', return_value=8080)
    def test_generate_html_report_with_web_server_success(self, mock_find_port, mock_start_web_server, 
                                                         mock_webbrowser, mock_file, mock_makedirs, mock_exists):
        """Test successful HTML report generation with web server."""
        # Mock the server thread with actual_port attribute
        mock_thread = MagicMock()
        mock_thread.actual_port = 8080  # Set the actual port
        mock_start_web_server.return_value = mock_thread
        
        result = generate_html_report(
            self.sample_user_data, 
            'test_report.html', 
            cloud_region='global',
            use_web_server=True,
            keep_server_alive=False
        )
        
        # Verify directory creation
        mock_makedirs.assert_called_once_with('html')
        
        # Verify file writing
        expected_path = os.path.abspath('html/test_report_global.html')
        mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
        
        # Verify web server setup
        mock_find_port.assert_called_once()
        mock_start_web_server.assert_called_once_with(expected_path, 8080, daemon=True)
        # Updated to match the new URL format
        mock_webbrowser.assert_called_once_with('http://127.0.0.1:8080/test_report_global.html')
        
        self.assertEqual(result, expected_path)

    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('providers.azure.lib.user.html_export.webbrowser.open')
    @patch('providers.azure.lib.user.html_export.start_web_server')
    def test_generate_html_report_without_web_server(self, mock_start_web_server, mock_webbrowser, 
                                                    mock_file, mock_exists):
        """Test HTML report generation without web server."""
        result = generate_html_report(
            self.sample_user_data,
            'test_report.html',
            cloud_region='china',
            use_web_server=False
        )
        
        # Verify no web server or browser calls
        mock_start_web_server.assert_not_called()
        mock_webbrowser.assert_not_called()
        
        # Verify file writing with cloud region
        expected_path = os.path.abspath('html/test_report_china.html')
        mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
        
        self.assertEqual(result, expected_path)

    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('providers.azure.lib.user.html_export.start_web_server')
    @patch('providers.azure.lib.user.html_export.find_available_port', side_effect=Exception("Port error"))
    def test_generate_html_report_web_server_failure_fallback(self, mock_find_port, mock_start_web_server, mock_file, mock_exists):
        """Test fallback to file mode when web server fails."""
        result = generate_html_report(
            self.sample_user_data,
            'test_report.html',
            use_web_server=True
        )
        
        # Verify web server was attempted but failed
        mock_find_port.assert_called_once()
        mock_start_web_server.assert_not_called()
        
        # Verify file was still created
        expected_path = os.path.abspath('html/test_report_global.html')
        self.assertEqual(result, expected_path)

    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('providers.azure.lib.user.html_export.start_web_server')
    @patch('providers.azure.lib.user.html_export.find_available_port', return_value=9000)
    def test_generate_html_report_keep_server_alive(self, mock_find_port, mock_start_web_server, mock_file, mock_exists):
        """Test HTML report generation with persistent web server."""
        mock_thread = MagicMock()
        mock_thread.join.side_effect = KeyboardInterrupt()  # Simulate user stopping server
        mock_start_web_server.return_value = mock_thread
        
        result = generate_html_report(
            self.sample_user_data,
            'test_report.html',
            use_web_server=True,
            keep_server_alive=True
        )
        
        # Verify server thread join was called (server kept alive)
        mock_thread.join.assert_called_once()
        
        expected_path = os.path.abspath('html/test_report_global.html')
        self.assertEqual(result, expected_path)

    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', side_effect=IOError("File write error"))
    def test_generate_html_report_file_error(self, mock_file, mock_exists):
        """Test error handling when file writing fails."""
        with self.assertRaises(IOError):
            generate_html_report(self.sample_user_data, 'test_report.html')

    def test_generate_html_report_custom_filename_extension(self):
        """Test handling of custom filename with extension."""
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', new_callable=mock_open) as mock_file:
            
            result = generate_html_report(
                self.sample_user_data,
                'custom_name.html',
                use_web_server=False
            )
            
            # Should use the base name without extension and add cloud region
            expected_path = os.path.abspath('html/custom_name_global.html')
            mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
            self.assertEqual(result, expected_path)

    def test_generate_html_report_custom_filename_no_extension(self):
        """Test handling of custom filename without extension."""
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', new_callable=mock_open) as mock_file:
            
            result = generate_html_report(
                self.sample_user_data,
                'custom_name',
                use_web_server=False
            )
            
            # Should use the custom name and add cloud region
            expected_path = os.path.abspath('html/custom_name_global.html')
            mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
            self.assertEqual(result, expected_path)

    @patch('providers.azure.lib.user.html_export.get_report_stats')
    @patch('providers.azure.lib.user.html_export.create_user_rows')
    @patch('providers.azure.lib.user.html_export.HTML_TEMPLATE')
    def test_create_html_content(self, mock_template, mock_create_rows, mock_get_stats):
        """Test HTML content creation with proper template formatting."""
        # Mock the stats and rows
        mock_get_stats.return_value = {
            'total_users': 2,
            'users_with_entra_roles': 1,
            'users_with_rbac_roles': 1,
            'users_with_both': 0,
            'unique_rbac_roles': 1
        }
        mock_create_rows.return_value = '<tr><td>Test User</td></tr>'
        mock_template.format.return_value = '<html>Mock HTML Content</html>'
        
        result = _create_html_content(self.sample_user_data)
        
        # Verify stats and rows functions were called
        mock_get_stats.assert_called_once_with(self.sample_user_data)
        mock_create_rows.assert_called_once_with(self.sample_user_data)
        
        # Verify template format was called with expected parameters
        mock_template.format.assert_called_once()
        call_kwargs = mock_template.format.call_args[1]
        
        self.assertEqual(call_kwargs['total_users'], 2)
        self.assertEqual(call_kwargs['users_with_entra_roles'], 1)
        self.assertEqual(call_kwargs['users_with_rbac_roles'], 1)
        self.assertEqual(call_kwargs['users_with_both'], 0)
        self.assertEqual(call_kwargs['unique_rbac_roles'], 1)
        self.assertIn('generation_date', call_kwargs)
        self.assertEqual(call_kwargs['user_rows'], '<tr><td>Test User</td></tr>')
        
        self.assertEqual(result, '<html>Mock HTML Content</html>')

    def test_generate_html_report_empty_user_data(self):
        """Test HTML report generation with empty user data."""
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', new_callable=mock_open) as mock_file:
            
            result = generate_html_report([], 'empty_report.html', use_web_server=False)
            
            # Should still create file
            expected_path = os.path.abspath('html/empty_report_global.html')
            mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
            self.assertEqual(result, expected_path)

    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    def test_generate_html_report_file_already_in_html_dir(self, mock_file, mock_exists):
        """Test that files already in html/ directory are handled correctly."""
        result = generate_html_report(
            self.sample_user_data,
            'html/already_in_html.html',
            use_web_server=False
        )
        
        # Should still place in html directory with cloud region
        expected_path = os.path.abspath('html/already_in_html_global.html')
        mock_file.assert_called_once_with(expected_path, 'w', encoding='utf-8')
        self.assertEqual(result, expected_path)


if __name__ == '__main__':
    unittest.main()
