#!/usr/bin/env python3
"""
Tests for the CloudAwareAzureCliCredential module.
"""

import pytest
import json
import subprocess
from unittest.mock import Mock, patch, MagicMock
from azure.core.credentials import AccessToken
from azure.core.exceptions import ClientAuthenticationError
from azure.identity import CredentialUnavailableError

# Import the modules under test
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from providers.azure.lib.common.china_cli_credential import CloudAwareAzureCliCredential, ChinaAzureCliCredential
from providers.azure.lib.common.config import CLOUD_ENVIRONMENTS


class TestCloudAwareAzureCliCredential:
    """Test cases for CloudAwareAzureCliCredential."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.mock_token_response = {
            'accessToken': 'mock_access_token_12345',
            'expiresOn': '2025-07-04T12:00:00.000000+00:00'
        }
        
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    def test_init_auto_detect_cloud(self, mock_get_cloud_config, mock_detect_cloud):
        """Test initialization with auto-detection of cloud environment."""
        # Setup mocks
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        # Create credential
        credential = CloudAwareAzureCliCredential()
        
        # Verify calls
        mock_detect_cloud.assert_called_once()
        mock_get_cloud_config.assert_called_once_with('china')
        
        # Verify attributes
        assert credential.cloud_env == 'china'
        assert credential.process_timeout == 10
        assert credential.cloud_config == CLOUD_ENVIRONMENTS['china']
    
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    def test_init_explicit_cloud(self, mock_get_cloud_config, mock_detect_cloud):
        """Test initialization with explicit cloud environment."""
        # Setup mocks
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        # Create credential with explicit cloud
        credential = CloudAwareAzureCliCredential(cloud_env='global', process_timeout=30)
        
        # Verify detect_azure_cloud was not called
        mock_detect_cloud.assert_not_called()
        mock_get_cloud_config.assert_called_once_with('global')
        
        # Verify attributes
        assert credential.cloud_env == 'global'
        assert credential.process_timeout == 30
        assert credential.cloud_config == CLOUD_ENVIRONMENTS['global']
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_success_management_scope(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test successful token retrieval for management scope."""
        # Setup mocks
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout=json.dumps(self.mock_token_response),
            stderr=''
        )
        
        # Create credential and get token
        credential = CloudAwareAzureCliCredential()
        token = credential.get_token('https://management.chinacloudapi.cn/.default')
        
        # Verify subprocess call
        expected_cmd = [
            'az', 'account', 'get-access-token',
            '--output', 'json',
            '--resource', 'https://management.chinacloudapi.cn'
        ]
        mock_subprocess.assert_called_once_with(
            expected_cmd,
            capture_output=True,
            text=True,
            timeout=10,
            check=False
        )
        
        # Verify token
        assert isinstance(token, AccessToken)
        assert token.token == 'mock_access_token_12345'
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_success_graph_scope(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test successful token retrieval for Microsoft Graph scope."""
        # Setup mocks
        mock_detect_cloud.return_value = 'global'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout=json.dumps(self.mock_token_response),
            stderr=''
        )
        
        # Create credential and get token
        credential = CloudAwareAzureCliCredential()
        token = credential.get_token('https://graph.microsoft.com/.default')
        
        # Verify subprocess call uses Graph endpoint
        expected_cmd = [
            'az', 'account', 'get-access-token',
            '--output', 'json',
            '--resource', 'https://graph.microsoft.com'
        ]
        mock_subprocess.assert_called_once_with(
            expected_cmd,
            capture_output=True,
            text=True,
            timeout=10,
            check=False
        )
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    def test_get_token_no_scopes(self, mock_detect_cloud, mock_get_cloud_config):
        """Test error when no scopes are provided."""
        mock_detect_cloud.return_value = 'global'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(ValueError, match="At least one scope must be specified"):
            credential.get_token()
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_not_logged_in(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test error when Azure CLI is not logged in."""
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        mock_subprocess.return_value = Mock(
            returncode=1,
            stdout='',
            stderr="Please run 'az login' to setup account."
        )
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(CredentialUnavailableError, match="Azure CLI not logged in"):
            credential.get_token('https://management.chinacloudapi.cn/.default')
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_invalid_json_response(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test error when Azure CLI returns invalid JSON."""
        mock_detect_cloud.return_value = 'global'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout='invalid json response',
            stderr=''
        )
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(ClientAuthenticationError, match="Failed to parse Azure CLI response"):
            credential.get_token('https://management.azure.com/.default')
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_missing_access_token(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test error when Azure CLI response is missing access token."""
        mock_detect_cloud.return_value = 'global'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout=json.dumps({'expiresOn': '2025-07-04T12:00:00.000000+00:00'}),
            stderr=''
        )
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(ClientAuthenticationError, match="Azure CLI did not return an access token"):
            credential.get_token('https://management.azure.com/.default')
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_timeout(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test timeout error when Azure CLI command takes too long."""
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        mock_subprocess.side_effect = subprocess.TimeoutExpired('az', 10)
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(CredentialUnavailableError, match="Azure CLI command timed out"):
            credential.get_token('https://management.chinacloudapi.cn/.default')
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_get_token_azure_cli_not_found(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test error when Azure CLI is not installed."""
        mock_detect_cloud.return_value = 'global'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['global']
        
        mock_subprocess.side_effect = FileNotFoundError()
        
        credential = CloudAwareAzureCliCredential()
        
        with pytest.raises(CredentialUnavailableError, match="Azure CLI is not installed"):
            credential.get_token('https://management.azure.com/.default')
    
    def test_close(self):
        """Test close method (no-op)."""
        with patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud'), \
             patch('providers.azure.lib.common.china_cli_credential.get_cloud_config'):
            credential = CloudAwareAzureCliCredential()
            # Should not raise any exception
            credential.close()


class TestBackwardCompatibility:
    """Test backward compatibility alias."""
    
    def test_china_azure_cli_credential_alias(self):
        """Test that ChinaAzureCliCredential is an alias for CloudAwareAzureCliCredential."""
        assert ChinaAzureCliCredential is CloudAwareAzureCliCredential
    
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    def test_china_azure_cli_credential_works(self, mock_get_cloud_config, mock_detect_cloud):
        """Test that the old class name still works."""
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        # Should work with the old class name
        credential = ChinaAzureCliCredential()
        assert isinstance(credential, CloudAwareAzureCliCredential)
        assert credential.cloud_env == 'china'


class TestScopeHandling:
    """Test different scope handling scenarios."""
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_custom_scope_with_default_suffix(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test handling of custom scopes with .default suffix."""
        mock_detect_cloud.return_value = 'government'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['government']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout=json.dumps({
                'accessToken': 'test_token',
                'expiresOn': '2025-07-04T12:00:00.000000+00:00'
            }),
            stderr=''
        )
        
        credential = CloudAwareAzureCliCredential()
        credential.get_token('https://custom.service.azure.us/.default')
        
        # Should use the custom scope without .default
        expected_cmd = [
            'az', 'account', 'get-access-token',
            '--output', 'json',
            '--resource', 'https://custom.service.azure.us'
        ]
        mock_subprocess.assert_called_once_with(
            expected_cmd,
            capture_output=True,
            text=True,
            timeout=10,
            check=False
        )
    
    @patch('providers.azure.lib.common.china_cli_credential.get_cloud_config')
    @patch('providers.azure.lib.common.china_cli_credential.detect_azure_cloud')
    @patch('subprocess.run')
    def test_fallback_to_management_endpoint(self, mock_subprocess, mock_detect_cloud, mock_get_cloud_config):
        """Test fallback to management endpoint for unknown scopes."""
        mock_detect_cloud.return_value = 'china'
        mock_get_cloud_config.return_value = CLOUD_ENVIRONMENTS['china']
        
        mock_subprocess.return_value = Mock(
            returncode=0,
            stdout=json.dumps({
                'accessToken': 'test_token',
                'expiresOn': '2025-07-04T12:00:00.000000+00:00'
            }),
            stderr=''
        )
        
        credential = CloudAwareAzureCliCredential()
        credential.get_token('unknown_scope')
        
        # Should fall back to management endpoint
        expected_cmd = [
            'az', 'account', 'get-access-token',
            '--output', 'json',
            '--resource', 'https://management.chinacloudapi.cn'
        ]
        mock_subprocess.assert_called_once_with(
            expected_cmd,
            capture_output=True,
            text=True,
            timeout=10,
            check=False
        )



if __name__ == '__main__':
    pytest.main([__file__])

# Contains AI-generated edits.
