import unittest
from unittest.mock import patch, MagicMock
import unittest
from unittest.mock import patch, MagicMock
from providers.azure.lib.user.user_operations import (
    get_entra_users_with_roles,
    get_entra_users_with_rbac_roles
)

class TestUserOperations(unittest.TestCase):

    @patch('providers.azure.lib.user.user_operations.get_entra_users_with_roles_impl')
    @patch('providers.azure.lib.user.user_operations.get_azure_credential')
    def test_get_entra_users_with_roles(self, mock_get_credential, mock_get_users_impl):
        """Test getting Entra users with their directory roles."""
        mock_get_credential.return_value = MagicMock()
        mock_get_users_impl.return_value = (True, [{'displayName': 'User 1'}], None)
        
        success, users, error = get_entra_users_with_roles()
        
        self.assertTrue(success)
        self.assertEqual(len(users), 1)
        self.assertIsNone(error)

    @patch('providers.azure.lib.user.user_operations.get_entra_users_with_rbac_roles_impl')
    @patch('providers.azure.lib.user.user_operations.get_azure_credential')
    def test_get_entra_users_with_rbac_roles(self, mock_get_credential, mock_get_users_impl):
        """Test getting Entra users with their RBAC roles."""
        mock_get_credential.return_value = MagicMock()
        mock_get_users_impl.return_value = (True, [{'displayName': 'User 1', 'rbac_roles': {}}], None)
        
        success, users, error = get_entra_users_with_rbac_roles()
        
        self.assertTrue(success)
        self.assertEqual(len(users), 1)
        self.assertIn('rbac_roles', users[0])
        self.assertIsNone(error)


if __name__ == '__main__':
    unittest.main()
