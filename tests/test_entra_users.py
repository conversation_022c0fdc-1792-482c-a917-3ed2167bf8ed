import unittest
from unittest.mock import patch, MagicMock
import unittest
from unittest.mock import patch, MagicMock
from providers.azure.lib.user.entra_users import get_entra_users_with_roles

class TestEntraUsers(unittest.TestCase):

    @patch('providers.azure.lib.user.entra_users.get_azure_credential')
    @patch('providers.azure.lib.user.entra_users.get_entra_users_with_roles_impl')
    def test_get_entra_users_with_roles(self, mock_get_users_impl, mock_get_credential):
        """Test retrieving all users with their roles."""
        mock_get_credential.return_value = MagicMock()
        mock_get_users_impl.return_value = (True, [{'displayName': 'User 1'}, {'displayName': 'User 2'}], None)
        
        success, users, error = get_entra_users_with_roles()
        
        self.assertTrue(success)
        self.assertEqual(len(users), 2)
        self.assertIsNone(error)
        mock_get_credential.assert_called_once()
        mock_get_users_impl.assert_called_once()


if __name__ == '__main__':
    unittest.main()
