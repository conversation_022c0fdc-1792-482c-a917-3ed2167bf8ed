#!/usr/bin/env python3
"""
Test cases for Alibaba Resource Directory Account CLI functionality.
"""

import unittest
import sys
from unittest.mock import patch, MagicMock
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Add the project root to Python path for imports
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import cloudqx

runner = CliRunner()


class TestAlibabaResourceAccount(unittest.TestCase):
    """Test cases for Alibaba Resource Directory Account CLI functionality."""

    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_basic(self, mock_run):
        """Test basic resourceaccount command."""
        mock_run.return_value = MagicMock(returncode=0)
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount'])
        assert result.exit_code == 0
        
        cmd = mock_run.call_args[0][0]
        assert sys.executable in cmd
        assert '-m' in cmd
        assert 'providers.alibaba.resourceaccount' in cmd

    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_with_debug(self, mock_run):
        """Test resourceaccount command with debug flag."""
        mock_run.return_value = MagicMock(returncode=0)
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount', '--debug'])
        assert result.exit_code == 0
        
        cmd = mock_run.call_args[0][0]
        assert '--debug' in cmd

    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_with_debug_short(self, mock_run):
        """Test resourceaccount command with debug short flag."""
        mock_run.return_value = MagicMock(returncode=0)
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount', '-d'])
        assert result.exit_code == 0
        
        cmd = mock_run.call_args[0][0]
        assert '--debug' in cmd





    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_subprocess_error(self, mock_run):
        """Test resourceaccount command when subprocess returns error."""
        mock_run.return_value = MagicMock(returncode=1)
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount'])
        assert result.exit_code == 1

    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_keyboard_interrupt(self, mock_run):
        """Test resourceaccount command with keyboard interrupt."""
        mock_run.side_effect = KeyboardInterrupt()
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount'])
        assert result.exit_code == 1
        assert "Operation cancelled by user" in result.stdout

    @patch('cloudqx.subprocess.run')
    def test_resourceaccount_exception(self, mock_run):
        """Test resourceaccount command with general exception."""
        mock_run.side_effect = Exception("Test error")
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount'])
        assert result.exit_code == 1
        assert "Error: Test error" in result.stdout

    def test_resourceaccount_help(self):
        """Test resourceaccount command help."""
        result = runner.invoke(cloudqx.app, ['alibaba', 'resourceaccount', '--help'])
        assert result.exit_code == 0
        assert 'List all accounts in Alibaba Resource Directory' in result.stdout
        assert '--debug' in result.stdout

    def test_alibaba_help_includes_resourceaccount(self):
        """Test that alibaba help includes resourceaccount command."""
        result = runner.invoke(cloudqx.app, ['alibaba', '--help'])
        assert result.exit_code == 0
        assert 'resourceaccount' in result.stdout


if __name__ == '__main__':
    unittest.main()
