import sys
import unittest
from unittest.mock import patch, MagicMock

from providers.azure.__main__ import main

class TestProvidersAzureMain(unittest.TestCase):
    def setUp(self):
        self.orig_argv = sys.argv.copy()
    def tearDown(self):
        sys.argv = self.orig_argv

    @patch('providers.azure.organization.main')
    def test_default_module_runs_organization(self, mock_org_main):
        sys.argv = ['prog']
        main()
        mock_org_main.assert_called_once()

    @patch('providers.azure.user.main')
    def test_user_module(self, mock_user_main):
        sys.argv = ['prog', 'user']
        main()
        mock_user_main.assert_called_once()

    @patch('providers.azure.vnet.main')
    def test_vnet_module(self, mock_vnet_main):
        sys.argv = ['prog', 'vnet']
        main()
        mock_vnet_main.assert_called_once()

    @patch('providers.azure.organization.main')
    @patch('sys.argv', ['prog', 'organization'])
    def test_explicit_organization_arg(self, mock_org_main):
        main()
        mock_org_main.assert_called_once()

if __name__ == '__main__':
    unittest.main()