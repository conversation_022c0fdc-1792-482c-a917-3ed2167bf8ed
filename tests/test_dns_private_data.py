import pytest

import providers.azure.lib.dns_private.data as dpdata

class DummyToken:
    def __init__(self, token=None):
        self.token = token

@pytest.fixture(autouse=True)
def patch_auth(monkeypatch):
    # Patch get_current_cloud_info and get_cached_token
    monkeypatch.setattr(
        'providers.azure.lib.common.config.get_current_cloud_info',
        lambda: (None, {'management_scope': 'scope', 'management_endpoint': 'https://management.azure.com'})
    )
    monkeypatch.setattr(
        'providers.azure.lib.common.token_manager.get_cached_token',
        lambda cred, scope: DummyToken()  # empty token
    )
    return None

@pytest.mark.parametrize("func", [
    dpdata.list_private_dns_zones,
    dpdata.list_virtual_network_links,
    dpdata.list_private_endpoints,
    dpdata.list_private_resolvers,
    dpdata.list_dns_forwarding_rulesets,
    dpdata.list_forwarding_rules,
])
def test_list_functions_empty(monkeypatch, func):
    # With no valid token, all list functions should return empty lists
    result = func(credential=None, subscription_id='subid')
    assert isinstance(result, list)
    assert result == []

def test_build_resolver_hierarchy():
    """Test the build_resolver_hierarchy function with sample data."""
    # Sample test data
    resolvers = [
        {
            'name': 'test-resolver',
            'resource_guid': 'test-guid',
            'location': 'eastus',
            'subscription': 'sub1',
            'resource_group': 'rg1'
        }
    ]
    
    rulesets = [
        {
            'name': 'ruleset-test-resolver',
            'location': 'eastus',
            'subscription': 'sub1',
            'resource_group': 'rg1',
            'outbound_endpoints': [
                {
                    'id': '/subscriptions/sub1/resourceGroups/rg1/providers/Microsoft.Network/dnsResolvers/test-resolver/outboundEndpoints/ep1'
                }
            ]
        },
        {
            'name': 'standalone-ruleset',
            'location': 'westus',
            'subscription': 'sub2',
            'resource_group': 'rg2',
            'outbound_endpoints': []
        }
    ]
    
    rules = [
        {
            'name': 'rule1',
            'ruleset_name': 'ruleset-test-resolver',
            'domain': 'example.com'
        },
        {
            'name': 'rule2',
            'ruleset_name': 'standalone-ruleset',
            'domain': 'test.com'
        }
    ]
    
    result = dpdata.build_resolver_hierarchy(resolvers, rulesets, rules)
    
    # Verify structure
    assert isinstance(result, list)
    assert len(result) == 2  # One resolver entry + one standalone
    
    # Find resolver entry
    resolver_entry = next((entry for entry in result if entry['resolver'] is not None), None)
    assert resolver_entry is not None
    assert resolver_entry['resolver']['name'] == 'test-resolver'
    assert len(resolver_entry['rulesets']) == 1
    assert resolver_entry['rulesets'][0]['ruleset']['name'] == 'ruleset-test-resolver'
    assert len(resolver_entry['rulesets'][0]['rules']) == 1
    assert resolver_entry['rulesets'][0]['rules'][0]['name'] == 'rule1'
    
    # Find standalone entry
    standalone_entry = next((entry for entry in result if entry['resolver'] is None), None)
    assert standalone_entry is not None
    assert len(standalone_entry['rulesets']) == 1
    assert standalone_entry['rulesets'][0]['ruleset']['name'] == 'standalone-ruleset'
    assert len(standalone_entry['rulesets'][0]['rules']) == 1
    assert standalone_entry['rulesets'][0]['rules'][0]['name'] == 'rule2'

def test_build_resolver_hierarchy_empty():
    """Test build_resolver_hierarchy with empty data."""
    result = dpdata.build_resolver_hierarchy([], [], [])
    assert isinstance(result, list)
    assert len(result) == 0