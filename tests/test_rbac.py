import unittest
from unittest.mock import patch, MagicMock
import unittest
from unittest.mock import patch, MagicMock
from providers.azure.lib.user.rbac import get_user_rbac_roles

class TestRbac(unittest.TestCase):

    @patch('providers.azure.lib.user.rbac.get_user_rbac_roles_async')
    def test_get_user_rbac_roles(self, mock_get_user_rbac_roles_async):
        """Test retrieving RBAC roles for a user."""
        mock_get_user_rbac_roles_async.return_value = {
            'subscriptions': {'sub1': ['Reader']}
        }
        
        roles = get_user_rbac_roles(MagicMock(), 'user123', [{'id': 'sub1'}], [])
        
        self.assertIn('sub1', roles['subscriptions'])
        self.assertEqual(roles['subscriptions']['sub1'], ['Reader'])


if __name__ == '__main__':
    unittest.main()
