import unittest
from unittest.mock import patch, mock_open
import unittest
from unittest.mock import patch, mock_open
from providers.azure.lib.vnet.html_report import generate_vnet_html_report

class TestHtmlReport(unittest.TestCase):

    @patch('builtins.open', new_callable=mock_open)
    def test_generate_vnet_html_report(self, mock_file):
        """Test generating a VNet HTML report."""
        vnets_data = [{'name': 'TestVNet', 'subnets': [], 'subscription_name': 'sub1', 'location': 'eastus', 'address_spaces': [], 'tags': {}, 'provisioning_state': 'Succeeded', 'resource_group': 'rg1', 'subnet_count': 0}]
        
        generate_vnet_html_report(vnets_data, 'vnet_report.html')
        
        self.assertTrue(mock_file.called)
        mock_file().write.assert_called()
        self.assertIn('TestVNet', mock_file().write.call_args[0][0])


if __name__ == '__main__':
    unittest.main()
