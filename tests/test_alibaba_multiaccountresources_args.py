#!/usr/bin/env python3
"""
Test cases for Alibaba Multi-Account Resources argument parsing
"""

import sys
import unittest
from unittest.mock import patch

from providers.alibaba.multiaccountresources import parse_args

class TestAlibabaMultiAccountResourcesArgs(unittest.TestCase):
    """Test cases for Alibaba Multi-Account Resources argument parsing."""

    @patch.object(sys, 'argv', ['prog'])
    def test_default_arguments(self):
        """Test default argument values."""
        args = parse_args()
        self.assertFalse(args.debug)
        self.assertIsNone(args.region)
        self.assertIsNone(args.account_id)
        self.assertIsNone(args.resource_type)

    @patch.object(sys, 'argv', ['prog', '--debug'])
    def test_debug_flag(self):
        """Test debug flag."""
        args = parse_args()
        self.assertTrue(args.debug)
        self.assertIsNone(args.region)
        self.assertIsNone(args.account_id)
        self.assertIsNone(args.resource_type)

    @patch.object(sys, 'argv', ['prog', '-d'])
    def test_debug_short_flag(self):
        """Test debug short flag."""
        args = parse_args()
        self.assertTrue(args.debug)

    @patch.object(sys, 'argv', ['prog', '--region', 'cn-beijing'])
    def test_region_flag(self):
        """Test region flag."""
        args = parse_args()
        self.assertFalse(args.debug)
        self.assertEqual(args.region, 'cn-beijing')
        self.assertIsNone(args.account_id)
        self.assertIsNone(args.resource_type)

    @patch.object(sys, 'argv', ['prog', '-r', 'cn-shanghai'])
    def test_region_short_flag(self):
        """Test region short flag."""
        args = parse_args()
        self.assertEqual(args.region, 'cn-shanghai')

    @patch.object(sys, 'argv', ['prog', '--account-id', 'test-account'])
    def test_account_flag(self):
        """Test account flag."""
        args = parse_args()
        self.assertFalse(args.debug)
        self.assertIsNone(args.region)
        self.assertEqual(args.account_id, 'test-account')
        self.assertIsNone(args.resource_type)

    @patch.object(sys, 'argv', ['prog', '--account-id', 'blz-p-ow-game'])
    def test_account_short_flag(self):
        """Test account short flag."""
        args = parse_args()
        self.assertEqual(args.account_id, 'blz-p-ow-game')

    @patch.object(sys, 'argv', ['prog', '--resource-type', 'ECS::Instance'])
    def test_resource_type_flag(self):
        """Test resource type flag."""
        args = parse_args()
        self.assertFalse(args.debug)
        self.assertIsNone(args.region)
        self.assertIsNone(args.account_id)
        self.assertEqual(args.resource_type, 'ECS::Instance')

    @patch.object(sys, 'argv', ['prog', '-t', 'RDS::Database'])
    def test_resource_type_short_flag(self):
        """Test resource type short flag."""
        args = parse_args()
        self.assertEqual(args.resource_type, 'RDS::Database')

    @patch.object(sys, 'argv', ['prog', '-d', '-r', 'cn-beijing', '--account-id', 'test-account', '-t', 'ECS::Instance'])
    def test_all_flags(self):
        """Test all flags together."""
        args = parse_args()
        self.assertTrue(args.debug)
        self.assertEqual(args.region, 'cn-beijing')
        self.assertEqual(args.account_id, 'test-account')
        self.assertEqual(args.resource_type, 'ECS::Instance')

    @patch.object(sys, 'argv', ['prog', '--debug', '--region', 'cn-hangzhou', '--account-id', 'blz-p-diablo4-prod', '--resource-type', 'OSS::Bucket'])
    def test_all_long_flags(self):
        """Test all long flags together."""
        args = parse_args()
        self.assertTrue(args.debug)
        self.assertEqual(args.region, 'cn-hangzhou')
        self.assertEqual(args.account_id, 'blz-p-diablo4-prod')
        self.assertEqual(args.resource_type, 'OSS::Bucket')

if __name__ == '__main__':
    unittest.main()
