# CloudQX Requirements
# Multi-Cloud Infrastructure Management Tool

# CLI Framework
typer[all]>=0.16.0

# Azure SDK (for Azure provider)
azure-identity>=1.15.0
azure-mgmt-authorization>=4.0.0
azure-mgmt-resource>=23.0.0
azure-mgmt-subscription>=3.1.1
azure-mgmt-managementgroups
azure-mgmt-network>=25.0.0
azure-mgmt-dnsresolver>=1.0.0
azure-mgmt-resourcegraph>=8.0.0

# Microsoft Graph SDK - New Pattern
msgraph-core>=1.0.0
azure-identity>=1.15.0
kiota-abstractions

# HTTP client for API calls
httpx>=0.25.0
aiohttp>=3.9.0

# Data processing and display
tabulate>=0.9.0

# Rich console output (included with typer[all])
rich>=10.11.0

# Development dependencies (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=5.0.0
# mypy>=1.0.0

# Alibaba Cloud SDK (for Alibaba provider)
alibabacloud_resourcecenter20221201
alibabacloud_credentials
alibabacloud_tea_openapi
alibabacloud_tea_util
