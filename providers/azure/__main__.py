#!/usr/bin/env python3
"""
Entry point for running Azure provider modules.
Supports: organization, user, vnet
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Main entry point that routes to the appropriate module based on arguments."""
    import argparse
    
    # Check if we have enough arguments and if the first arg is a module name
    if len(sys.argv) > 1 and sys.argv[1] in ['organization', 'user', 'vnet', 'dns-private', 'resource-overview']:
        # Module specified, remove it from sys.argv and run that module
        module_name = sys.argv[1]
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        # Import and run the appropriate module
        if module_name == 'organization':
            from providers.azure.organization import main
            main()
        elif module_name == 'user':
            from providers.azure.user import main
            main()
        elif module_name == 'vnet':
            from providers.azure.vnet import main
            main()
        elif module_name == 'dns-private':
            from providers.azure.dns_private import main
            main()
        elif module_name == 'resource-overview':
            from providers.azure.resource_overview import main
            main()
    else:
        # No module specified or help requested, show our help
        parser = argparse.ArgumentParser(
            description="Azure Provider - Multi-module support",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Available modules:
  organization      - Azure organization hierarchy and resource analysis
  user              - Azure user and RBAC role analysis
  vnet              - Azure Virtual Network and subnet analysis
  dns-private       - Azure Private DNS and related resources analysis
  resource-overview - Comprehensive overview of all Azure resources

Examples:
  python -m providers.azure organization       # Run organization module
  python -m providers.azure user               # Run user module  
  python -m providers.azure vnet               # Run vnet module
  python -m providers.azure dns-private        # Run DNS private module
  python -m providers.azure resource-overview  # Run resource overview module  
  python -m providers.azure vnet            # Run vnet module
  python -m providers.azure                 # Run organization module (default)
            """
        )
        
        parser.add_argument(
            'module',
            nargs='?',
            default='organization',
            choices=['organization', 'user', 'vnet', 'dns-private', 'resource-overview'],
            help='Module to run (default: organization)'
        )
        
        # Parse arguments
        args = parser.parse_args()
        
        # Run the default module (organization)
        if args.module == 'organization':
            from providers.azure.organization import main
            main()
        elif args.module == 'user':
            from providers.azure.user import main
            main()
        elif args.module == 'vnet':
            from providers.azure.vnet import main
            main()
        elif args.module == 'dns-private':
            from providers.azure.dns_private import main
            main()
        elif args.module == 'resource-overview':
            from providers.azure.resource_overview import main
            main()

if __name__ == "__main__":
    main()

# Contains AI-generated edits.
