#!/usr/bin/env python3
"""
Azure User Management Tool - Simplified Main Module

This module provides a streamlined Azure user management tool that generates
HTML reports for Entra ID users and RBAC role management for China Azure.

By default, the tool:
- Retrieves both Entra ID users and RBAC roles
- Generates an HTML report
- Opens the report in the default browser

The functionality is modularized in the lib directory:
- lib/auth.py: Authentication and credential management
- lib/entra_users.py: Entra ID user operations
- lib/rbac.py: RBAC role management
- lib/azure_resources.py: Subscription and resource group operations
- lib/display.py: User data display and formatting
- lib/export.py: Data export functionality
- lib/html_export.py: HTML report generation and browser launching
- lib/config.py: Configuration and logging setup
- lib/user_operations.py: Combined user and RBAC operations
"""

import logging
import sys
import os
import time
import traceback
import argparse

from providers.azure.lib.common.config import setup_logging, print_detailed_performance_summary, get_current_cloud_info
from providers.azure.lib.common.auth import check_authentication, get_azure_credential
from providers.azure.lib.user.entra_users import get_entra_users_with_roles
from providers.azure.lib.user.user_operations import get_entra_users_with_rbac_roles
from providers.azure.lib.user.display import display_users_with_roles, debug_user_data
from providers.azure.lib.user.export import export_users_rbac_to_json
from providers.azure.lib.user.html_export import generate_html_report


logger = logging.getLogger(__name__)


def parse_user_arguments():
    """Parse command line arguments for User module."""
    parser = argparse.ArgumentParser(
        description="Azure User Management Tool - Generates HTML report by default (works with Global and China Azure)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m providers.azure user                   # Generate HTML report and open in browser (auto-detect cloud)
  python -m providers.azure user --debug           # Generate HTML report with debug logging
  python -m providers.azure user --cloud global    # Force use of Global Azure
  python -m providers.azure user --cloud china     # Force use of China Azure
        """
    )
    
    # Optional flags
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='Enable debug logging to show detailed debugging information'
    )
    
    parser.add_argument(
        '--cloud', '-c',
        choices=['global', 'china'],
        help='Specify Azure cloud environment (auto-detects if not specified)'
    )
    
    parser.add_argument(
        '--web', '-w',
        action='store_true',
        help='Launch HTTP server and open HTML report in web browser (default: off)'
    )
    return parser.parse_args()


def main():
    """
    Main function to orchestrate Azure user management operations.
    By default, generates HTML report and opens in browser.
    """
    args = None
    try:
        # Setup logging and parse arguments
        args = parse_user_arguments()
        setup_logging(args.debug)
        
        # Get cloud environment information - use forced cloud if specified
        if args.cloud:
            from providers.azure.lib.common.config import get_cloud_config
            cloud_config = get_cloud_config(args.cloud)
            detected_cloud = args.cloud
            logger.info(f"🌏 Using forced Azure Cloud: {cloud_config['name']}")
        else:
            detected_cloud, cloud_config = get_current_cloud_info()
            logger.info(f"🌏 Detected Azure Cloud: {cloud_config['name']}")
        
        logger.info(f"🚀 Starting Azure User Management Tool ({cloud_config['name']})")
        logger.info("=" * 60)
        
        # Test authentication first using a single credential instance
        logger.info("🔐 Testing Azure authentication...")
        credential = get_azure_credential(detected_cloud)
        auth_success = check_authentication(detected_cloud, credential)
        
        if not auth_success:
            logger.error("❌ Authentication failed. Please check your Azure CLI setup.")
            sys.exit(1)
        
        logger.info("✅ Authentication successful!")
        
        # Start timing for performance tracking
        start_time = time.time()
        
        # Default operation: Get users with both Entra ID roles and RBAC roles
        logger.info("🔑 Retrieving users with both Entra ID and RBAC roles...")
        success, users_data, error_msg = get_entra_users_with_rbac_roles(credential)
        operation_name = "HTML Report Generation (Entra ID + RBAC)"
        
        if not success:
            logger.error(f"❌ Failed to retrieve user data: {error_msg}")
            sys.exit(1)
        
        # End timing
        end_time = time.time()
        
        # Debug output if requested
        if args.debug:
            debug_user_data(users_data)
        
        # Generate HTML report (optionally launch web server and browser)
        cloud_region = detected_cloud.lower() if detected_cloud else 'global'
        logger.info(f"🌐 Generating HTML report in html/ directory...")
        try:
            report_path = generate_html_report(
                users_data,
                "azure_user_report.html",
                cloud_region=cloud_region,
                use_web_server=(args.web is True),
                keep_server_alive=(args.web is True)
            )
            if args.web:
                logger.info(f"✅ HTML report generated and opened in browser!")
                print(f"\n📄 HTML Report: {report_path}")
                print("🌐 The report should open automatically in your default browser")
            else:
                logger.info(f"✅ HTML report generated (no browser launched).")
                print(f"\n📄 HTML Report: {report_path}")
                print("ℹ️  To view the report, open the above HTML file in your browser or rerun with -w/--web to launch the web server.")
        except Exception as e:
            logger.error(f"❌ Failed to generate HTML report: {str(e)}")
            if args.debug:
                import traceback
                logger.error(f"HTML generation traceback:\n{traceback.format_exc()}")
        
        # Print performance summary
        total_users = len(users_data) if users_data else 0
        print_detailed_performance_summary(start_time, end_time, total_users, operation_name)
        
        logger.info("🎉 Azure User Management Tool completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        if args and args.debug:
            import traceback
            logger.error(f"Full traceback:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()

# Contains AI-generated edits.