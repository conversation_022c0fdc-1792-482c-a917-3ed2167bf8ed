#!/usr/bin/env python3
"""
Azure Resource Overview reporting tool.
Generates comprehensive hierarchical views of all Azure resources across subscriptions.
"""
import sys
import os
import logging
import traceback
import webbrowser
from pathlib import Path
import argparse

from providers.azure.lib.common.config import setup_logging, get_current_cloud_info
from providers.azure.lib.common.auth import check_authentication, get_azure_credential
from providers.azure.lib.common.azure_resources import get_subscriptions
from providers.azure.lib.common.web_server import find_available_port, start_web_server
from providers.azure.lib.resource_overview.data import get_all_resources, calculate_overview_statistics
from providers.azure.lib.resource_overview.html_report import generate_resource_overview_html_report

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Azure Resource Overview - Comprehensive resource inventory across subscriptions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m providers.azure.resource_overview                    # Generate report (auto-detect cloud)
  python -m providers.azure.resource_overview --debug           # Generate with debug logging  
  python -m providers.azure.resource_overview --cloud global    # Force use of Global Azure
  python -m providers.azure.resource_overview --web             # Launch web server and open in browser
        """
    )
    
    parser.add_argument(
        "--debug", "-d",
        action="store_true",
        help="Enable debug logging to show detailed debugging information"
    )
    
    parser.add_argument(
        "--cloud", "-c",
        choices=["global", "china"],
        help="Specify Azure cloud environment (auto-detects if not specified)"
    )
    
    parser.add_argument(
        "--web", "-w",
        action="store_true",
        help="Launch HTTP server and open HTML report in web browser (default: off)"
    )
    
    return parser.parse_args()


def main():
    """Main entry point for Azure Resource Overview."""
    args = None
    try:
        args = parse_args()
        setup_logging(args.debug)

        if args.cloud:
            detected_cloud = args.cloud
            _, cloud_config = get_current_cloud_info()
        else:
            detected_cloud, cloud_config = get_current_cloud_info()
        logger.info(f"🌏 Azure Cloud: {cloud_config['name']}")

        logger.info("🔐 Testing Azure authentication...")
        credential = get_azure_credential(detected_cloud)
        if not check_authentication(detected_cloud, credential):
            logger.error("❌ Authentication failed")
            sys.exit(1)
        logger.info("✅ Authentication successful")

        logger.info("🔍 Retrieving accessible subscriptions...")
        subscriptions = get_subscriptions(credential)
        if not subscriptions:
            logger.warning("No subscriptions found")
            sys.exit(0)

        logger.info(f"📊 Found {len(subscriptions)} subscription(s)")

        # Retrieve all resources using Azure Resource Graph
        logger.info("📡 Retrieving all Azure resources...")
        try:
            hierarchical_data = get_all_resources(credential, detected_cloud)
            logger.info(f"✅ Successfully retrieved resources from all subscriptions")
        except Exception as e:
            logger.error(f"❌ Error retrieving resources: {e}")
            if args and args.debug:
                logger.debug(f"Traceback: {traceback.format_exc()}")
            sys.exit(1)

        # Generate HTML report
        logger.info("📄 Generating HTML report...")
        try:
            # Calculate statistics
            stats = calculate_overview_statistics(hierarchical_data)
            output_file = generate_resource_overview_html_report(hierarchical_data, stats, cloud_region=detected_cloud)
            abs_path = os.path.abspath(output_file)
            logger.info(f"✅ Report generated successfully")
        except Exception as e:
            logger.error(f"❌ Error generating HTML report: {e}")
            if args and args.debug:
                logger.debug(f"Traceback: {traceback.format_exc()}")
            sys.exit(1)

        # Launch web server if requested
        if args.web:
            try:
                port = find_available_port()
                thread = start_web_server(output_file, port, daemon=False)
                
                # Get the actual port (in case it was auto-assigned)
                actual_port = getattr(thread, 'actual_port', port)
                url = f"http://127.0.0.1:{actual_port}/{os.path.basename(output_file)}"
                logger.info(f"🌐 Report served at: {url}")
                
                try:
                    webbrowser.open(url)
                    logger.info("🚀 Opening report in web browser...")
                except Exception as e:
                    logger.warning(f"Could not automatically open web browser: {e}")
                    print(f"🌐 Please open this URL in your browser: {url}")
                
                print(f"💡 Server is running. Press Ctrl+C to stop.")
                try:
                    thread.join()
                except Exception as e:
                    logger.warning(f"Web server thread error: {e}")
            except Exception as e:
                logger.error(f"❌ Error starting web server: {e}")
                if args and args.debug:
                    logger.debug(f"Traceback: {traceback.format_exc()}")
        else:
            logger.info(f"📁 Report available at: {abs_path}")

    except KeyboardInterrupt:
        logger.info("🛑 Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        if args and args.debug:
            logger.debug(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == '__main__':
    main()

# Contains AI-generated edits.
