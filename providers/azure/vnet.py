#!/usr/bin/env python3
"""
Azure Virtual Network Management Tool

This module provides Azure Virtual Network (VNet) management tool that generates
HTML reports for VNets and their associated subnets for China Azure.

By default, the tool:
- Retrieves all VNets across accessible subscriptions
- Gets subnet information for each VNet
- Generates an HTML report with interactive table
- Serves the report via light HTTP server

The functionality is modularized in the lib directory:
- lib/auth.py: Authentication and credential management
- lib/azure_resources.py: Subscription and resource group operations
- lib/config.py: Configuration and logging setup
- lib/web_server.py: Web server for serving HTML reports
"""

import logging
import sys
import os
import time
import traceback
import webbrowser
import httpx
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Add the project root to Python path for imports
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from providers.azure.lib.common.config import setup_logging, print_detailed_performance_summary, get_current_cloud_info
from providers.azure.lib.common.auth import check_authentication, get_azure_credential
from providers.azure.lib.common.azure_resources import get_subscriptions
from providers.azure.lib.common.web_server import find_available_port, start_web_server
from providers.azure.lib.common.token_manager import get_cached_token
from providers.azure.lib.vnet import generate_vnet_html_report


logger = logging.getLogger(__name__)

# Thread-safe data collection
vnets_data_lock = Lock()


def get_ssl_verify_setting(cloud_config):
    """
    Determine SSL verification setting based on cloud environment.
    
    Args:
        cloud_config: Cloud configuration dictionary
        
    Returns:
        bool: SSL verification setting
    """
    # For China Azure, we often need to handle self-signed certificates
    # Check environment variable for override
    ssl_verify_env = os.getenv('AZURE_SSL_VERIFY', '').lower()
    if ssl_verify_env in ['false', '0', 'no']:
        logger.warning("🔒 SSL verification disabled via AZURE_SSL_VERIFY environment variable")
        return False
    elif ssl_verify_env in ['true', '1', 'yes']:
        return True
    
    # Auto-detect based on cloud environment
    cloud_name = cloud_config.get('name', '').lower()
    if 'china' in cloud_name:
        logger.debug("🔒 Using relaxed SSL verification for China Azure environment")
        return False
    
    # Default to strict SSL verification for Global Azure
    return True


def parse_vnet_arguments():
    """Parse command line arguments for VNet module."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Azure Virtual Network Analysis Tool - Generates HTML report by default (works with Global and China Azure)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m providers.azure vnet                     # Generate VNet HTML report and open in browser
  python -m providers.azure vnet --debug             # Generate VNet report with debug logging
  python -m providers.azure vnet --cloud global      # Force use of Global Azure
  python -m providers.azure vnet --cloud china       # Force use of China Azure
  python -m providers.azure vnet --no-ssl-verify     # Disable SSL verification (for China Azure with certificate issues)
        """
    )
    
    # Optional flags
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='Enable debug logging to show detailed debugging information'
    )
    parser.add_argument(
        '--cloud', '-c',
        choices=['global', 'china'],
        help='Specify Azure cloud environment (auto-detects if not specified)'
    )
    parser.add_argument(
        '--no-ssl-verify',
        action='store_true',
        help='Disable SSL certificate verification (use with caution, primarily for China Azure with self-signed certificates)'
    )
    parser.add_argument(
        '--web', '-w',
        action='store_true',
        help='Launch HTTP server and open HTML report in web browser (default: off)'
    )
    return parser.parse_args()


def process_subscription_vnets(subscription, headers, cloud_config, ssl_verify=True):
    """
    Process VNets for a single subscription in parallel.
    
    Args:
        subscription: Subscription information
        headers: Authentication headers
        cloud_config: Cloud configuration
        ssl_verify: SSL verification setting
        
    Returns:
        List of VNet dictionaries for this subscription
    """
    subscription_vnets = []
    subscription_id = subscription.get('subscriptionId')
    subscription_name = subscription.get('displayName', 'Unknown')
    
    logger.info(f"📋 Processing subscription: {subscription_name}")
    
    try:
        # Use the provided SSL verification setting
        if not ssl_verify:
            logger.debug(f"🔒 Using relaxed SSL verification for subscription {subscription_name}")
        
        with httpx.Client(base_url=cloud_config['management_endpoint'], verify=ssl_verify, timeout=30) as client:
            # Get VNets for this subscription
            vnets_endpoint = f"/subscriptions/{subscription_id}/providers/Microsoft.Network/virtualNetworks?api-version=2023-09-01"
            
            vnets_response = client.get(vnets_endpoint, headers=headers)
            
            if vnets_response.status_code == 200:
                vnets_data_raw = vnets_response.json()
                vnets = vnets_data_raw.get('value', [])
                
                logger.info(f"🔍 Found {len(vnets)} VNets in subscription {subscription_name}")
                
                # Process VNets in parallel for this subscription
                with ThreadPoolExecutor(max_workers=5) as vnet_executor:
                    vnet_futures = []
                    
                    for vnet in vnets:
                        future = vnet_executor.submit(
                            process_single_vnet,
                            vnet, subscription_name, subscription_id, headers, cloud_config, ssl_verify
                        )
                        vnet_futures.append(future)
                    
                    # Collect results
                    for future in as_completed(vnet_futures):
                        try:
                            vnet_info = future.result()
                            if vnet_info:
                                subscription_vnets.append(vnet_info)
                        except Exception as e:
                            logger.error(f"❌ Error processing VNet in subscription {subscription_name}: {str(e)}")
            
            elif vnets_response.status_code == 403:
                logger.warning(f"⚠️ Access denied for subscription {subscription_name} - insufficient permissions")
            else:
                logger.error(f"❌ Failed to get VNets for subscription {subscription_name}: {vnets_response.status_code}")
                
    except Exception as e:
        logger.error(f"❌ Error processing subscription {subscription_name}: {str(e)}")
    
    return subscription_vnets


def process_single_vnet(vnet, subscription_name, subscription_id, headers, cloud_config, ssl_verify=True):
    """
    Process a single VNet and its subnets.
    
    Args:
        vnet: VNet data from Azure API
        subscription_name: Name of the subscription
        subscription_id: ID of the subscription
        headers: Authentication headers
        cloud_config: Cloud configuration
        ssl_verify: SSL verification setting
        
    Returns:
        Dictionary with processed VNet information
    """
    try:
        vnet_info = process_vnet_data(vnet, subscription_name, subscription_id)
        
        # Get subnets for this VNet using a new client connection
        with httpx.Client(base_url=cloud_config['management_endpoint'], verify=ssl_verify, timeout=30) as client:
            subnets = get_vnet_subnets(client, headers, subscription_id, vnet_info['resource_group'], vnet_info['name'])
            vnet_info['subnets'] = subnets
            vnet_info['subnet_count'] = len(subnets)
        
        return vnet_info
    
    except Exception as e:
        logger.error(f"❌ Error processing VNet {vnet.get('name', 'Unknown')}: {str(e)}")
        return None


def get_vnets_and_subnets(credential, args=None) -> list:
    """
    Get all VNets and their subnets across all accessible subscriptions using parallel processing.
    
    Args:
        credential: Azure credential object
        args: Command line arguments containing SSL settings
        
    Returns:
        List of VNet dictionaries with subnet information
    """
    vnets_data = []
    
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        logger.info(f"🌐 Retrieving VNets from {cloud_config['name']} using parallel processing...")
        
        # Determine SSL verification setting
        ssl_verify = get_ssl_verify_setting(cloud_config)
        if args and args.no_ssl_verify:
            ssl_verify = False
            logger.warning("🔒 SSL verification disabled via command line argument")
        
        logger.info(f"🔒 SSL verification: {'Enabled' if ssl_verify else 'Disabled'}")
        
        # Get access token
        token = get_cached_token(credential, cloud_config['management_scope'])
        if not token or not token.token:
            logger.error("❌ Failed to get access token for Azure Management")
            return []
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        # Get all subscriptions
        subscriptions = get_subscriptions(credential)
        if not subscriptions:
            logger.warning("⚠️ No subscriptions found")
            return []
        
        logger.info(f"📋 Processing {len(subscriptions)} subscriptions in parallel...")
        
        # Process subscriptions in parallel
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Submit tasks for each subscription
            future_to_subscription = {}
            for subscription in subscriptions:
                future = executor.submit(process_subscription_vnets, subscription, headers, cloud_config, ssl_verify)
                future_to_subscription[future] = subscription
            
            # Collect results as they complete
            for future in as_completed(future_to_subscription):
                subscription = future_to_subscription[future]
                subscription_name = subscription.get('displayName', 'Unknown')
                
                try:
                    subscription_vnets = future.result()
                    if subscription_vnets:
                        with vnets_data_lock:
                            vnets_data.extend(subscription_vnets)
                        logger.info(f"✅ Collected {len(subscription_vnets)} VNets from subscription {subscription_name}")
                    else:
                        logger.info(f"ℹ️ No VNets found in subscription {subscription_name}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing subscription {subscription_name}: {str(e)}")
    
    except Exception as e:
        logger.error(f"❌ Error retrieving VNets: {str(e)}")
        logger.debug(traceback.format_exc())
    
    logger.info(f"✅ Retrieved {len(vnets_data)} VNets total using parallel processing")
    return vnets_data


def process_vnet_data(vnet, subscription_name, subscription_id):
    """
    Process and extract relevant VNet information.
    
    Args:
        vnet: Raw VNet data from Azure API
        subscription_name: Name of the subscription
        subscription_id: ID of the subscription
        
    Returns:
        Dictionary with processed VNet information
    """
    properties = vnet.get('properties', {})
    
    # Extract resource group from resource ID
    resource_id = vnet.get('id', '')
    resource_group = ''
    if '/resourceGroups/' in resource_id:
        resource_group = resource_id.split('/resourceGroups/')[1].split('/')[0]
    
    # Extract address spaces
    address_spaces = []
    if 'addressSpace' in properties and 'addressPrefixes' in properties['addressSpace']:
        address_spaces = properties['addressSpace']['addressPrefixes']
    
    # Extract DHCP options
    dhcp_options = properties.get('dhcpOptions', {})
    dns_servers = dhcp_options.get('dnsServers', [])
    
    return {
        'id': vnet.get('id', ''),
        'name': vnet.get('name', ''),
        'location': vnet.get('location', ''),
        'resource_group': resource_group,
        'subscription_name': subscription_name,
        'subscription_id': subscription_id,
        'address_spaces': address_spaces,
        'dns_servers': dns_servers,
        'provisioning_state': properties.get('provisioningState', ''),
        'tags': vnet.get('tags', {}),
        'type': vnet.get('type', ''),
        'subnets': [],  # Will be populated later
        'subnet_count': 0  # Will be calculated later
    }


def get_vnet_subnets(client, headers, subscription_id, resource_group, vnet_name):
    """
    Get subnets for a specific VNet with NSG rules.
    
    Args:
        client: HTTP client
        headers: Request headers
        subscription_id: Subscription ID
        resource_group: Resource group name
        vnet_name: VNet name
        
    Returns:
        List of subnet dictionaries with NSG rules included
    """
    subnets = []
    
    try:
        subnets_endpoint = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Network/virtualNetworks/{vnet_name}/subnets?api-version=2023-09-01"
        
        subnets_response = client.get(subnets_endpoint, headers=headers)
        
        if subnets_response.status_code == 200:
            subnets_data = subnets_response.json()
            subnets_raw = subnets_data.get('value', [])
            
            for subnet in subnets_raw:
                subnet_info = process_subnet_data(subnet)
                
                # Get NSG rules if NSG is associated with the subnet
                if subnet_info['network_security_group_id']:
                    nsg_rules = get_nsg_rules(client, headers, subnet_info['network_security_group_id'])
                    subnet_info['nsg_rules'] = nsg_rules
                else:
                    subnet_info['nsg_rules'] = []
                
                subnets.append(subnet_info)
        
        else:
            logger.warning(f"⚠️ Failed to get subnets for VNet {vnet_name}: {subnets_response.status_code}")
    
    except Exception as e:
        logger.error(f"❌ Error getting subnets for VNet {vnet_name}: {str(e)}")
    
    return subnets


def process_subnet_data(subnet):
    """
    Process and extract relevant subnet information.
    
    Args:
        subnet: Raw subnet data from Azure API
        
    Returns:
        Dictionary with processed subnet information
    """
    properties = subnet.get('properties', {})
    
    # Get network security group
    nsg = None
    nsg_id = None
    if 'networkSecurityGroup' in properties:
        nsg_id = properties['networkSecurityGroup'].get('id', '')
        if nsg_id:
            nsg = nsg_id.split('/')[-1]  # Extract NSG name from ID
    
    # Get route table
    route_table = None
    if 'routeTable' in properties:
        rt_id = properties['routeTable'].get('id', '')
        if rt_id:
            route_table = rt_id.split('/')[-1]  # Extract route table name from ID
    
    return {
        'id': subnet.get('id', ''),
        'name': subnet.get('name', ''),
        'address_prefix': properties.get('addressPrefix', ''),
        'provisioning_state': properties.get('provisioningState', ''),
        'network_security_group': nsg,
        'network_security_group_id': nsg_id,
        'route_table': route_table,
        'delegations': [d.get('name', '') for d in properties.get('delegations', [])],
        'purpose': properties.get('purpose', ''),
        'type': subnet.get('type', '')
    }


def get_nsg_rules(client, headers, nsg_id):
    """
    Get Network Security Group rules for a given NSG ID.
    
    Args:
        client: HTTP client for API calls
        headers: Authentication headers
        nsg_id: Full resource ID of the NSG
        
    Returns:
        List of NSG rules dictionaries
    """
    if not nsg_id:
        return []
    
    try:
        # Extract subscription ID and resource group from NSG ID
        # Format: /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/networkSecurityGroups/{networkSecurityGroupName}
        parts = nsg_id.split('/')
        if len(parts) < 8:
            logger.warning(f"⚠️ Invalid NSG ID format: {nsg_id}")
            return []
        
        subscription_id = parts[2]
        resource_group = parts[4]
        nsg_name = parts[8]
        
        # Construct API URL for NSG rules
        url = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Network/networkSecurityGroups/{nsg_name}?api-version=2023-09-01"
        
        logger.debug(f"🔍 Getting NSG rules for {nsg_name}")
        response = client.get(url, headers=headers)
        
        if response.status_code == 200:
            nsg_data = response.json()
            properties = nsg_data.get('properties', {})
            
            # Get both default and custom security rules
            default_rules = properties.get('defaultSecurityRules', [])
            custom_rules = properties.get('securityRules', [])
            
            rules = []
            
            # Process custom rules first (higher priority)
            for rule in custom_rules:
                rules.append(process_nsg_rule(rule, is_default=False))
            
            # Process default rules
            for rule in default_rules:
                rules.append(process_nsg_rule(rule, is_default=True))
            
            logger.debug(f"✅ Retrieved {len(rules)} NSG rules for {nsg_name}")
            return rules
        else:
            logger.warning(f"⚠️ Failed to get NSG rules for {nsg_name}: {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error getting NSG rules for {nsg_id}: {str(e)}")
        return []


def process_nsg_rule(rule, is_default=False):
    """
    Process and extract relevant NSG rule information.
    
    Args:
        rule: Raw NSG rule data from Azure API
        is_default: Whether this is a default rule
        
    Returns:
        Dictionary with processed NSG rule information
    """
    properties = rule.get('properties', {})
    
    # Process source and destination
    source = properties.get('sourceAddressPrefix', '')
    if not source:
        source_prefixes = properties.get('sourceAddressPrefixes', [])
        source = ', '.join(source_prefixes) if source_prefixes else 'Any'
    
    destination = properties.get('destinationAddressPrefix', '')
    if not destination:
        dest_prefixes = properties.get('destinationAddressPrefixes', [])
        destination = ', '.join(dest_prefixes) if dest_prefixes else 'Any'
    
    # Process ports
    dest_port = properties.get('destinationPortRange', '')
    if not dest_port:
        dest_port_ranges = properties.get('destinationPortRanges', [])
        dest_port = ', '.join(dest_port_ranges) if dest_port_ranges else 'Any'
    
    source_port = properties.get('sourcePortRange', '')
    if not source_port:
        source_port_ranges = properties.get('sourcePortRanges', [])
        source_port = ', '.join(source_port_ranges) if source_port_ranges else 'Any'
    
    return {
        'name': rule.get('name', ''),
        'priority': properties.get('priority', 0),
        'direction': properties.get('direction', ''),
        'access': properties.get('access', ''),
        'protocol': properties.get('protocol', ''),
        'source': source,
        'source_port': source_port,
        'destination': destination,
        'destination_port': dest_port,
        'description': properties.get('description', ''),
        'is_default': is_default
    }


def main():
    """
    Main function to orchestrate Azure VNet management operations.
    Generates HTML report and serves it via web server.
    """
    """
    Main function to orchestrate Azure VNet management operations.
    Generates HTML report and serves it via web server.
    """
    args = None
    try:
        # Setup logging and parse arguments
        args = parse_vnet_arguments()
        setup_logging(args.debug if args else False)
        
        logger.info("🚀 Starting Azure VNet Management Tool...")
        
        # Get cloud information - use forced cloud if specified
        if args and args.cloud:
            from providers.azure.lib.common.config import get_cloud_config
            cloud_config = get_cloud_config(args.cloud)
            detected_cloud = args.cloud
            logger.info(f"🌏 Using forced Azure Cloud: {cloud_config['name']}")
        else:
            detected_cloud, cloud_config = get_current_cloud_info()
            logger.info(f"🌏 Detected Azure Cloud: {cloud_config['name']}")
        
        start_time = time.time()
        
        # Test authentication with consistent cloud environment
        logger.info("🔐 Testing Azure authentication...")
        credential = get_azure_credential(detected_cloud)
        
        if not check_authentication(detected_cloud, credential):
            logger.error("❌ Authentication failed. Please check your credentials.")
            sys.exit(1)
        
        logger.info("✅ Authentication successful")
        
        # Get VNets and subnets data with parallel processing
        logger.info("🔍 Retrieving VNets and subnets using parallel processing...")
        vnets_data = get_vnets_and_subnets(credential, args)
        
        if not vnets_data:
            logger.warning("⚠️ No VNets found or accessible")
            sys.exit(0)
        
        # Generate HTML report
        logger.info("📊 Generating HTML report...")
        output_file = generate_vnet_html_report(vnets_data, "azure_vnet_report.html", detected_cloud)
        
        if hasattr(args, 'web') and args.web:
            # Start web server and open in browser
            try:
                port = find_available_port()
                server_thread = start_web_server(output_file, port, daemon=False)
                url = f"http://localhost:{port}/{os.path.basename(output_file)}"
                logger.info(f"🌐 VNet report available at: {url}")
                try:
                    webbrowser.open(url)
                    logger.info("🔗 Opening report in default browser...")
                except Exception as e:
                    logger.warning(f"⚠️ Could not open browser automatically: {e}")
                logger.info("📊 VNet report server is running. Press Ctrl+C to stop.")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("🛑 Shutting down VNet report server...")
            except Exception as e:
                logger.error(f"❌ Failed to start web server: {e}")
                logger.info(f"📁 Report saved to: {os.path.abspath(output_file)}")
        else:
            logger.info(f"📁 Report saved to: {os.path.abspath(output_file)} (no browser launched)")
            logger.info("To view the report, open the above HTML file in your browser or rerun with -w/--web to launch the web server.")
        
        end_time = time.time()
        
        # Print performance summary
        if args and args.debug:
            print_detailed_performance_summary(start_time, end_time, len(vnets_data), "VNet Management (Parallel Processing)")
        
        logger.info("✅ Azure VNet Management Tool completed successfully")
        
    except KeyboardInterrupt:
        logger.info("🛑 Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ An unexpected error occurred: {str(e)}")
        if args and args.debug:
            logger.debug(traceback.format_exc())

# Contains AI-generated edits.
        sys.exit(1)


if __name__ == "__main__":
    main()

# Contains AI-generated edits.
