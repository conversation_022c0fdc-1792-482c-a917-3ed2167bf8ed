#!/usr/bin/env python3
"""
Azure Resource Overview HTML report generator.
"""
import os
import logging
from datetime import datetime
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

def generate_resource_overview_html_report(
    hierarchical_data: List[Dict[str, Any]], 
    stats: Dict[str, int],
    cloud_region: str = "global"
) -> str:
    """
    Generate an interactive HTML report for Azure resource overview.
    
    Args:
        hierarchical_data: Hierarchical resource data organized by subscription -> resource group -> resource type
        stats: Summary statistics including total counts
        cloud_region: Azure cloud region (global or china)
    
    Returns:
        Path to the generated HTML file
    """
    try:
        # Ensure html directory exists
        html_dir = "html"
        os.makedirs(html_dir, exist_ok=True)
        
        # Create resource_overview subdirectory if it doesn't exist
        resource_overview_dir = os.path.join(html_dir, "resource_overview")
        if not os.path.exists(resource_overview_dir):
            os.makedirs(resource_overview_dir)
            logger.info(f"Created Resource Overview assets directory: {resource_overview_dir}")
        
        # Generate HTML content
        html_content = _generate_html_template(hierarchical_data, stats, cloud_region)
        
        # Write to file with consistent naming pattern (no timestamp)
        filename = f"azure_resource_overview_report_{cloud_region}.html"
        output_path = os.path.join(html_dir, filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"📄 Resource overview report generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"❌ Error generating resource overview HTML report: {e}")
        raise


def _generate_html_template(
    hierarchical_data: List[Dict[str, Any]], 
    stats: Dict[str, Any],
    cloud_region: str
) -> str:
    """Generate the complete HTML template for the resource overview."""
    
    # Convert data to JSON for JavaScript
    import json
    data_json = json.dumps(hierarchical_data, indent=2, default=str)
    stats_json = json.dumps(stats, indent=2, default=str)
    
    # Get cloud display name
    cloud_name = "Azure Global" if cloud_region == "global" else "Azure China"
    
    html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Resource Overview - {cloud_name}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="resource_overview/resource_overview_report.css">
</head>
<body class="bg-blue-50 text-gray-900 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-blue-600 shadow-lg border-b border-blue-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-white">
                            🌐 Azure Resource Overview
                        </h1>
                        <span class="ml-3 px-3 py-1 bg-blue-800 text-blue-100 text-sm font-medium rounded-full border border-blue-500">
                            {cloud_name}
                        </span>
                    </div>
                    <div class="text-sm text-blue-100">
                        Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Summary Statistics -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">📊 Summary Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="summaryStats">
                    <!-- Stats will be populated by JavaScript -->
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">🔍 Filters</h2>
                <div class="bg-white p-6 rounded-lg shadow-lg border border-blue-200">
                    <!-- Search Box -->
                    <div class="mb-4">
                        <label for="resourceSearch" class="block text-sm font-medium text-gray-700 mb-2">
                            Search Resources
                        </label>
                        <input type="text" 
                               id="resourceSearch" 
                               class="w-full border border-blue-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Search resources by name, type, or ID...">
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="subscriptionFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Subscription
                            </label>
                            <select id="subscriptionFilter" class="w-full border border-blue-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">All Subscriptions</option>
                            </select>
                        </div>
                        <div>
                            <label for="resourceGroupFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Resource Group
                            </label>
                            <select id="resourceGroupFilter" class="w-full border border-blue-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">All Resource Groups</option>
                            </select>
                        </div>
                        <div>
                            <label for="resourceTypeFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Resource Type
                            </label>
                            <select id="resourceTypeFilter" class="w-full border border-blue-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">All Resource Types</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <button id="clearFilters" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors border border-blue-300 hover:border-blue-400">
                            Clear All Filters
                        </button>
                        <div class="text-sm text-gray-600">
                            <span id="filteredCount">0</span> resources displayed
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Hierarchy -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-900">🏗️ Resource Hierarchy</h2>
                    <div class="space-x-2">
                        <button id="expandAll" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors border border-blue-600 hover:border-blue-700">
                            Expand All
                        </button>
                        <button id="collapseAll" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors border border-blue-300 hover:border-blue-400">
                            Collapse All
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg border border-blue-200">
                    <div id="resourceHierarchy" class="p-6">
                        <!-- Hierarchy will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Data from Python backend
        window.hierarchicalData = {data_json};
        window.summaryStats = {stats_json};
    </script>
    <script src="resource_overview/resource_overview_report.js"></script>
</body>
</html>"""
    
    return html_template

# Contains AI-generated edits.
