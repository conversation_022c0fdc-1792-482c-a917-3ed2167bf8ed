#!/usr/bin/env python3
"""
Azure Resource Overview data collection module.
Efficiently retrieves all Azure resources using Azure Resource Graph API.
"""
import logging
from typing import List, Dict, Any, Optional, Union
from azure.identity import DefaultAzureCredential
from azure.mgmt.resource.subscriptions import SubscriptionClient
from azure.mgmt.resourcegraph import ResourceGraphClient
from azure.mgmt.resourcegraph.models import QueryRequest, QueryRequestOptions

# Import cloud configuration for proper endpoint handling
from providers.azure.lib.common.config import get_current_cloud_info

logger = logging.getLogger(__name__)

def get_all_resources(credential: Union[DefaultAzureCredential, Any], cloud_env: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Retrieve all Azure resources across all accessible subscriptions.
    
    Args:
        credential: Azure credential for authentication
        cloud_env: Cloud environment ('global', 'china', 'government')
                  If None, will auto-detect
        
    Returns:
        List of resource dictionaries with hierarchical organization
        
    Raises:
        Exception: If resource retrieval fails
    """
    try:
        # Get cloud configuration for proper endpoint handling
        if cloud_env:
            from providers.azure.lib.common.config import get_cloud_config
            cloud_config = get_cloud_config(cloud_env)
            detected_cloud = cloud_env
        else:
            detected_cloud, cloud_config = get_current_cloud_info()
        
        logger.info(f"🌍 Using {cloud_config['name']} endpoints")
        
        # Step 1: Retrieve all subscription IDs accessible by the credential
        logger.info("🔍 Retrieving accessible subscriptions...")
        
        # Initialize SubscriptionClient with correct cloud endpoint
        subs_client = SubscriptionClient(
            credential, 
            base_url=cloud_config['management_endpoint']
        )
        subscriptions = []
        
        for sub in subs_client.subscriptions.list():
            subscriptions.append({
                'subscription_id': sub.subscription_id,
                'subscription_name': sub.display_name or sub.subscription_id
            })
        
        if not subscriptions:
            logger.warning("No subscriptions found or missing proper permissions.")
            return []
        
        logger.info(f"📊 Found {len(subscriptions)} accessible subscription(s)")
        
        # Step 2: Initialize Resource Graph client with correct cloud endpoint
        rg_client = ResourceGraphClient(
            credential,
            base_url=cloud_config['management_endpoint']
        )
        
        # Step 3: Define the query to fetch all resources
        kusto_query = """
        Resources 
        | project id, name, type, location, resourceGroup, subscriptionId, kind, sku, tags
        | order by subscriptionId, resourceGroup, type, name
        """
        
        # Step 4: Prepare the query request with options
        subscription_ids = [sub['subscription_id'] for sub in subscriptions]
        request = QueryRequest(
            subscriptions=subscription_ids,
            query=kusto_query,
            options=QueryRequestOptions(result_format="objectArray")
        )
        
        # Step 5: Execute the query with pagination handling
        logger.info("📡 Executing Resource Graph query...")
        response = rg_client.resources(request)
        
        # Handle response data - cast to expected types
        all_data: List[Dict[str, Any]] = []
        if hasattr(response, 'data') and response.data is not None:
            # The ResourceGraph API returns data as a list of objects
            # We'll cast it to our expected type
            response_data = response.data
            if response_data:
                try:
                    # Try to iterate and convert each item to a dict
                    for item in response_data:  # type: ignore
                        if isinstance(item, dict):
                            all_data.append(item)
                        else:
                            # Convert object to dict if possible
                            all_data.append(dict(item.__dict__) if hasattr(item, '__dict__') else {})
                except (TypeError, AttributeError):
                    # If iteration fails, assume it's a single item
                    logger.warning("Response data is not iterable, treating as single item")
                    if isinstance(response_data, dict):
                        all_data = [response_data]  # type: ignore
                    else:
                        all_data = [dict(response_data.__dict__) if hasattr(response_data, '__dict__') else {}]  # type: ignore
        
        # Handle pagination
        skip_token = getattr(response, 'skip_token', None)
        while skip_token:
            try:
                # Create new request options with skip_token
                request.options = QueryRequestOptions(
                    result_format="objectArray",
                    skip_token=skip_token
                )
                response = rg_client.resources(request)
                
                if hasattr(response, 'data') and response.data is not None:
                    response_data = response.data
                    if response_data:
                        try:
                            page_count = 0
                            for item in response_data:  # type: ignore
                                if isinstance(item, dict):
                                    all_data.append(item)
                                else:
                                    all_data.append(dict(item.__dict__) if hasattr(item, '__dict__') else {})
                                page_count += 1
                            logger.debug(f"Retrieved additional {page_count} resources (pagination)")
                        except (TypeError, AttributeError):
                            # If iteration fails, assume it's a single item
                            if isinstance(response_data, dict):
                                all_data.append(response_data)  # type: ignore
                            else:
                                all_data.append(dict(response_data.__dict__) if hasattr(response_data, '__dict__') else {})  # type: ignore
                            logger.debug("Retrieved additional 1 resource (pagination)")
                
                skip_token = getattr(response, 'skip_token', None)
            except Exception as e:
                logger.warning(f"Error during pagination: {e}")
                break
        
        logger.info(f"✅ Total resources retrieved: {len(all_data)}")
        
        # Step 6: Enrich with subscription names and organize hierarchically
        subscription_map = {sub['subscription_id']: sub['subscription_name'] for sub in subscriptions}
        
        # Organize resources into hierarchical structure
        hierarchical_data = _organize_resources_hierarchically(all_data, subscription_map)
        
        return hierarchical_data
        
    except Exception as e:
        logger.error(f"❌ Error retrieving resources: {e}")
        raise


def _organize_resources_hierarchically(
    resources: List[Dict[str, Any]], 
    subscription_map: Dict[str, str]
) -> List[Dict[str, Any]]:
    """
    Organize flat resource list into hierarchical structure sorted by resource count.
    
    Args:
        resources: Flat list of resources from Resource Graph
        subscription_map: Mapping of subscription IDs to names
        
    Returns:
        Hierarchical structure organized by subscription -> resource group -> resource type
    """
    logger.info("🏗️ Organizing resources into hierarchical structure...")
    
    # Build hierarchy: subscription -> resource_group -> resource_type -> resources
    hierarchy = {}
    
    for resource in resources:
        sub_id = resource.get('subscriptionId', '')
        sub_name = subscription_map.get(sub_id, sub_id)
        rg_name = resource.get('resourceGroup', 'Unknown')
        res_type = resource.get('type', 'Unknown')
        
        # Initialize subscription level
        if sub_id not in hierarchy:
            hierarchy[sub_id] = {
                'subscription_id': sub_id,
                'subscription_name': sub_name,
                'resource_groups': {},
                'resource_count': 0
            }
        
        # Initialize resource group level
        if rg_name not in hierarchy[sub_id]['resource_groups']:
            hierarchy[sub_id]['resource_groups'][rg_name] = {
                'name': rg_name,
                'resource_types': {},
                'resource_count': 0
            }
        
        # Initialize resource type level
        if res_type not in hierarchy[sub_id]['resource_groups'][rg_name]['resource_types']:
            hierarchy[sub_id]['resource_groups'][rg_name]['resource_types'][res_type] = {
                'type': res_type,
                'resources': [],
                'resource_count': 0
            }
        
        # Add resource to the hierarchy
        hierarchy[sub_id]['resource_groups'][rg_name]['resource_types'][res_type]['resources'].append({
            'id': resource.get('id', ''),
            'name': resource.get('name', ''),
            'type': res_type,
            'location': resource.get('location', ''),
            'resource_group': rg_name
        })
        
        # Update counts
        hierarchy[sub_id]['resource_groups'][rg_name]['resource_types'][res_type]['resource_count'] += 1
        hierarchy[sub_id]['resource_groups'][rg_name]['resource_count'] += 1
        hierarchy[sub_id]['resource_count'] += 1
    
    # Convert to sorted list structure
    result = []
    
    # Sort subscriptions by resource count (descending)
    sorted_subscriptions = sorted(
        hierarchy.items(), 
        key=lambda x: x[1]['resource_count'], 
        reverse=True
    )
    
    for sub_id, sub_data in sorted_subscriptions:
        # Sort resource groups by resource count (descending)
        sorted_rgs = sorted(
            sub_data['resource_groups'].items(),
            key=lambda x: x[1]['resource_count'],
            reverse=True
        )
        
        resource_groups = []
        for rg_name, rg_data in sorted_rgs:
            # Sort resource types by resource count (descending)
            sorted_types = sorted(
                rg_data['resource_types'].items(),
                key=lambda x: x[1]['resource_count'],
                reverse=True
            )
            
            resource_types = []
            for type_name, type_data in sorted_types:
                # Sort individual resources by name
                sorted_resources = sorted(
                    type_data['resources'],
                    key=lambda x: x['name'].lower()
                )
                
                resource_types.append({
                    'type': type_name,
                    'resource_count': type_data['resource_count'],
                    'resources': sorted_resources
                })
            
            resource_groups.append({
                'name': rg_name,
                'resource_count': rg_data['resource_count'],
                'resource_types': resource_types
            })
        
        result.append({
            'subscription_id': sub_id,
            'subscription_name': sub_data['subscription_name'],
            'resource_count': sub_data['resource_count'],
            'resource_groups': resource_groups
        })
    
    logger.info(f"✅ Organized into {len(result)} subscription(s) with hierarchical structure")
    return result


def calculate_overview_statistics(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate summary statistics for the resource overview.
    
    Args:
        data: Hierarchical resource data
        
    Returns:
        Dictionary containing summary statistics
    """
    stats = {
        'total_resources': 0,
        'total_subscriptions': len(data),
        'total_resource_groups': 0,
        'total_resource_types': 0,
        'locations': set(),
        'resource_type_counts': {},
        'top_resource_groups': [],
        'top_resource_types': []
    }
    
    # Collect statistics
    for subscription in data:
        stats['total_resources'] += subscription['resource_count']
        stats['total_resource_groups'] += len(subscription['resource_groups'])
        
        for resource_group in subscription['resource_groups']:
            for resource_type in resource_group['resource_types']:
                # Count resource types
                type_name = resource_type['type']
                if type_name not in stats['resource_type_counts']:
                    stats['resource_type_counts'][type_name] = 0
                stats['resource_type_counts'][type_name] += resource_type['resource_count']
                
                # Collect locations
                for resource in resource_type['resources']:
                    location = resource.get('location', '')
                    if location:
                        stats['locations'].add(location)
    
    stats['total_locations'] = len(stats['locations'])
    stats['total_resource_types'] = len(stats['resource_type_counts'])
    
    # Calculate top items
    stats['top_resource_types'] = sorted(
        stats['resource_type_counts'].items(),
        key=lambda x: x[1],
        reverse=True
    )[:10]
    
    # Convert locations set to count
    stats['locations'] = stats['total_locations']
    
    return stats

# Contains AI-generated edits.
