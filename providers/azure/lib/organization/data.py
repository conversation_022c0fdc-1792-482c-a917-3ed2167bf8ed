#!/usr/bin/env python3
"""
Azure Organization data collection module.
Retrieves organization hierarchy with management groups, subscriptions, and resource groups.
"""
import logging
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

from azure.mgmt.resource import ResourceManagementClient, SubscriptionClient
from azure.mgmt.managementgroups import ManagementGroupsAPI
from azure.identity import DefaultAzureCredential

logger = logging.getLogger(__name__)

# Thread-safe counter for progress tracking
progress_lock = Lock()
progress_counter = 0


def get_organization_hierarchy(
    credential: DefaultAzureCredential, 
    cloud_config: Dict[str, str],
    include_resources: bool = False
) -> List[Dict[str, Any]]:
    """
    Retrieve the complete Azure organization hierarchy including management groups,
    subscriptions, resource groups, and optionally resources.
    
    Args:
        credential: Azure credential for authentication
        cloud_config: Cloud configuration dictionary with endpoints
        include_resources: Whether to include individual resources in resource groups
        
    Returns:
        List of hierarchical organization data organized as:
        [
            {
                "id": "mg_id",
                "name": "Management Group Name", 
                "type": "management_group",
                "children": [...],  # Child management groups
                "subscriptions": [...]  # Direct subscriptions
            }
        ]
        
    Raises:
        Exception: If hierarchy retrieval fails
    """
    try:
        logger.info("🏢 Starting organization hierarchy retrieval...")
        
        # Step 1: Get all subscriptions
        logger.info("📋 Retrieving subscriptions...")
        subscription_results = _get_all_subscriptions(credential, cloud_config, include_resources)
        
        # Create subscription lookup map
        subscription_map = {sub["id"]: sub for sub in subscription_results}
        
        # Step 2: Get management groups
        logger.info("🏗️ Retrieving management groups...")
        hierarchy = _get_management_group_hierarchy(credential, cloud_config, subscription_map)
        
        # If no management groups or all subscriptions are unassigned, create default structure
        if not hierarchy:
            logger.info("📦 No management groups found, creating default structure")
            hierarchy = [{
                "id": "tenant_root",
                "name": "Tenant Root",
                "type": "management_group", 
                "children": [],
                "subscriptions": subscription_results,
                "resource_count": sum(sub.get("resource_count", 0) for sub in subscription_results),
                "subscription_count": len(subscription_results),
                "resource_group_count": sum(len(sub.get("resource_groups", [])) for sub in subscription_results)
            }]
        
        # Step 3: Calculate statistics for each node
        _calculate_hierarchy_statistics(hierarchy)
        
        logger.info(f"✅ Organization hierarchy retrieved successfully")
        return hierarchy
        
    except Exception as e:
        logger.error(f"❌ Error retrieving organization hierarchy: {e}")
        raise


def _get_all_subscriptions(
    credential: DefaultAzureCredential, 
    cloud_config: Dict[str, str],
    include_resources: bool = False
) -> List[Dict[str, Any]]:
    """Get all accessible subscriptions with their resource groups and optionally resources."""
    try:
        # Initialize subscription client
        subscription_client = SubscriptionClient(
            credential,
            base_url=cloud_config.get('management_endpoint', 'https://management.azure.com/')
        )
        
        # Get list of subscriptions
        subscriptions = list(subscription_client.subscriptions.list())
        logger.info(f"📊 Found {len(subscriptions)} accessible subscriptions")
        
        if not subscriptions:
            return []
        
        # Process subscriptions in parallel
        subscription_results = []
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_sub = {
                executor.submit(
                    _get_subscription_details, 
                    credential, 
                    sub, 
                    cloud_config, 
                    include_resources
                ): sub for sub in subscriptions
            }
            
            for future in as_completed(future_to_sub):
                try:
                    result = future.result()
                    if result:
                        subscription_results.append(result)
                        
                        global progress_counter
                        with progress_lock:
                            progress_counter += 1
                            logger.info(f"📈 Progress: {progress_counter}/{len(subscriptions)} subscriptions processed")
                            
                except Exception as e:
                    sub = future_to_sub[future]
                    logger.warning(f"Failed to process subscription {getattr(sub, 'display_name', 'Unknown')}: {e}")
        
        return subscription_results
        
    except Exception as e:
        logger.error(f"Error retrieving subscriptions: {e}")
        raise


def _get_subscription_details(
    credential: DefaultAzureCredential,
    subscription: Any,
    cloud_config: Dict[str, str], 
    include_resources: bool = False
) -> Optional[Dict[str, Any]]:
    """Get detailed information for a single subscription."""
    try:
        sub_id = getattr(subscription, 'subscription_id', None)
        sub_name = getattr(subscription, 'display_name', None) or sub_id or 'Unknown'
        
        if not sub_id:
            logger.warning(f"Subscription without ID: {sub_name}")
            return None
        
        # Initialize Resource Management client for this subscription
        resource_client = ResourceManagementClient(
            credential,
            sub_id,
            base_url=cloud_config.get('management_endpoint', 'https://management.azure.com/')
        )
        
        # Get resource groups
        resource_groups = []
        total_resource_count = 0
        
        try:
            rg_list = list(resource_client.resource_groups.list())
            
            for rg in rg_list:
                rg_info = {
                    "name": rg.name,
                    "location": rg.location,
                    "id": rg.id,
                    "type": "resource_group"
                }
                
                # Get resource count for this resource group
                if include_resources and rg.name:
                    try:
                        # Use Resource Graph or direct resource listing
                        resources_list = list(resource_client.resources.list(
                            filter=f"resourceGroup eq '{rg.name}'"
                        ))
                        rg_info["resources"] = [
                            {
                                "name": res.name,
                                "type": res.type,
                                "location": getattr(res, 'location', None),
                                "id": res.id
                            } for res in resources_list
                        ]
                        rg_info["resource_count"] = len(resources_list)
                        total_resource_count += len(resources_list)
                    except Exception as e:
                        logger.warning(f"Could not get resources for RG {rg.name}: {e}")
                        rg_info["resource_count"] = 0
                else:
                    # Just get count without details
                    try:
                        if rg.name:
                            resources_list = list(resource_client.resources.list(
                                filter=f"resourceGroup eq '{rg.name}'"
                            ))
                            rg_info["resource_count"] = len(resources_list)
                            total_resource_count += len(resources_list)
                        else:
                            rg_info["resource_count"] = 0
                    except Exception as e:
                        logger.warning(f"Could not count resources for RG {rg.name}: {e}")
                        rg_info["resource_count"] = 0
                
                resource_groups.append(rg_info)
                
        except Exception as e:
            logger.warning(f"Could not retrieve resource groups for subscription {sub_name}: {e}")
        
        return {
            "id": sub_id,
            "name": sub_name,
            "type": "subscription",
            "resource_groups": resource_groups,
            "resource_group_count": len(resource_groups),
            "resource_count": total_resource_count
        }
        
    except Exception as e:
        logger.warning(f"Error processing subscription details: {e}")
        return None


def _get_management_group_hierarchy(
    credential: DefaultAzureCredential,
    cloud_config: Dict[str, str],
    subscription_map: Dict[str, Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Get management group hierarchy and assign subscriptions."""
    try:
        # Initialize Management Groups client
        mg_client = ManagementGroupsAPI(
            credential,
            base_url=cloud_config.get('management_endpoint', 'https://management.azure.com/')
        )
        
        # Get all management groups
        try:
            mg_list = list(mg_client.management_groups.list())
        except Exception as e:
            logger.warning(f"Could not retrieve management groups: {e}")
            return []
        
        if not mg_list:
            logger.info("📋 No management groups found")
            return []
        
        logger.info(f"🏗️ Found {len(mg_list)} management groups")
        
        # Build management group nodes
        mg_nodes = {}
        assigned_subscriptions = set()
        all_mg_ids = set()
        child_mg_ids = set()
        
        # First pass: Create all management group nodes
        for mg in mg_list:
            mg_id = getattr(mg, 'name', None)
            if not mg_id:
                continue
                
            display_name = getattr(mg, 'display_name', None) or mg_id
            all_mg_ids.add(mg_id)
            
            # Get detailed information for this management group
            try:
                mg_detail = mg_client.management_groups.get(group_id=mg_id, expand="children")
                
                mg_node = {
                    "id": mg_id,
                    "name": display_name,
                    "type": "management_group",
                    "children": [],
                    "subscriptions": []
                }
                
                # Process direct children
                if hasattr(mg_detail, 'children') and mg_detail.children:
                    for child in mg_detail.children:
                        child_type = getattr(child, 'type', '').lower()
                        child_id = getattr(child, 'id', '').split('/')[-1]
                        
                        if 'managementgroup' in child_type:
                            child_mg_ids.add(child_id)
                        elif 'subscription' in child_type and child_id in subscription_map:
                            mg_node["subscriptions"].append(subscription_map[child_id])
                            assigned_subscriptions.add(child_id)
                
                mg_nodes[mg_id] = mg_node
                
            except Exception as e:
                logger.warning(f"Could not get details for management group {mg_id}: {e}")
                mg_nodes[mg_id] = {
                    "id": mg_id,
                    "name": display_name,
                    "type": "management_group",
                    "children": [],
                    "subscriptions": []
                }
        
        # Second pass: Build parent-child relationships
        for mg_id, mg_node in mg_nodes.items():
            try:
                mg_detail = mg_client.management_groups.get(group_id=mg_id, expand="children")
                if hasattr(mg_detail, 'children') and mg_detail.children:
                    for child in mg_detail.children:
                        child_type = getattr(child, 'type', '').lower()
                        child_id = getattr(child, 'id', '').split('/')[-1]
                        
                        if 'managementgroup' in child_type and child_id in mg_nodes:
                            mg_node["children"].append(mg_nodes[child_id])
            except Exception as e:
                logger.warning(f"Could not get children for management group {mg_id}: {e}")
        
        # Find root management groups
        root_mg_ids = all_mg_ids - child_mg_ids
        root_groups = [mg_nodes[mg_id] for mg_id in root_mg_ids if mg_id in mg_nodes]
        
        # Handle unassigned subscriptions
        unassigned_subs = [sub for sub_id, sub in subscription_map.items() if sub_id not in assigned_subscriptions]
        if unassigned_subs:
            logger.info(f"📋 Found {len(unassigned_subs)} unassigned subscriptions")
            unassigned_mg = {
                "id": "unassigned",
                "name": "Unassigned Subscriptions",
                "type": "management_group",
                "children": [],
                "subscriptions": unassigned_subs
            }
            root_groups.append(unassigned_mg)
        
        return root_groups
        
    except Exception as e:
        logger.error(f"Error building management group hierarchy: {e}")
        return []


def _calculate_hierarchy_statistics(hierarchy: List[Dict[str, Any]]) -> None:
    """Calculate recursive statistics for all nodes in the hierarchy."""
    def calculate_node_stats(node: Dict[str, Any]) -> Dict[str, int]:
        """Calculate statistics for a single node recursively."""
        stats = {
            "subscription_count": 0,
            "resource_group_count": 0,
            "resource_count": 0,
            "management_group_count": 0
        }
        
        # Count direct subscriptions
        subscriptions = node.get("subscriptions", [])
        stats["subscription_count"] = len(subscriptions)
        
        # Count resource groups and resources from direct subscriptions
        for sub in subscriptions:
            stats["resource_group_count"] += sub.get("resource_group_count", 0)
            stats["resource_count"] += sub.get("resource_count", 0)
        
        # Count from child management groups recursively
        children = node.get("children", [])
        for child in children:
            if child.get("type") == "management_group":
                stats["management_group_count"] += 1
                child_stats = calculate_node_stats(child)
                
                # Add child statistics to this node
                stats["subscription_count"] += child_stats["subscription_count"]
                stats["resource_group_count"] += child_stats["resource_group_count"]
                stats["resource_count"] += child_stats["resource_count"]
                stats["management_group_count"] += child_stats["management_group_count"]
                
                # Update child node with its calculated statistics
                child.update(child_stats)
        
        return stats
    
    # Calculate statistics for all root nodes
    for node in hierarchy:
        stats = calculate_node_stats(node)
        node.update(stats)


def calculate_organization_statistics(hierarchy: List[Dict[str, Any]]) -> Dict[str, int]:
    """Calculate overall organization statistics."""
    total_stats = {
        "total_management_groups": 0,
        "total_subscriptions": 0,
        "total_resource_groups": 0,
        "total_resources": 0
    }
    
    for root_node in hierarchy:
        total_stats["total_management_groups"] += 1 + root_node.get("management_group_count", 0)
        total_stats["total_subscriptions"] += root_node.get("subscription_count", 0)
        total_stats["total_resource_groups"] += root_node.get("resource_group_count", 0)
        total_stats["total_resources"] += root_node.get("resource_count", 0)
    
    return total_stats

# Contains AI-generated edits.
