#!/usr/bin/env python3
"""
Azure Organization HTML report generator.
Creates interactive tree-view reports matching the resource overview style.
"""
import os
import logging
from datetime import datetime
from typing import List, Dict, Any
import json

logger = logging.getLogger(__name__)

def generate_organization_html_report(
    hierarchical_data: List[Dict[str, Any]], 
    stats: Dict[str, int],
    cloud_region: str = "global"
) -> str:
    """
    Generate an interactive HTML report for Azure organization hierarchy.
    
    Args:
        hierarchical_data: Hierarchical organization data with management groups, subscriptions, etc.
        stats: Summary statistics including total counts
        cloud_region: Azure cloud region (global or china)
    
    Returns:
        Path to the generated HTML file
    """
    try:
        # Ensure html directory exists
        html_dir = "html"
        os.makedirs(html_dir, exist_ok=True)
        
        # Create organization subdirectory if it doesn't exist
        organization_dir = os.path.join(html_dir, "organization")
        if not os.path.exists(organization_dir):
            os.makedirs(organization_dir)
            logger.info(f"Created Organization assets directory: {organization_dir}")
        
        # Generate HTML content
        html_content = _generate_html_template(hierarchical_data, stats, cloud_region)
        
        # Write to file with consistent naming pattern (no timestamp)
        filename = f"azure_organization_report_{cloud_region}.html"
        output_path = os.path.join(html_dir, filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"📄 Organization report generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"❌ Error generating organization HTML report: {e}")
        raise


def _generate_html_template(
    hierarchical_data: List[Dict[str, Any]], 
    stats: Dict[str, Any],
    cloud_region: str
) -> str:
    """Generate the complete HTML template for the organization report."""
    
    # Convert data to JSON for JavaScript
    data_json = json.dumps(hierarchical_data, indent=2, default=str)
    stats_json = json.dumps(stats, indent=2, default=str)
    
    # Get cloud display name
    cloud_name = "Azure Global" if cloud_region == "global" else "Azure China"
    
    html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Organization Hierarchy - {cloud_name}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="organization/organization_report.css">
</head>
<body class="bg-blue-50 text-gray-900 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-blue-600 shadow-lg border-b border-blue-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-white">
                            🏢 Azure Organization Hierarchy
                        </h1>
                        <span class="ml-3 px-3 py-1 bg-blue-800 text-blue-100 text-sm font-medium rounded-full border border-blue-500">
                            {cloud_name}
                        </span>
                    </div>
                    <div class="text-sm text-blue-100">
                        Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500">Management Groups</h3>
                            <p class="text-2xl font-bold text-gray-900" id="stat-management-groups">{stats.get('total_management_groups', 0)}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500">Subscriptions</h3>
                            <p class="text-2xl font-bold text-gray-900" id="stat-subscriptions">{stats.get('total_subscriptions', 0)}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500">Resource Groups</h3>
                            <p class="text-2xl font-bold text-gray-900" id="stat-resource-groups">{stats.get('total_resource_groups', 0)}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500">Resources</h3>
                            <p class="text-2xl font-bold text-gray-900" id="stat-resources">{stats.get('total_resources', 0)}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Controls removed -->

            <!-- Search and Controls -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
                <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                    <div class="flex-1 max-w-md">
                        <label for="search-input" class="sr-only">Search organization</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input id="search-input" type="text" 
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="Search management groups, subscriptions, resource groups...">
                        </div>
                    </div>
                    
                    <div class="flex gap-2 flex-wrap">
                        <button id="expand-all-btn" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Expand All
                        </button>
                        
                        <button id="collapse-all-btn" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                            </svg>
                            Collapse All
                        </button>
                        
                        <button id="reset-view-btn" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Reset View
                        </button>
                    </div>
                </div>
            </div>

            <!-- Organization Tree -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Organization Hierarchy</h2>
                    <p class="text-sm text-gray-500 mt-1">Interactive tree view of your Azure organization structure</p>
                </div>
                
                <div class="p-6">
                    <div id="organization-tree" class="organization-tree">
                        <!-- Tree content will be generated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Include embedded JavaScript -->
    <script>
        // Data from Python - define on window object for global access
        window.organizationData = {data_json};
        window.organizationStats = {stats_json};
        window.cloudRegion = '{cloud_region}';
        
        // Also define as constants for backward compatibility
        const organizationData = window.organizationData;
        const organizationStats = window.organizationStats;
        const cloudRegion = window.cloudRegion;
    </script>
    <script src="organization/organization_report.js"></script>
</body>
</html>"""

    return html_template

# Contains AI-generated edits.
