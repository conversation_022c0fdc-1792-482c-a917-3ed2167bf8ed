<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Organization Hierarchy</title>
    <link rel="stylesheet" href="organization/organization_report.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Azure Organization Hierarchy</h1>
            <p>Interactive tree view of Management Groups, Subscriptions, and Resource Groups</p>
        </div>
        
        <div class="stats" id="stats"></div>
        
        <div class="search-container">
            <input type="text" class="search-input" id="searchInput" placeholder="Search for management groups, subscriptions, or resource groups...">
            <button class="search-button" onclick="searchTree()">Search</button>
            <button class="control-button" onclick="clearSearch()">Clear</button>
        </div>
        
        <div class="controls">
            <button class="control-button" onclick="expandAll()">Expand All</button>
            <button class="control-button" onclick="collapseAll()">Collapse All</button>
            <button class="control-button" onclick="collapseResourceGroups()">Hide Resource Groups</button>
            <button class="control-button" onclick="resetView()">Reset View</button>
            <button class="control-button" onclick="toggleAllResourceDetails()" id="toggleResourceDetailsBtn">Show All Resource Details</button>
        </div>
        
        <div class="tree-container" id="treeContainer"></div>
    </div>
    
    <div class="tooltip" id="tooltip"></div>
    
    <script>
        // Data from Python
        const data = {{HIERARCHY_DATA}};
    </script>
    <script src="organization/organization_report.js"></script>
</body>
</html>

<!-- Contains AI-generated edits. -->
<!-- Contains AI-generated edits. -->
