#!/usr/bin/env python3
"""
Azure VNet HTML Report Generator

This module provides HTML report generation functionality for Azure Virtual Networks,
including subnets, NSG rules, and associated resources visualization.
"""

import os
import logging

logger = logging.getLogger(__name__)


def generate_vnet_html_report(vnets_data, output_file="azure_vnet_report.html", cloud_region="global"):
    """
    Generate an HTML report for VNets data with comprehensive visualization.
    
    Args:
        vnets_data: List of VNet dictionaries containing VNet and subnet information
        output_file: Output HTML file name
        cloud_region: Azure cloud region (global or china)
        
    Returns:
        Path to generated HTML file
    """
    # Ensure the html directory exists
    html_dir = "html"
    if not os.path.exists(html_dir):
        os.makedirs(html_dir)
        logger.info(f"Created HTML output directory: {html_dir}")
    
    # Create vnet subdirectory if it doesn't exist
    vnet_dir = os.path.join(html_dir, "vnet")
    if not os.path.exists(vnet_dir):
        os.makedirs(vnet_dir)
        logger.info(f"Created VNet assets directory: {vnet_dir}")
    
    # Format the output filename to include the cloud region
    base_name = "azure_vnet_report"
    if "." in os.path.basename(output_file):
        base_name = os.path.basename(output_file).split(".")[0]
    
    final_filename = f"{base_name}_{cloud_region}.html"
    
    # Ensure output file is placed in html directory
    if not final_filename.startswith("html/"):
        output_file = os.path.join(html_dir, final_filename)
    
    # Calculate statistics
    stats = _calculate_vnet_statistics(vnets_data)
    
    # Generate table rows HTML
    table_rows = _generate_table_rows(vnets_data)
    
    # Create the complete HTML content
    html_content = _generate_html_content(stats, table_rows, cloud_region)
    
    # Write the HTML file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"📊 VNet HTML report generated: {output_file}")
    return output_file


def _calculate_vnet_statistics(vnets_data):
    """Calculate statistics for the VNets data."""
    total_vnets = len(vnets_data)
    total_subnets = sum(vnet['subnet_count'] for vnet in vnets_data)
    subscriptions = len(set(vnet['subscription_name'] for vnet in vnets_data))
    locations = len(set(vnet['location'] for vnet in vnets_data))
    
    return {
        'total_vnets': total_vnets,
        'total_subnets': total_subnets,
        'subscriptions': subscriptions,
        'locations': locations
    }


def _generate_table_rows(vnets_data):
    """Generate HTML table rows for VNets data."""
    table_rows = []
    
    for vnet in vnets_data:
        # Create subnets details HTML
        subnets_html = _generate_subnets_html(vnet)
        
        # Create address spaces display
        address_spaces_display = "<br>".join(vnet['address_spaces']) if vnet['address_spaces'] else "N/A"
        
        # Create tags display
        tags_display = _generate_tags_display(vnet['tags'])
        
        # Build table row
        table_rows.append(f"""
        <tr>
            <td>{vnet['name']}</td>
            <td>{vnet['subscription_name']}</td>
            <td>{vnet['resource_group']}</td>
            <td>{vnet['location']}</td>
            <td>{address_spaces_display}</td>
            <td class='subnet-count'>{vnet['subnet_count']}</td>
            <td class='subnets-detail'>{subnets_html}</td>
            <td><span class='status-badge status-{vnet['provisioning_state'].lower()}'>{vnet['provisioning_state']}</span></td>
            <td>{tags_display}</td>
        </tr>
        """)
    
    return table_rows


def _generate_subnets_html(vnet):
    """Generate HTML for subnet details including NSG rules."""
    if not vnet['subnets']:
        return ""
    
    subnets_html = "<div class='subnets-container'>"
    
    for subnet in vnet['subnets']:
        # Generate badges for NSG and Route Table
        nsg_badge = f"<span class='nsg-badge'>{subnet['network_security_group']}</span>" if subnet['network_security_group'] else ""
        rt_badge = f"<span class='rt-badge'>{subnet['route_table']}</span>" if subnet['route_table'] else ""
        
        # Generate NSG rules HTML
        nsg_rules_html = _generate_nsg_rules_html(vnet, subnet)
        
        subnets_html += f"""
        <div class='subnet-item'>
            <div class='subnet-main'>
                <strong>{subnet['name']}</strong>
                <span class='subnet-prefix'>{subnet['address_prefix']}</span>
                {nsg_badge}
                {rt_badge}
            </div>
            {nsg_rules_html}
        </div>
        """
    
    subnets_html += "</div>"
    return subnets_html


def _generate_nsg_rules_html(vnet, subnet):
    """Generate HTML for NSG rules within a subnet."""
    if not subnet.get('nsg_rules'):
        return ""
    
    rules_count = len(subnet['nsg_rules'])
    subnet_id = f"subnet_{vnet['name'].replace(' ', '_')}_{subnet['name'].replace(' ', '_')}"
    
    nsg_rules_html = f"<div class='nsg-rules-container'>"
    nsg_rules_html += f"""
    <div class='nsg-rules-header' onclick='toggleNSGRules("{subnet_id}")'>
        <span class='toggle-icon' id='icon_{subnet_id}'>▼</span>
        NSG Rules ({rules_count})
    </div>
    """
    nsg_rules_html += f"<div class='nsg-rules-list' id='{subnet_id}' style='display: none;'>"
    
    # Sort rules by direction (Inbound first) then by priority
    sorted_rules = sorted(subnet['nsg_rules'], key=lambda x: (x['direction'] != 'Inbound', x['priority']))
    
    current_direction = None
    for rule in sorted_rules:
        rule_class = "default-rule" if rule['is_default'] else "custom-rule"
        access_class = "allow" if rule['access'] == "Allow" else "deny"
        direction_icon = "→" if rule['direction'] == "Inbound" else "←"
        
        # Add direction separator
        if current_direction != rule['direction']:
            if current_direction is not None:
                nsg_rules_html += "<div class='direction-separator'></div>"
            nsg_rules_html += f"<div class='direction-header'>{direction_icon} {rule['direction']} Rules</div>"
            current_direction = rule['direction']
        
        # Format source and destination for better readability
        source_display = rule['source'] if rule['source'] != '*' else 'Any'
        dest_display = rule['destination'] if rule['destination'] != '*' else 'Any'
        source_port_display = rule['source_port'] if rule['source_port'] != '*' else 'Any'
        dest_port_display = rule['destination_port'] if rule['destination_port'] != '*' else 'Any'
        
        nsg_rules_html += f"""
        <div class='nsg-rule {rule_class}'>
            <div class='rule-header'>
                <div class='rule-main-info'>
                    <span class='rule-name'>{rule['name']}</span>
                    <span class='rule-priority'>Priority: {rule['priority']}</span>
                </div>
                <div class='rule-status'>
                    <span class='rule-access {access_class}'>{rule['access']}</span>
                    <span class='rule-direction'>{direction_icon} {rule['direction']}</span>
                </div>
            </div>
            <div class='rule-flow'>
                <div class='flow-section'>
                    <div class='flow-label'>Source</div>
                    <div class='flow-value'>{source_display}</div>
                    <div class='flow-port'>Port: {source_port_display}</div>
                </div>
                <div class='flow-arrow'>
                    <span class='protocol-badge'>{rule['protocol']}</span>
                    <div class='arrow-line'></div>
                </div>
                <div class='flow-section'>
                    <div class='flow-label'>Destination</div>
                    <div class='flow-value'>{dest_display}</div>
                    <div class='flow-port'>Port: {dest_port_display}</div>
                </div>
            </div>
        </div>
        """
    
    nsg_rules_html += "</div></div>"
    return nsg_rules_html


def _generate_tags_display(tags):
    """Generate HTML for tags display."""
    if not tags:
        return ""
    
    tags_display = "<div class='tags-container'>"
    for key, value in tags.items():
        tags_display += f"<span class='tag'>{key}: {value}</span>"
    tags_display += "</div>"
    
    return tags_display


def _generate_html_content(stats, table_rows, cloud_region="global"):
    """Generate the complete HTML content for the report."""
    from datetime import datetime
    
    return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Virtual Networks Report - {cloud_region}</title>
    <link rel="stylesheet" href="vnet/vnet_report.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Azure Virtual Networks Report</h1>
            <p>Comprehensive overview of VNets and Subnets across your Azure subscriptions</p>
            <p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{stats['total_vnets']}</div>
                <div class="stat-label">Virtual Networks</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats['total_subnets']}</div>
                <div class="stat-label">Total Subnets</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats['subscriptions']}</div>
                <div class="stat-label">Subscriptions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats['locations']}</div>
                <div class="stat-label">Locations</div>
            </div>
        </div>
        
        <div class="search-container">
            <input type="text" class="search-box" id="searchInput" placeholder="🔍 Search VNets by name, subscription, resource group, location, address spaces, status, tags, or subnets...">
        </div>
        
        <div class="table-container">
            <table id="vnetsTable">
                <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">VNet Name</th>
                        <th class="sortable" onclick="sortTable(1)">Subscription</th>
                        <th class="sortable" onclick="sortTable(2)">Resource Group</th>
                        <th class="sortable" onclick="sortTable(3)">Location</th>
                        <th class="sortable" onclick="sortTable(4)">Address Spaces</th>
                        <th class="sortable" onclick="sortTable(5)">Subnets</th>
                        <th>Subnet Details</th>
                        <th class="sortable" onclick="sortTable(7)">Status</th>
                        <th>Tags</th>
                    </tr>
                </thead>
                <tbody>
                    {"".join(table_rows)}
                </tbody>
            </table>
            <div id="noResults" class="no-results" style="display: none;">
                No VNets found matching your search criteria. Try searching by VNet name, subscription, resource group, location, address spaces, status, tags, or subnet details.
            </div>
        </div>
    </div>

    <script src="vnet/vnet_report.js"></script>
</body>
</html>"""


# Contains AI-generated edits.
