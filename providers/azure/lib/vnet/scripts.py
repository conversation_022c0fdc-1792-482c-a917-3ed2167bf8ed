#!/usr/bin/env python3
"""
Azure VNet HTML Report JavaScript

This module contains the JavaScript code for Azure VNet HTML reports.
"""


def get_vnet_report_javascript():
    """Return the JavaScript code for Azure VNet HTML reports."""
    return """
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                const table = document.getElementById('vnetsTable');
                const tbody = table.getElementsByTagName('tbody')[0];
                const rows = tbody.getElementsByTagName('tr');
                const noResults = document.getElementById('noResults');
                let visibleRows = 0;
                
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    const cells = row.getElementsByTagName('td');
                    let found = false;
                    
                    // Search across all columns including address spaces, status, tags, and subnet details
                    for (let j = 0; j < cells.length; j++) {
                        if (cells[j]) {
                            // Get all text content including nested elements and normalize whitespace
                            const cellText = (cells[j].textContent || cells[j].innerText || '').toLowerCase().replace(/\\s+/g, ' ').trim();
                            if (cellText.includes(searchTerm)) {
                                found = true;
                                break;
                            }
                        }
                    }
                    
                    if (found) {
                        row.style.display = '';
                        visibleRows++;
                    } else {
                        row.style.display = 'none';
                    }
                }
                
                // Show/hide no results message
                noResults.style.display = visibleRows === 0 ? 'block' : 'none';
                tbody.style.display = visibleRows === 0 ? 'none' : '';
            });
            
            // Toggle NSG Rules functionality
            function toggleNSGRules(subnetId) {
                const rulesList = document.getElementById(subnetId);
                const icon = document.getElementById('icon_' + subnetId);
                
                if (rulesList.style.display === 'none') {
                    rulesList.style.display = 'block';
                    icon.textContent = '▲';
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    rulesList.style.display = 'none';
                    icon.textContent = '▼';
                    icon.style.transform = 'rotate(0deg)';
                }
            }
            
            // Sorting functionality
            let sortDirection = []; // Track sort direction for each column
            
            function sortTable(columnIndex) {
                const table = document.getElementById('vnetsTable');
                const tbody = table.getElementsByTagName('tbody')[0];
                const rows = Array.from(tbody.getElementsByTagName('tr'));
                const headers = table.getElementsByTagName('th');
                
                // Initialize sort direction array
                if (sortDirection.length === 0) {
                    sortDirection = new Array(headers.length).fill(0); // 0 = unsorted, 1 = asc, -1 = desc
                }
                
                // Determine sort direction
                sortDirection[columnIndex] = sortDirection[columnIndex] === 1 ? -1 : 1;
                
                // Update header classes
                for (let i = 0; i < headers.length; i++) {
                    headers[i].classList.remove('sort-asc', 'sort-desc');
                }
                headers[columnIndex].classList.add(sortDirection[columnIndex] === 1 ? 'sort-asc' : 'sort-desc');
                
                // Sort rows
                rows.sort((a, b) => {
                    const aValue = a.getElementsByTagName('td')[columnIndex]?.textContent || '';
                    const bValue = b.getElementsByTagName('td')[columnIndex]?.textContent || '';
                    
                    // Handle numeric sorting for subnet count
                    if (columnIndex === 5) {
                        return (parseInt(aValue) - parseInt(bValue)) * sortDirection[columnIndex];
                    }
                    
                    // String comparison
                    return aValue.localeCompare(bValue) * sortDirection[columnIndex];
                });
                
                // Reappend sorted rows
                rows.forEach(row => tbody.appendChild(row));
            }
            
            // Initialize table with initial sort by VNet name
            document.addEventListener('DOMContentLoaded', function() {
                sortTable(0);
            });
            """

# Contains AI-generated edits.
