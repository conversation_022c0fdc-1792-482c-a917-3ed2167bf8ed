"""
This module contains the HTML template for the Azure User Role Analysis Report.
External CSS and JavaScript files are now used instead of inline styles and scripts.
"""

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure User Role Analysis Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {{
            theme: {{
                extend: {{
                    colors: {{
                        'azure-blue': '#0078d4',
                        'azure-blue-dark': '#106ebe',
                        'azure-blue-light': '#40e0d0'
                    }}
                }}
            }}
        }}
    </script>
    <style>
        /* Custom styles for Azure-specific elements */
        .sort-asc::after {{ content: ' ↑'; color: #fbbf24; }}
        .sort-desc::after {{ content: ' ↓'; color: #fbbf24; }}
        .hover-row:hover {{ background-color: #f3f4f6; }}
        .role-tag {{ 
            display: inline-block; 
            margin: 2px; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 0.75rem; 
            background-color: #e5e7eb; 
            color: #374151; 
        }}
        .entra-role {{ background-color: #dbeafe; color: #1e40af; }}
        .rbac-role {{ background-color: #dcfce7; color: #166534; }}
        .no-roles {{ color: #9ca3af; font-style: italic; }}
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 p-5">
    <div class="max-w-7xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-azure-blue to-azure-blue-dark text-white p-8 text-center">
            <h1 class="text-4xl font-light mb-2">🔐 Azure User Role Analysis</h1>
            <p class="text-lg opacity-90">Generated on {generation_date}</p>
        </div>
        
        <!-- Statistics Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 p-8 bg-gray-50">
            <div class="bg-white p-6 rounded-lg shadow-sm border-l-4 border-azure-blue text-center">
                <div class="text-3xl font-bold text-azure-blue">{total_users}</div>
                <div class="text-gray-600 text-sm mt-2">Total Users</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border-l-4 border-azure-blue text-center">
                <div class="text-3xl font-bold text-azure-blue">{users_with_entra_roles}</div>
                <div class="text-gray-600 text-sm mt-2">Users with Entra ID Roles</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border-l-4 border-azure-blue text-center">
                <div class="text-3xl font-bold text-azure-blue">{users_with_rbac_roles}</div>
                <div class="text-gray-600 text-sm mt-2">Users with RBAC Roles</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border-l-4 border-azure-blue text-center">
                <div class="text-3xl font-bold text-azure-blue">{users_with_both}</div>
                <div class="text-gray-600 text-sm mt-2">Users with Both Role Types</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm border-l-4 border-azure-blue text-center">
                <div class="text-3xl font-bold text-azure-blue">{unique_rbac_roles}</div>
                <div class="text-gray-600 text-sm mt-2">Unique RBAC Roles</div>
            </div>
        </div>
        
        <!-- Table Container -->
        <div class="p-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">User Role Details</h2>
            
            <!-- Search Box -->
            <input 
                type="text" 
                id="searchBox" 
                class="w-full p-3 border border-gray-300 rounded-lg mb-6 focus:ring-2 focus:ring-azure-blue focus:border-transparent outline-none" 
                placeholder="🔍 Search users, roles, or scopes..."
            >
            
            <!-- Filter Buttons -->
            <div class="flex flex-wrap gap-2 mb-6">
            <!-- Filter Buttons -->
            <div class="flex flex-wrap gap-2 mb-6">
                <button class="px-4 py-2 bg-azure-blue text-white rounded-lg hover:bg-azure-blue-dark transition-colors filter-btn active" onclick="filterTable('all')">
                    All Users
                </button>
                <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors filter-btn" onclick="filterTable('entra')">
                    Entra ID
                </button>
                <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors filter-btn" onclick="filterTable('rbac')">
                    RBAC
                </button>
                <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors filter-btn" onclick="filterTable('no-roles')">
                    No Roles
                </button>
            </div>
            
            <!-- User Table -->
            <div class="overflow-x-auto rounded-lg border border-gray-200">
                <table id="userTable" class="w-full">
                    <thead class="bg-azure-blue text-white">
                        <tr>
                            <th class="w-12 px-4 py-4 text-left font-medium cursor-pointer hover:bg-azure-blue-dark transition-colors sortable select-none" onclick="sortTable(0)">
                                #
                            </th>
                            <th class="w-1/5 px-4 py-4 text-left font-medium cursor-pointer hover:bg-azure-blue-dark transition-colors sortable select-none" onclick="sortTable(1)">
                                User
                            </th>
                            <th class="w-2/5 px-4 py-4 text-left font-medium cursor-pointer hover:bg-azure-blue-dark transition-colors sortable select-none" onclick="sortTable(2)">
                                Entra ID Directory Roles
                            </th>
                            <th class="w-2/5 px-4 py-4 text-left font-medium cursor-pointer hover:bg-azure-blue-dark transition-colors sortable select-none" onclick="sortTable(3)">
                                RBAC Roles by Resource Type
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {user_rows}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="bg-gray-50 px-8 py-6 text-center text-gray-600 border-t">
            <p class="mb-1">📊 Azure User Role Analysis Tool - China Azure Environment</p>
            <p class="text-sm">This report shows user permissions across Entra ID Directory Roles and Azure RBAC roles</p>
        </div>
    </div>

    <script src="user/user_report.js"></script>
</body>
</html>
"""

# Contains AI-generated edits.
