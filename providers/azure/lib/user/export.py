#!/usr/bin/env python3
"""
Data export functionality for user and role information.
"""

import json
import logging
from datetime import datetime
from typing import List, Dict

logger = logging.getLogger(__name__)


def export_users_rbac_to_json(users, filename="users_rbac_roles.json"):
    """
    Export users with all their roles to a JSON file.
    
    Args:
        users: List of user dictionaries with all role information
        filename: Output filename
    """
    try:
        # Prepare data for export
        export_data = {
            "metadata": {
                "total_users": len(users),
                "export_date": str(datetime.now()),
                "environment": "China Azure"
            },
            "users": []
        }
        
        for user in users:
            user_export = {
                "id": user.get("id"),
                "displayName": user.get("displayName"),
                "userPrincipalName": user.get("userPrincipalName"),
                "mail": user.get("mail"),
                "accountEnabled": user.get("accountEnabled"),
                "userType": user.get("userType"),
                "jobTitle": user.get("jobTitle"),
                "department": user.get("department"),
                "companyName": user.get("companyName"),
                "createdDateTime": user.get("createdDateTime"),
                "usageLocation": user.get("usageLocation"),
                "city": user.get("city"),
                "state": user.get("state"),
                "country": user.get("country"),
                "entra_roles": user.get("roles", []),
                "rbac_roles": user.get("rbac_roles", {
                    "management_groups": {},
                    "subscriptions": {},
                    "resource_groups": {}
                })
            }
            export_data["users"].append(user_export)
        
        # Write to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Successfully exported {len(users)} users to {filename}")
        print(f"\n📄 Export completed successfully:")
        print(f"   📂 File: {filename}")
        print(f"   👥 Users exported: {len(users)}")
        print(f"   📊 File size: {get_file_size(filename)}")
        return True
        
    except Exception as e:
        error_msg = f"Failed to export users to JSON: {str(e)}"
        logger.error(f"❌ {error_msg}")
        print(f"❌ Export failed: {error_msg}")
        return False


def get_file_size(filename):
    """
    Get human-readable file size.
    
    Args:
        filename: Path to the file
        
    Returns:
        Human-readable file size string
    """
    try:
        import os
        size_bytes = os.path.getsize(filename)
        
        if size_bytes < 1024:
            return f"{size_bytes} bytes"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    except Exception:
        return "Unknown"

# Contains AI-generated edits.
