#!/usr/bin/env python3
"""
RBAC role management and assignment operations for both Global and China Azure.
"""

import logging
import asyncio
import aiohttp
import httpx
import time
from typing import List, Dict, Tuple, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Import cloud configuration and token management
from ..common.config import get_current_cloud_info
from ..common.token_manager import get_cached_token
from ..common.azure_resources import get_resource_groups

logger = logging.getLogger(__name__)


def get_rbac_role_assignments(credential, scope: str) -> List[Dict]:
    """
    Get RBAC role assignments for a given scope.
    
    Args:
        credential: Azure credential object
        scope: The scope to check (e.g., /subscriptions/{id}, /providers/Microsoft.Management/managementGroups/{id})
        
    Returns:
        List of role assignment dictionaries
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        # Get access token for the appropriate Management API using cached token
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            logger.error("❌ Failed to get access token for Azure Management")
            return []
        
        # Construct the role assignments endpoint
        base_url = cloud_config['management_endpoint']
        endpoint = f"{base_url}{scope}/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        # Make request with SSL handling
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            data = response.json()
            assignments = data.get("value", [])
            # Filter out inherited role assignments; only keep direct assignments for this scope
            filtered = [a for a in assignments if a.get("properties", {}).get("scope") == scope]
            return filtered
        else:
            logger.warning(f"⚠️ Failed to get role assignments for scope {scope}: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting role assignments for scope {scope}: {str(e)}")
        return []


def get_role_definition(credential, role_definition_id: str) -> Optional[Dict]:
    """
    Get role definition details by ID.
    
    Args:
        credential: Azure credential object
        role_definition_id: Full resource ID of the role definition
        
    Returns:
        Role definition dictionary or None
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            return None
        
        endpoint = f"{cloud_config['management_endpoint']}{role_definition_id}?api-version=2022-04-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            return response.json()
        else:
            return None
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting role definition {role_definition_id}: {str(e)}")
        return None


async def get_rbac_role_assignments_async(session: aiohttp.ClientSession, headers: Dict, scope: str, cloud_config: Dict) -> Tuple[str, List[Dict]]:
    """
    Async version: Get RBAC role assignments for a given scope.
    
    Args:
        session: aiohttp session
        headers: HTTP headers with authorization
        scope: The scope to check
        cloud_config: Cloud configuration dictionary
        
    Returns:
        tuple: (scope, list of role assignment dictionaries)
    """
    try:
        base_url = cloud_config['management_endpoint']
        endpoint = f"{base_url}{scope}/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01"
        
        async with session.get(endpoint, headers=headers, ssl=False) as response:
            if response.status == 200:
                data = await response.json()
                assignments = data.get("value", [])
                # Filter out inherited role assignments; only keep direct assignments for this scope
                filtered = [a for a in assignments if a.get("properties", {}).get("scope") == scope]
                return scope, filtered
            else:
                logger.warning(f"⚠️ Failed to get role assignments for scope {scope}: HTTP {response.status}")
                return scope, []
                
    except Exception as e:
        logger.warning(f"⚠️ Error getting role assignments for scope {scope}: {str(e)}")
        return scope, []


async def get_role_definition_async(session: aiohttp.ClientSession, headers: Dict, role_definition_id: str, cloud_config: Dict) -> Tuple[str, Optional[Dict]]:
    """
    Async version: Get role definition details by ID.
    
    Args:
        session: aiohttp session
        headers: HTTP headers with authorization
        role_definition_id: Full resource ID of the role definition
        cloud_config: Cloud configuration dictionary
        
    Returns:
        tuple: (role_definition_id, role definition dictionary or None)
    """
    try:
        endpoint = f"{cloud_config['management_endpoint']}{role_definition_id}?api-version=2022-04-01"
        
        async with session.get(endpoint, headers=headers, ssl=False) as response:
            if response.status == 200:
                data = await response.json()
                return role_definition_id, data
            else:
                return role_definition_id, None
                
    except Exception as e:
        logger.warning(f"⚠️ Error getting role definition {role_definition_id}: {str(e)}")
        return role_definition_id, None


def get_user_rbac_roles(credential, principal_id: str, subscriptions: List[Dict], management_groups: List[Dict]) -> Dict:
    """
    Get all RBAC role assignments for a specific principal across different scopes.
    If the async function returns an awaitable, run it via asyncio.run; otherwise, return directly (useful for testing).
    """
    try:
        result = get_user_rbac_roles_async(credential, principal_id, subscriptions, management_groups)
        if asyncio.iscoroutine(result):
            return asyncio.run(result)
        return result
    except Exception:
        # Fallback: direct call in restricted or test environments
        # Ensure synchronous return by running coroutine
        return asyncio.run(get_user_rbac_roles_async(credential, principal_id, subscriptions, management_groups))


async def get_user_rbac_roles_async(credential, principal_id: str, subscriptions: List[Dict], management_groups: List[Dict]) -> Dict:
    """
    Async version: Get all RBAC role assignments for a specific principal across different scopes.
    """
    rbac_roles = {
        "management_groups": {},
        "subscriptions": {},
        "resource_groups": {}
    }
    
    # Get cloud configuration
    detected_cloud, cloud_config = get_current_cloud_info()
    
    # Get management token using cached token
    mg_token = get_cached_token(credential, cloud_config['management_scope'])
    if not mg_token or not mg_token.token:
        logger.warning("⚠️ Failed to get management token for RBAC roles")
        return rbac_roles
    
    mg_headers = {
        "Authorization": f"Bearer {mg_token.token}",
        "Content-Type": "application/json"
    }
    
    # Create aiohttp session with optimized settings for high concurrency
    connector = aiohttp.TCPConnector(
        limit=25,  # Increased total connection pool size for better parallelism
        limit_per_host=12,  # Increased per-host limit for Azure API endpoints
        ssl=False,
        keepalive_timeout=30,  # Keep connections alive longer
        enable_cleanup_closed=True
    )
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=aiohttp.ClientTimeout(total=60, connect=10)  # Optimized timeouts
    ) as session:
        # Collect all scopes to check with optimized batching
        scopes_info = []
        
        # Add management groups (these are typically fewer, so add them directly)
        for mg in management_groups:
            mg_id = mg.get("id", "")
            mg_name = mg.get("properties", {}).get("displayName", mg.get("name", "Unknown"))
            if mg_id:
                scopes_info.append(("management_group", mg_id, mg_name))
        
        # Add subscriptions (these are the main scopes we care about)
        for sub in subscriptions:
            sub_id = sub.get("id", "")
            sub_name = sub.get("displayName", sub.get("name", "Unknown"))
            if sub_id:
                scopes_info.append(("subscription", sub_id, sub_name))
        
        # Add resource groups (sample first few to avoid too many requests) - parallel discovery
        logger.info(f"🔍 Discovering resource groups across {min(3, len(subscriptions))} subscriptions in parallel...")
        
        async def get_resource_groups_for_subscription(sub_info):
            """Get resource groups for a subscription asynchronously."""
            subscription_id = sub_info.get("subscriptionId", "")
            sub_name = sub_info.get("displayName", sub_info.get("name", "Unknown"))
            if not subscription_id:
                return []
            
            try:
                # Import here to avoid circular imports
                from ..common.azure_resources import get_resource_groups
                resource_groups = get_resource_groups(credential, subscription_id)
                rg_scopes = []
                for rg in resource_groups[:3]:  # Limit to first 3 RGs per subscription
                    rg_id = rg.get("id", "")
                    rg_name = rg.get("name", "Unknown")
                    if rg_id:
                        rg_key = f"{sub_name}/{rg_name}"
                        rg_scopes.append(("resource_group", rg_id, rg_key))
                return rg_scopes
            except Exception as e:
                logger.warning(f"⚠️ Failed to get resource groups for subscription {sub_name}: {str(e)}")
                return []
        
        # Get resource groups for first 3 subscriptions in parallel
        if subscriptions:
            rg_tasks = [
                get_resource_groups_for_subscription(sub)
                for sub in subscriptions[:3]
            ]
            rg_results = await asyncio.gather(*rg_tasks, return_exceptions=True)
            
            for rg_result in rg_results:
                if isinstance(rg_result, Exception):
                    logger.warning(f"⚠️ Resource group discovery failed: {str(rg_result)}")
                    continue
                if isinstance(rg_result, list):
                    scopes_info.extend(rg_result)
        
        logger.info(f"🔍 Checking RBAC roles across {len(scopes_info)} scopes using optimized parallel processing...")
        
        # Create tasks for all scopes with batching to avoid overwhelming the API
        batch_size = 15  # Process scopes in batches to balance speed and API limits
        all_scope_assignments = {}
        
        for i in range(0, len(scopes_info), batch_size):
            batch = scopes_info[i:i + batch_size]
            logger.info(f"🚀 Processing batch {i//batch_size + 1}/{(len(scopes_info) + batch_size - 1)//batch_size} ({len(batch)} scopes)...")
            
            batch_tasks = [
                get_rbac_role_assignments_async(session, mg_headers, scope_id, cloud_config)
                for _, scope_id, _ in batch
            ]
            
            # Execute batch
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process batch results
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    scope_name = batch[j][2] if j < len(batch) else "Unknown"
                    logger.warning(f"⚠️ Failed to get assignments for scope {scope_name}: {str(result)}")
                    continue
                
                if not isinstance(result, tuple) or len(result) != 2:
                    continue
                    
                scope_id, assignments = result
                all_scope_assignments[scope_id] = assignments
        
        # Process all results and collect role definitions to fetch
        role_definitions_needed = set()
        scope_assignments = {}
        
        # Find user assignments across all scopes
        for scope_id, assignments in all_scope_assignments.items():
            user_assignments = [a for a in assignments if a.get("properties", {}).get("principalId") == principal_id]
            
            if user_assignments:
                scope_assignments[scope_id] = user_assignments
                # Collect role definition IDs
                for assignment in user_assignments:
                    role_def_id = assignment.get("properties", {}).get("roleDefinitionId", "")
                    if role_def_id:
                        role_definitions_needed.add(role_def_id)
        
        # Fetch all role definitions in parallel
        role_cache = {}
        if role_definitions_needed:
            logger.info(f"📋 Fetching {len(role_definitions_needed)} role definitions in parallel...")
            role_tasks = [
                get_role_definition_async(session, mg_headers, role_def_id, cloud_config)
                for role_def_id in role_definitions_needed
            ]
            
            role_results = await asyncio.gather(*role_tasks, return_exceptions=True)
            
            for role_result in role_results:
                if isinstance(role_result, Exception):
                    continue
                if not isinstance(role_result, tuple) or len(role_result) != 2:
                    continue
                    
                role_def_id, role_def = role_result
                if role_def and "properties" in role_def:
                    role_name = role_def["properties"].get("roleName", "")
                    if not role_name or role_name.strip() == "":
                        logger.debug(f"Empty role name for role definition {role_def_id}")
                        continue  # Skip roles with empty names
                    role_cache[role_def_id] = role_name.strip()
        
        # Organize results by scope type
        for i, (scope_type, scope_id, scope_name) in enumerate(scopes_info):
            if scope_id in scope_assignments:
                assignments = scope_assignments[scope_id]
                role_names = []
                
                for assignment in assignments:
                    role_def_id = assignment.get("properties", {}).get("roleDefinitionId", "")
                    if role_def_id and role_def_id in role_cache:
                        role_name = role_cache[role_def_id]
                        if role_name:  # Only add non-empty role names
                            role_names.append(role_name)
                
                if scope_type == "management_group":
                    rbac_roles["management_groups"][scope_name] = role_names
                elif scope_type == "subscription":
                    rbac_roles["subscriptions"][scope_name] = role_names
                elif scope_type == "resource_group":
                    rbac_roles["resource_groups"][scope_name] = role_names
    
    return rbac_roles


async def get_users_rbac_roles_parallel(credential, users: List[Dict], subscriptions: List[Dict], management_groups: List[Dict]) -> List[Dict]:
    """
    Get RBAC roles for all users in parallel with enhanced concurrency.
    
    Args:
        credential: Azure credential object
        users: List of user dictionaries with Entra ID roles
        subscriptions: List of subscription dictionaries
        management_groups: List of management group dictionaries
        
    Returns:
        List of user dictionaries with RBAC roles added
    """
    if not users:
        return []
    
    # Increase parallelism for better performance while respecting Azure API limits
    max_workers = min(8, len(users))  # Increased from 5 to 8 for better throughput
    
    def get_rbac_for_user(user_data):
        user, subscriptions, management_groups = user_data
        user_id = user.get('id')
        user_name = user.get('displayName', user.get('userPrincipalName', 'N/A'))
        
        if user_id:
            try:
                logger.info(f"   🔍 Getting RBAC roles for user: {user_name}")
                start_time = time.time()
                rbac_roles = get_user_rbac_roles(credential, user_id, subscriptions, management_groups)
                end_time = time.time()
                
                user['rbac_roles'] = rbac_roles
                
                # Count total roles for logging
                total_roles = (
                    len(rbac_roles.get('management_groups', {})) +
                    len(rbac_roles.get('subscriptions', {})) +
                    len(rbac_roles.get('resource_groups', {}))
                )
                logger.info(f"   ✅ Found {total_roles} RBAC role assignments for {user_name} in {end_time - start_time:.2f}s")
                
            except Exception as e:
                logger.error(f"   ❌ Failed to get RBAC roles for {user_name}: {str(e)}")
                user['rbac_roles'] = {
                    "management_groups": {},
                    "subscriptions": {},
                    "resource_groups": {}
                }
        else:
            logger.warning(f"   ⚠️ No user ID found for {user_name}, skipping RBAC roles")
            user['rbac_roles'] = {
                "management_groups": {},
                "subscriptions": {},
                "resource_groups": {}
            }
        return user
    
    # Prepare data for parallel processing
    user_data_list = [(user, subscriptions, management_groups) for user in users]
    
    logger.info(f"🚀 Processing RBAC roles for {len(users)} users with {max_workers} parallel workers...")
    
    # Process users in parallel using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_user = {
            executor.submit(get_rbac_for_user, user_data): i 
            for i, user_data in enumerate(user_data_list)
        }
        
        # Collect results as they complete
        users_with_rbac = [None] * len(users)  # type: List[Optional[Dict]]
        completed = 0
        total_start_time = time.time()
        
        for future in as_completed(future_to_user):
            try:
                user_index = future_to_user[future]
                result_user = future.result(timeout=180)  # Increased timeout to 3 minutes per user
                users_with_rbac[user_index] = result_user
                completed += 1
                
                # Progress logging with ETA
                elapsed = time.time() - total_start_time
                if completed > 0:
                    avg_time_per_user = elapsed / completed
                    remaining = len(users) - completed
                    eta = remaining * avg_time_per_user
                    logger.info(f"   📊 Progress: {completed}/{len(users)} users completed (ETA: {eta:.1f}s)")
                
            except Exception as e:
                user_index = future_to_user[future]
                user_name = user_data_list[user_index][0].get('displayName', 'Unknown')
                logger.error(f"   ❌ Failed to process RBAC for user {user_name}: {str(e)}")
                
                # Use original user data without RBAC roles
                original_user = user_data_list[user_index][0].copy()
                original_user['rbac_roles'] = {
                    "management_groups": {},
                    "subscriptions": {},
                    "resource_groups": {}
                }
                users_with_rbac[user_index] = original_user
                completed += 1
    
    total_time = time.time() - total_start_time
    logger.info(f"🎉 Completed RBAC processing for all {len(users)} users in {total_time:.2f} seconds")
    
    # Filter out any None values (shouldn't happen, but safety check)
    return [user for user in users_with_rbac if user is not None]

# Contains AI-generated edits.
