#!/usr/bin/env python3
"""
HTML export functionality for Azure User Role Analysis Tool.
"""

import os
import webbrowser
import logging
from datetime import datetime
from typing import Dict, List, Any

from .html_template import HTML_TEMPLATE
from .html_data_transformer import create_user_rows, get_report_stats
from ..common.web_server import find_available_port, start_web_server

logger = logging.getLogger(__name__)


def generate_html_report(users_data: List[Dict[str, Any]], 
                        output_file: str = "azure_user_report.html",
                        cloud_region: str = "global",
                        use_web_server: bool = True,
                        keep_server_alive: bool = True) -> str:
    """
    Generate an HTML report from users data and open it in browser.
    
    Args:
        users_data: List of user data dictionaries
        output_file: Base name for the output file
        cloud_region: Azure cloud region (global or china)
        use_web_server: Whether to serve the report using a web server
        keep_server_alive: Whether to keep the server alive after opening the browser
        
    Returns:
        Path to the generated HTML file
    """
    try:
        # Ensure the html directory exists
        html_dir = "html"
        if not os.path.exists(html_dir):
            os.makedirs(html_dir)
            logger.info(f"Created HTML output directory: {html_dir}")
        
        # Create user subdirectory if it doesn't exist
        user_dir = os.path.join(html_dir, "user")
        if not os.path.exists(user_dir):
            os.makedirs(user_dir)
            logger.info(f"Created User assets directory: {user_dir}")
        
        # Format the output filename to include the cloud region
        if output_file:
            base_name = os.path.splitext(os.path.basename(output_file))[0]
        else:
            base_name = "azure_user_report"
        
        final_filename = f"{base_name}_{cloud_region}.html"
        
        # Ensure output file is placed in html directory
        if not final_filename.startswith("html/"):
            output_file = os.path.join(html_dir, final_filename)
        
        abs_path = os.path.abspath(output_file)
        
        html_content = _create_html_content(users_data)
        
        with open(abs_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report generated: {abs_path}")
        
        if use_web_server:
            try:
                port = find_available_port()
                server_thread = start_web_server(abs_path, port, daemon=not keep_server_alive)
                
                # Get the actual port (in case it was auto-assigned)
                actual_port = getattr(server_thread, 'actual_port', port)
                html_filename = os.path.basename(abs_path)
                web_url = f"http://127.0.0.1:{actual_port}/{html_filename}"
                
                try:
                    webbrowser.open(web_url)
                    logger.info(f"HTML report served at: {web_url}")
                    print(f"📄 HTML report generated: {abs_path}")
                    print(f"🌐 Web server started at: {web_url}")
                except Exception as e:
                    logger.warning(f"Could not automatically open web browser: {e}")
                    print(f"📄 HTML report generated: {abs_path}")
                    print(f"🌐 Web server started at: {web_url}")
                    print(f"💡 Please open this URL in your browser manually")
                
                if keep_server_alive:
                    print("💡 The server will keep running. Press Ctrl+C to stop.")
                    try:
                        server_thread.join()
                    except KeyboardInterrupt:
                        print("\n⏹️ Server stopped by user")
                        return abs_path
                else:
                    print("💡 Server is running as a background process.")
            except KeyboardInterrupt:
                logger.info("Server startup interrupted by user")
                return abs_path
            except Exception as e:
                logger.warning(f"Could not start web server: {e}, falling back to file:// URL")
                use_web_server = False
        # Do NOT open the file in the browser if use_web_server is False
        if not use_web_server:
            print(f"📄 HTML report generated: {abs_path}")
            print("🌐 Open this file in your browser to view the report")
        
        return abs_path
        
    except Exception as e:
        logger.error(f"Error in generate_html_report: {str(e)}")
        raise

# Contains AI-generated edits.


def _create_html_content(users_data: List[Dict[str, Any]]) -> str:
    """Create the HTML content for the report."""
    
    stats = get_report_stats(users_data)
    user_rows = create_user_rows(users_data)
    
    return HTML_TEMPLATE.format(
        generation_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        total_users=stats["total_users"],
        users_with_entra_roles=stats["users_with_entra_roles"],
        users_with_rbac_roles=stats["users_with_rbac_roles"],
        users_with_both=stats["users_with_both"],
        unique_rbac_roles=stats["unique_rbac_roles"],
        user_rows=user_rows
    )

# Contains AI-generated edits.
