#!/usr/bin/env python3
"""
Display and formatting utilities for user and role information.
"""

import logging
from tabulate import tabulate
from typing import List, Dict
from collections import Counter

logger = logging.getLogger(__name__)


def display_users_with_roles(users):
    """
    Display user information including both Entra roles and RBAC roles (if available) in a table format.
    
    Args:
        users: List of user dictionaries from Microsoft Graph API with roles (and optionally RBAC roles)
    """
    if not users:
        print("📝 No users to display")
        return
    
    # Check if users have RBAC role data
    has_rbac_data = any(user.get('rbac_roles') for user in users)
    
    if has_rbac_data:
        print(f"\n👥 Found {len(users)} Entra ID Users with Directory and RBAC Roles:")
        display_users_with_all_roles_table(users)
        return
    
    # Original display for Entra-only data
    print(f"\n👥 Found {len(users)} Entra ID Users with Directory Roles from China Azure:")
    print("=" * 140)
    
    # Prepare table data
    table_data = []
    headers = [
        "No.",
        "Display Name", 
        "User Principal Name",
        "Mail",
        "Account Enabled",
        "User Type",
        "Entra ID Roles"
    ]
    
    for i, user in enumerate(users, 1):
        # Truncate long values for better table display
        def truncate(value, max_len=30):
            if value and len(str(value)) > max_len:
                return str(value)[:max_len-3] + "..."
            return value or "N/A"
        
        # Format roles - show first few roles, truncate if too many
        roles = user.get('roles', [])
        if roles:
            if len(roles) <= 3:
                roles_str = "\n".join(roles)
            else:
                roles_str = f"{chr(10).join(roles[:3])}\n+{len(roles)-3} more"
            roles_display = roles_str
        else:
            roles_display = "No roles"
        
        row = [
            i,
            truncate(user.get('displayName'), 25),
            truncate(user.get('userPrincipalName'), 35),
            truncate(user.get('mail'), 30),
            "✅" if user.get('accountEnabled') is True else "❌",
            truncate(user.get('userType', 'N/A'), 15),
            roles_display
        ]
        table_data.append(row)
    
    # Print the table
    print(tabulate(table_data, headers=headers, tablefmt="grid", stralign="left"))
    
    # Print summary statistics
    print("=" * 140)
    total_users = len(users)
    enabled_users = sum(1 for user in users if user.get('accountEnabled') is True)
    disabled_users = total_users - enabled_users
    users_with_roles = sum(1 for user in users if user.get('roles', []))
    
    print(f"📊 Summary Statistics:")
    print(f"   Total Users: {total_users}")
    print(f"   Enabled Users: {enabled_users} ✅")
    print(f"   Disabled Users: {disabled_users} ❌")
    print(f"   Users with Entra ID Roles: {users_with_roles} 🔑")
    print(f"   Users without Roles: {total_users - users_with_roles}")
    
    # User type breakdown
    user_types = {}
    for user in users:
        user_type = user.get('userType', 'Unknown')
        user_types[user_type] = user_types.get(user_type, 0) + 1
    
    if user_types:
        print(f"   User Types:")
        for user_type, count in user_types.items():
            print(f"     - {user_type}: {count}")
    
    # Role statistics
    all_roles = []
    for user in users:
        all_roles.extend(user.get('roles', []))
    
    if all_roles:
        role_counts = Counter(all_roles)
        print(f"   Most Common Entra ID Roles:")
        for role, count in role_counts.most_common(5):
            print(f"     - {role}: {count} users")
    
    print("=" * 140)


def display_users_with_all_roles_table(users):
    """
    Display user information including both Entra ID roles and RBAC roles in a comprehensive table format.
    
    Args:
        users: List of user dictionaries with roles and RBAC roles
    """
    if not users:
        print("📝 No users to display")
        return
    
    print(f"\n👥 Found {len(users)} Entra ID Users with Directory and RBAC Roles (Complete List):")
    print("=" * 250)
    
    # Prepare table data
    table_data = []
    headers = [
        "No.",
        "Display Name", 
        "User Principal Name",
        "Mail",
        "Enabled",
        "User Type",
        "Entra ID Roles",
        "RBAC Roles (All Scopes)"
    ]
    
    for i, user in enumerate(users, 1):
        # Truncate long values for better table display
        def truncate(value, max_len=25):
            if value and len(str(value)) > max_len:
                return str(value)[:max_len-3] + "..."
            return value or "N/A"
        
        # Format Entra ID roles - show ALL roles without truncation
        entra_roles = user.get('roles', [])
        if entra_roles:
            entra_roles_str = "\n".join(entra_roles)
        else:
            entra_roles_str = "No roles"
        
        # Format RBAC roles from all scopes - show complete names without truncation
        rbac_roles = user.get('rbac_roles', {})
        rbac_display_parts = []
        
        # Management Group roles
        mg_roles = rbac_roles.get('management_groups', {})
        for mg_name, roles in mg_roles.items():
            for role in roles:
                rbac_display_parts.append(f"🏢 MG: {mg_name} → {role}")
        
        # Subscription roles
        sub_roles = rbac_roles.get('subscriptions', {})
        for sub_name, roles in sub_roles.items():
            for role in roles:
                rbac_display_parts.append(f"📋 SUB: {sub_name} → {role}")
        
        # Resource Group roles
        rg_roles = rbac_roles.get('resource_groups', {})
        for rg_path, roles in rg_roles.items():
            for role in roles:
                rbac_display_parts.append(f"📁 RG: {rg_path} → {role}")
        
        # Format RBAC roles display - show ALL roles without truncation
        if rbac_display_parts:
            rbac_roles_str = "\n".join(rbac_display_parts)
        else:
            rbac_roles_str = "No RBAC roles"
        
        row = [
            i,
            truncate(user.get('displayName'), 20),
            truncate(user.get('userPrincipalName'), 30),
            truncate(user.get('mail'), 25),
            "✅" if user.get('accountEnabled') is True else "❌",
            truncate(user.get('userType', 'N/A'), 10),
            entra_roles_str,
            rbac_roles_str
        ]
        table_data.append(row)
    
    # Print the table with better formatting for multi-line content
    print(tabulate(table_data, headers=headers, tablefmt="fancy_grid", stralign="left", maxcolwidths=[None, 25, 35, 30, None, 15, 40, 60]))
    
    # Print detailed summary statistics
    print("=" * 250)
    total_users = len(users)
    enabled_users = sum(1 for user in users if user.get('accountEnabled') is True)
    disabled_users = total_users - enabled_users
    
    # Count users with different types of roles
    users_with_entra_roles = sum(1 for u in users if u.get('roles', []))
    users_with_rbac_roles = sum(1 for u in users if any([
        u.get('rbac_roles', {}).get('management_groups', {}),
        u.get('rbac_roles', {}).get('subscriptions', {}),
        u.get('rbac_roles', {}).get('resource_groups', {})
    ]))
    users_with_both_roles = sum(1 for u in users if u.get('roles', []) and any([
        u.get('rbac_roles', {}).get('management_groups', {}),
        u.get('rbac_roles', {}).get('subscriptions', {}),
        u.get('rbac_roles', {}).get('resource_groups', {})
    ]))
    
    print(f"📊 Summary Statistics:")
    print(f"   Total Users: {total_users}")
    print(f"   Enabled Users: {enabled_users} ✅")
    print(f"   Disabled Users: {disabled_users} ❌")
    print(f"   Users with Entra ID roles: {users_with_entra_roles} 🔑")
    print(f"   Users with RBAC roles: {users_with_rbac_roles} 🔐")
    print(f"   Users with both types of roles: {users_with_both_roles} 🔑🔐")
    print(f"   Users with no roles: {total_users - max(users_with_entra_roles, users_with_rbac_roles)}")
    
    # User type breakdown
    user_types = {}
    for user in users:
        user_type = user.get('userType', 'Unknown')
        user_types[user_type] = user_types.get(user_type, 0) + 1
    
    if user_types:
        print(f"   User Types:")
        for user_type, count in user_types.items():
            print(f"     - {user_type}: {count}")
    
    # Role statistics
    all_entra_roles = []
    all_rbac_roles = []
    
    for user in users:
        all_entra_roles.extend(user.get('roles', []))
        
        rbac_roles = user.get('rbac_roles', {})
        for scope_type in ['management_groups', 'subscriptions', 'resource_groups']:
            for scope_name, roles in rbac_roles.get(scope_type, {}).items():
                all_rbac_roles.extend(roles)
    
    if all_entra_roles:
        entra_role_counts = Counter(all_entra_roles)
        print(f"   Most Common Entra ID Roles:")
        for role, count in entra_role_counts.most_common(5):
            print(f"     - {role}: {count} users")
    
    if all_rbac_roles:
        rbac_role_counts = Counter(all_rbac_roles)
        print(f"   Most Common RBAC Roles:")
        for role, count in rbac_role_counts.most_common(5):
            print(f"     - {role}: {count} users")
    
    print("=" * 250)


def display_user_details(user, index=None):
    """
    Display detailed information for a single user including roles.
    
    Args:
        user: User dictionary from Microsoft Graph API
        index: Optional index number for display
    """
    title = f"User Details" + (f" (#{index})" if index else "")
    print(f"\n🔍 {title}:")
    print("=" * 50)
    
    details = [
        ("📧 Display Name", user.get('displayName')),
        ("📨 User Principal Name", user.get('userPrincipalName')),
        ("🆔 Object ID", user.get('id')),
        ("✉️ Mail", user.get('mail')),
        ("📞 Job Title", user.get('jobTitle')),
        ("🏢 Department", user.get('department')),
        ("🏢 Company Name", user.get('companyName')),
        ("📱 Mobile Phone", user.get('mobilePhone')),
        ("☎️ Business Phone", user.get('businessPhones', [])),
        ("🌍 Account Enabled", "✅ Yes" if user.get('accountEnabled') is True else "❌ No"),
        ("👤 User Type", user.get('userType')),
        ("🗓️ Created Date", user.get('createdDateTime')),
        ("🔄 Last Sign-in", user.get('signInActivity', {}).get('lastSignInDateTime') if user.get('signInActivity') else None),
        ("🌍 Usage Location", user.get('usageLocation')),
        ("🏙️ City", user.get('city')),
        ("🏛️ State", user.get('state')),
        ("🌍 Country", user.get('country')),
    ]
    
    for label, value in details:
        if value:
            if isinstance(value, list):
                value = ", ".join(value) if value else "N/A"
            print(f"   {label}: {value}")
        else:
            print(f"   {label}: N/A")
    
    # Display roles separately with better formatting
    roles = user.get('roles', [])
    print(f"   🔑 Entra Roles:")
    if roles:
        for i, role in enumerate(roles, 1):
            print(f"     {i}. {role}")
    else:
        print(f"     No directory roles assigned")
    
    print("=" * 50)


def debug_user_data(users):
    """
    Debug function to check the actual user data structure.
    
    Args:
        users: List of user dictionaries from Microsoft Graph API
    """
    if users:
        print("\n🔍 DEBUG: Checking user data structure:")
        print("=" * 80)
        
        # Check first few users for accountEnabled values
        for i, user in enumerate(users[:3], 1):
            print(f"\n--- User {i}: {user.get('displayName', 'N/A')} ---")
            
            # Key fields to check
            key_fields = ['accountEnabled', 'userType', 'id', 'displayName', 'userPrincipalName']
            for field in key_fields:
                value = user.get(field)
                print(f"   {field}: {value} (type: {type(value).__name__})")
            
            # Specifically analyze accountEnabled
            account_enabled = user.get('accountEnabled')
            print(f"   accountEnabled analysis:")
            print(f"     - Raw: {repr(account_enabled)}")
            print(f"     - Bool: {bool(account_enabled)}")
            print(f"     - Is True: {account_enabled is True}")
            print(f"     - Is False: {account_enabled is False}")
            print(f"     - Is None: {account_enabled is None}")
        
        print("=" * 80)
