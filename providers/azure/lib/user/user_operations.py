#!/usr/bin/env python3
"""
Main user operations orchestrating Entra ID and RBAC role retrieval.
"""

import logging
import asyncio
import time
from typing import List, Dict, Tuple


logger = logging.getLogger(__name__)
# Import Azure credential retrieval for default credential
# Import Azure credential retrieval for default credential
# Import Azure credential retrieval for default credential
from ..common.auth import get_azure_credential
from .entra_users import get_entra_users_with_roles_impl


def get_entra_users_with_roles(credential=None):
    """
    Wrapper function to get Entra ID users with roles using default credential.
    This function imports the implementation from entra_users module.
    
    Args:
        credential: Optional Azure credential object. If None, default credential will be used.
    
    Returns:
        tuple: (success: bool, users_with_roles: list, error_message: str)
    """
    try:
        if credential is None:
            credential = get_azure_credential()
        # Dynamically resolve implementation to allow patching
        impl = globals().get('get_entra_users_with_roles_impl')
        return impl(credential)
    except Exception as e:
        logger.error(f"❌ Failed to get users with Entra ID roles: {str(e)}")
        return False, [], str(e)


def get_entra_users_with_rbac_roles(credential=None):
    """
    Wrapper function to get Entra ID users with RBAC roles using default credential.
    
    Returns:
        tuple: (success: bool, users_with_all_roles: list, error_message: str)
    """
    try:
        if credential is None:
            credential = get_azure_credential()
        impl = globals().get('get_entra_users_with_rbac_roles_impl')
        return impl(credential)
    except Exception as e:
        logger.error(f"❌ Failed to get users with RBAC roles: {str(e)}")
        return False, [], str(e)


def get_entra_users_with_rbac_roles_impl(credential):
    """
    Retrieve Entra ID users with their directory roles and RBAC roles at all scopes using parallel processing.
    
    Args:
        credential: Azure credential object
        
    Returns:
        tuple: (success: bool, users_with_all_roles: list, error_message: str)
    """
    try:
        # Import modules locally to avoid circular imports
        from .entra_users import get_entra_users_with_roles_impl
        from ..common.azure_resources import get_subscriptions, get_management_groups
        from .rbac import get_users_rbac_roles_parallel
        
        # First get users with their Entra ID roles
        logger.info("👥 Getting Entra ID users with directory roles...")
        success, users, error_message = get_entra_users_with_roles_impl(credential)
        if not success:
            return False, [], error_message
        
        # Get all accessible subscriptions and management groups
        logger.info("🔍 Getting Azure scopes (subscriptions and management groups)...")
        subscriptions = get_subscriptions(credential)
        management_groups = get_management_groups(credential)
        
        # For each user, get their RBAC roles in parallel (enhanced parallelization)
        logger.info("🔐 Getting RBAC roles for each user across all scopes with enhanced parallel processing...")
        start_time = time.time()
        
        # Enhanced parallel processing with better progress tracking
        users_with_all_roles = asyncio.run(get_users_rbac_roles_parallel(credential, users, subscriptions, management_groups))
        
        end_time = time.time()
        total_rbac_assignments = sum(
            len(user.get('rbac_roles', {}).get('management_groups', {})) +
            len(user.get('rbac_roles', {}).get('subscriptions', {})) +
            len(user.get('rbac_roles', {}).get('resource_groups', {}))
            for user in users_with_all_roles
        )
        
        logger.info(f"✅ Successfully retrieved {len(users_with_all_roles)} users with {total_rbac_assignments} total RBAC assignments in {end_time - start_time:.2f} seconds")
        logger.info(f"📊 Performance: {total_rbac_assignments/(end_time - start_time):.1f} RBAC assignments/second")
        return True, users_with_all_roles, None
        
    except Exception as e:
        error_msg = f"Failed to retrieve users with RBAC roles: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, [], error_msg

# Contains AI-generated edits.
