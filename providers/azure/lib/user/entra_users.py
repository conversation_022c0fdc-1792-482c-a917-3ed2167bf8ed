#!/usr/bin/env python3
"""
Entra ID user operations and role retrieval for both Global and China Azure.

This module has been upgraded to use the new msgraph-core SDK pattern (>=1.0.0)
as recommended by Microsoft, replacing the legacy msgraph-sdk approach.
The new pattern provides better async support, authentication flexibility,
and aligns with modern Azure SDK practices using Kiota-based request adapters.
"""

import logging
import asyncio
import aiohttp
import requests
import time
from typing import List, Dict, Tuple, Optional
from msal import ConfidentialClientApplication

# New Microsoft Graph SDK imports
from azure.identity import EnvironmentCredential
from msgraph_core.authentication import AzureIdentityAuthenticationProvider
from msgraph_core import BaseGraphRequestAdapter
from kiota_abstractions.request_information import RequestInformation
from kiota_abstractions.method import Method

# Import cloud configuration and token management
from ..common.config import get_current_cloud_info
from ..common.token_manager import get_cached_token
from ..common.auth import get_azure_credential

logger = logging.getLogger(__name__)


def get_graph_token_with_msal(tenant_id, client_id, client_secret, cloud="global"):
    """
    Get Microsoft Graph API token using MSAL library.
    Supports both Global and China Azure clouds.
    
    Args:
        tenant_id: Azure tenant ID
        client_id: Application client ID
        client_secret: Application client secret
        cloud: Cloud environment ("global" or "china")
        
    Returns:
        tuple: (token, graph_endpoint)
    """
    # Get cloud configuration from config module
    from .common.config import get_cloud_config
    cloud_config = get_cloud_config(cloud)
    
    authority = f"{cloud_config['authority_base']}/{tenant_id}"
    scope = [cloud_config['graph_scope']]
    graph_endpoint = cloud_config['graph_endpoint']

    app = ConfidentialClientApplication(
        client_id, authority=authority, client_credential=client_secret
    )
    result = app.acquire_token_for_client(scopes=scope)
    
    if not result or "access_token" not in result:
        error_desc = result.get('error_description', 'Unknown error') if result else 'No result returned'
        raise Exception(f"Failed to acquire token: {error_desc}")
    
    return result["access_token"], graph_endpoint


def get_entra_users(credential):
    """
    Retrieve Entra ID (Azure AD) users using the new msgraph-core SDK pattern.
    This demonstrates the recommended approach from msgraph-core >=1.0.0.
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        logger.info(f"👥 Retrieving Entra ID users from {cloud_config['name']} using new SDK pattern...")
        
        # For multi-cloud compatibility, use direct credential approach
        # This avoids the AzureIdentityAuthenticationProvider cloud configuration issues
        
        # Build request information using the new SDK pattern
        req = RequestInformation()
        req.url = f"{cloud_config['graph_endpoint']}/v1.0/users"
        req.http_method = Method.GET
        
        # Add query parameters
        select_fields = "id,displayName,userPrincipalName,mail,jobTitle,department,accountEnabled,userType,companyName,mobilePhone,businessPhones,createdDateTime,usageLocation,city,state,country"
        req.query_parameters = {
            "$select": select_fields,
            "$top": "999"
        }
        
        logger.info(f"🔗 Making request using new SDK to: {req.url}")
        logger.debug(f"🌐 Using {detected_cloud} Azure cloud environment")
        
        # Get the token using the credential directly (works across all Azure clouds)
        token = credential.get_token(cloud_config['graph_scope'])
        if not token or not token.token:
            raise Exception("Failed to get access token for Microsoft Graph")
        
        # Build the complete URL with query parameters
        import urllib.parse
        
        if req.query_parameters:
            query_string = urllib.parse.urlencode(req.query_parameters)
            full_url = f"{req.url}?{query_string}"
        else:
            full_url = req.url
        
        # Use requests for the actual HTTP call with proper headers
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json",
            "User-Agent": "CloudQX-Azure-Tool/1.0"
        }
        
        # Use requests with proper SSL handling for China Azure
        try:
            response = requests.get(full_url, headers=headers, verify=True, timeout=30)
        except requests.exceptions.SSLError as ssl_error:
            # Handle SSL verification issues (common in corporate environments)
            logger.warning(f"⚠️ SSL verification failed: {ssl_error}")
            logger.info("🔄 Retrying with SSL verification disabled...")
            response = requests.get(full_url, headers=headers, verify=False, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            users = data.get("value", [])
            logger.info(f"✅ Successfully retrieved {len(users)} Entra ID users using new SDK structure")
            return True, users, None
        else:
            error_msg = f"HTTP {response.status_code}: {response.text}"
            logger.error(f"❌ Failed to retrieve users: {error_msg}")
            return False, [], error_msg
            
    except Exception as e:
        error_msg = f"Failed to retrieve Entra ID users: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, [], error_msg


def get_user_roles(credential, user_id):
    """
    Get directory roles assigned to a specific user using the new SDK pattern.
    Supports both Global and China Azure clouds with proper endpoint configuration.
    
    Args:
        credential: Azure credential object
        user_id: User's object ID
        
    Returns:
        list: List of role names assigned to the user
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        # Build request information using the new SDK pattern (without problematic auth provider)
        req = RequestInformation()
        req.url = f"{cloud_config['graph_endpoint']}/v1.0/roleManagement/directory/roleAssignments"
        req.http_method = Method.GET
        req.query_parameters = {
            "$filter": f"principalId eq '{user_id}'"
        }
        
        # Get access token for the appropriate Graph API using cached token
        token = get_cached_token(credential, cloud_config['graph_scope'])
        
        if not token or not token.token:
            logger.warning(f"⚠️ Failed to get access token for user {user_id}")
            return []
        
        # Build the complete URL with query parameters
        import urllib.parse
        
        if req.query_parameters:
            query_string = urllib.parse.urlencode(req.query_parameters)
            full_url = f"{req.url}?{query_string}"
        else:
            full_url = req.url
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json",
            "User-Agent": "CloudQX-Azure-Tool/1.0"
        }
        
        # Use requests instead of httpx for simpler SSL handling
        try:
            response = requests.get(full_url, headers=headers, verify=True, timeout=30)
        except requests.exceptions.SSLError:
            # Handle SSL verification issues
            logger.debug(f"🔄 SSL verification failed for user {user_id}, retrying without verification")
            response = requests.get(full_url, headers=headers, verify=False, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            role_assignments = data.get("value", [])
            
            # Get role definitions for each assignment
            role_names = []
            for assignment in role_assignments:
                role_definition_id = assignment.get('roleDefinitionId')
                if role_definition_id:
                    role_name = get_role_definition_name(credential, role_definition_id, cloud_config)
                    if role_name:
                        role_names.append(role_name)
            
            return role_names
        else:
            logger.warning(f"⚠️ Failed to get roles for user {user_id}: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting roles for user {user_id}: {str(e)}")
        return []


def get_role_definition_name(credential, role_definition_id, cloud_config) -> Optional[str]:
    """
    Get the display name for a role definition ID using the new SDK pattern.
    
    Args:
        credential: Azure credential object
        role_definition_id: Role definition ID
        cloud_config: Cloud configuration dictionary
        
    Returns:
        str: Role display name or None if not found
    """
    try:
        # Build request information using the new SDK pattern (without problematic auth provider)
        req = RequestInformation()
        req.url = f"{cloud_config['graph_endpoint']}/v1.0/roleManagement/directory/roleDefinitions/{role_definition_id}"
        req.http_method = Method.GET
        
        token = get_cached_token(credential, cloud_config['graph_scope'])
        if not token or not token.token:
            return None
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json",
            "User-Agent": "CloudQX-Azure-Tool/1.0"
        }
        
        try:
            response = requests.get(req.url, headers=headers, verify=True, timeout=30)
        except requests.exceptions.SSLError:
            response = requests.get(req.url, headers=headers, verify=False, timeout=30)
        
        if response.status_code == 200:
            role_data = response.json()
            role_name = role_data.get('displayName', '').strip()
            return role_name if role_name else None  # Return None for empty role names
        
        return None
    except Exception:
        return None


async def get_user_roles_async(session: aiohttp.ClientSession, headers: Dict, user_id: str, cloud_config: Dict) -> Tuple[str, List[str]]:
    """
    Async version: Get directory roles assigned to a specific user using role management endpoint.
    Supports both Global and China Azure clouds with proper endpoint configuration.
    
    Args:
        session: aiohttp session with proper base_url configured
        headers: HTTP headers with authorization
        user_id: User's object ID
        cloud_config: Cloud configuration dictionary
        
    Returns:
        tuple: (user_id, list of role names)
    """
    try:
        # Use role management endpoint for more accurate role information
        roles_endpoint = f"/v1.0/roleManagement/directory/roleAssignments"
        params = {
            "$filter": f"principalId eq '{user_id}'"
        }
        
        # Set SSL based on cloud environment - China Azure often requires SSL=False
        ssl_verify = False if 'china' in cloud_config.get('name', '').lower() else True
        
        async with session.get(roles_endpoint, headers=headers, params=params, ssl=ssl_verify) as response:
            if response.status == 200:
                data = await response.json()
                role_assignments = data.get("value", [])
                
                # Get role definitions for each assignment
                role_names = []
                for assignment in role_assignments:
                    role_definition_id = assignment.get('roleDefinitionId')
                    if role_definition_id:
                        role_name = await get_role_definition_name_async(session, headers, role_definition_id, cloud_config)
                        if role_name:  # Only add non-empty role names
                            role_names.append(role_name)
                
                return user_id, role_names
            else:
                logger.warning(f"⚠️ Failed to get roles for user {user_id}: HTTP {response.status}")
                return user_id, []
                
    except Exception as e:
        logger.warning(f"⚠️ Error getting roles for user {user_id}: {str(e)}")
        return user_id, []


async def get_role_definition_name_async(session: aiohttp.ClientSession, headers: Dict, role_definition_id: str, cloud_config: Dict) -> Optional[str]:
    """
    Async version: Get the display name for a role definition ID.
    
    Args:
        session: aiohttp session
        headers: HTTP headers
        role_definition_id: Role definition ID
        cloud_config: Cloud configuration dictionary
        
    Returns:
        str: Role display name or None if not found
    """
    try:
        role_def_endpoint = f"/v1.0/roleManagement/directory/roleDefinitions/{role_definition_id}"
        ssl_verify = False if 'china' in cloud_config.get('name', '').lower() else True
        
        async with session.get(role_def_endpoint, headers=headers, ssl=ssl_verify) as response:
            if response.status == 200:
                role_data = await response.json()
                role_name = role_data.get('displayName', '').strip()
                return role_name if role_name else None  # Return None for empty role names
        
        return None
    except Exception:
        return None


def get_entra_users_with_roles_impl(credential):
    """
    Retrieve Entra ID users with their assigned roles using parallel processing.
    Supports both Global and China Azure clouds with proper endpoint configuration.
    
    Args:
        credential: Azure credential object
        
    Returns:
        tuple: (success: bool, users_with_roles: list, error_message: str)
    """
    try:
        # Get cloud configuration first
        detected_cloud, cloud_config = get_current_cloud_info()
        
        logger.info(f"👥 Retrieving Entra ID users with roles from {cloud_config['name']}...")
        
        # First get the users
        success, users, error_message = get_entra_users(credential)
        if not success:
            return False, [], error_message
        
        logger.info("🔑 Fetching role assignments for each user in parallel...")
        start_time = time.time()
        
        # Run parallel role fetching
        users_with_roles = asyncio.run(get_users_roles_parallel(credential, users))
        
        end_time = time.time()
        logger.info(f"✅ Successfully retrieved {len(users_with_roles)} users with role information in {end_time - start_time:.2f} seconds")
        return True, users_with_roles, None
        
    except Exception as e:
        error_msg = f"Failed to retrieve users with roles: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, [], error_msg


async def get_users_roles_parallel(credential, users: List[Dict]) -> List[Dict]:
    """
    Get roles for all users in parallel using async requests.
    Supports both Global and China Azure clouds with proper endpoint configuration.
    
    Args:
        credential: Azure credential object
        users: List of user dictionaries
        
    Returns:
        List of user dictionaries with roles added
    """
    # Get cloud configuration
    detected_cloud, cloud_config = get_current_cloud_info()
    
    # Get access token using cached token
    token = get_cached_token(credential, cloud_config['graph_scope'])
    if not token or not token.token:
        # Fallback to sequential processing if token fails
        logger.warning("⚠️ Failed to get token for parallel processing, falling back to sequential")
        return await get_users_roles_sequential(credential, users)
    
    headers = {
        "Authorization": f"Bearer {token.token}",
        "Content-Type": "application/json",
        "User-Agent": "CloudQX-Azure-Tool/1.0"
    }
    
    # Configure SSL based on cloud environment
    ssl_verify = False if 'china' in cloud_config.get('name', '').lower() else True
    
    # Create aiohttp session with proper base URL and connection limits
    connector = aiohttp.TCPConnector(
        limit=10,  # Total connection limit
        limit_per_host=5,  # Per-host connection limit
        ssl=ssl_verify
    )
    
    async with aiohttp.ClientSession(
        base_url=cloud_config['graph_endpoint'],  # Configure base URL from cloud config
        connector=connector,
        timeout=aiohttp.ClientTimeout(total=30),  # 30 second timeout
        headers={"User-Agent": "CloudQX-Azure-Tool/1.0"}
    ) as session:
        # Create tasks for all users with valid IDs
        tasks = []
        user_id_to_index = {}
        
        for i, user in enumerate(users):
            user_id = user.get('id')
            if user_id:
                tasks.append(get_user_roles_async(session, headers, user_id, cloud_config))
                user_id_to_index[user_id] = i
                logger.debug(f"   Queued role fetch for user {i+1}/{len(users)}: {user.get('displayName', 'N/A')}")
        
        # Execute all tasks concurrently with progress tracking
        logger.info(f"🚀 Executing {len(tasks)} parallel requests for Entra ID roles on {cloud_config['name']}...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                logger.warning(f"⚠️ Task failed with exception: {str(result)}")
                continue
            
            if not isinstance(result, tuple) or len(result) != 2:
                logger.warning(f"⚠️ Unexpected result format: {type(result)}")
                continue
                
            user_id, roles = result
            if user_id in user_id_to_index:
                index = user_id_to_index[user_id]
                users[index]['roles'] = roles
        
        # Set empty roles for users without IDs or failed requests
        for user in users:
            if 'roles' not in user:
                user['roles'] = []
    
    return users


async def get_users_roles_sequential(credential, users: List[Dict]) -> List[Dict]:
    """
    Fallback sequential processing for getting user roles.
    """
    users_with_roles = []
    
    for i, user in enumerate(users, 1):
        user_id = user.get('id')
        if user_id:
            logger.info(f"   Getting roles for user {i}/{len(users)}: {user.get('displayName', 'N/A')}")
            roles = get_user_roles(credential, user_id)
            user['roles'] = roles
        else:
            user['roles'] = []
        
        users_with_roles.append(user)
    
    return users_with_roles


def get_entra_users_with_roles():
    """
    Wrapper function to get Entra ID users with roles using default credential.
    
    Returns:
        tuple: (success: bool, users_with_roles: list, error_message: str)
    """
    try:
        credential = get_azure_credential()
        return get_entra_users_with_roles_impl(credential)
    except Exception as e:
        logger.error(f"❌ Failed to get Entra ID users with roles: {e}")
        return False, [], str(e)


async def get_entra_users_async_sdk(credential):
    """
    Fully async Entra ID users retrieval using the new msgraph-core SDK pattern.
    This is the complete implementation following the new SDK approach.
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        logger.info(f"👥 Retrieving Entra ID users from {cloud_config['name']} using async new SDK pattern...")
        
        # Build request information using the new SDK pattern (without problematic auth provider)
        req = RequestInformation()
        req.url = f"{cloud_config['graph_endpoint']}/v1.0/users"
        req.http_method = Method.GET
        
        # Add query parameters
        select_fields = "id,displayName,userPrincipalName,mail,jobTitle,department,accountEnabled,userType,companyName,mobilePhone,businessPhones,createdDateTime,usageLocation,city,state,country"
        req.query_parameters = {
            "$select": select_fields,
            "$top": "999"
        }
        
        logger.info(f"🔗 Making async request using new SDK to: {req.url}")
        
        # Use the new SDK's async capabilities with fallback approach
        # Since we don't have custom model classes yet, we'll use the SDK structure but fall back to HTTP
        logger.info("🔧 Using new SDK structure with HTTP fallback for compatibility")
        
        # Get token using the authentication provider
        token = credential.get_token(cloud_config['graph_scope'])
        if not token or not token.token:
            raise Exception("Failed to get access token for Microsoft Graph")
        
        import urllib.parse
        query_string = urllib.parse.urlencode(req.query_parameters)
        full_url = f"{req.url}?{query_string}"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json",
            "User-Agent": "CloudQX-Azure-Tool/1.0-NewSDK"
        }
        
        import aiohttp
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(full_url, headers=headers, ssl=True) as response:
                    if response.status == 200:
                        data = await response.json()
                        users = data.get("value", [])
                        logger.info(f"✅ Successfully retrieved {len(users)} users using new SDK structure")
                        return True, users, None
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ HTTP request failed: {response.status} - {error_text}")
                        return False, [], f"HTTP {response.status}: {error_text}"
            except aiohttp.ClientSSLError:
                # Retry without SSL verification for China Azure
                logger.info("🔄 Retrying without SSL verification...")
                async with session.get(full_url, headers=headers, ssl=False) as response:
                    if response.status == 200:
                        data = await response.json()
                        users = data.get("value", [])
                        logger.info(f"✅ Successfully retrieved {len(users)} users using new SDK (no SSL)")
                        return True, users, None
                    else:
                        error_text = await response.text()
                        return False, [], f"HTTP {response.status}: {error_text}"
                            
    except Exception as e:
        error_msg = f"Failed to retrieve Entra ID users with async SDK: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, [], error_msg

# Contains AI-generated edits.
