"""
This module handles the transformation of user data for the HTML report.
"""

import logging
from typing import Dict, List, Any, DefaultDict, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)

def _convert_rbac_roles_to_list(rbac_roles_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert nested RBAC roles dictionary to a flat list format.
    """
    if not rbac_roles_dict or not isinstance(rbac_roles_dict, dict):
        return []
    
    roles_list = []
    
    mg_roles = rbac_roles_dict.get('management_groups', {})
    for mg_id, roles in mg_roles.items():
        if isinstance(roles, list):
            for role in roles:
                roles_list.append({
                    'role_name': role,
                    'scope': f'/providers/Microsoft.Management/managementGroups/{mg_id}'
                })
    
    sub_roles = rbac_roles_dict.get('subscriptions', {})
    for sub_id, roles in sub_roles.items():
        if isinstance(roles, list):
            for role in roles:
                roles_list.append({
                    'role_name': role,
                    'scope': f'/subscriptions/{sub_id}'
                })
    
    rg_roles = rbac_roles_dict.get('resource_groups', {})
    for scope, roles in rg_roles.items():
        if isinstance(roles, list):
            for role in roles:
                # Convert display format to proper Azure resource scope
                # scope format is "{sub_name}/{rg_name}", need "/subscriptions/{sub_id}/resourceGroups/{rg_name}"
                if '/' in scope:
                    # Extract resource group name from display format
                    rg_name = scope.split('/')[-1]
                    # Create a proper-looking Azure resource group scope for classification
                    # Note: We use a placeholder for subscription ID since we only need it for resource type detection
                    azure_scope = f"/subscriptions/placeholder/resourceGroups/{rg_name}"
                else:
                    # Fallback to original scope if format is unexpected
                    azure_scope = scope
                
                roles_list.append({
                    'role_name': role,
                    'scope': azure_scope,
                    'display_scope': scope  # Keep original for display purposes
                })
    
    return roles_list

def _group_rbac_roles_by_type(rbac_roles: List[Dict[str, Any]]) -> Dict[str, Dict[str, List[str]]]:
    """
    Group RBAC roles by role name, then by resource type with deduplication.
    """
    grouped_roles: DefaultDict[str, DefaultDict[str, List[str]]] = defaultdict(lambda: defaultdict(list))
    for role in rbac_roles:
        role_name = role.get('role_name', '').strip()
        scope = role.get('scope', '')
        display_scope = role.get('display_scope', scope)  # Use display_scope if available
        
        # Skip invalid role names
        if not role_name or role_name in ['Unknown Role', 'Role Data Missing', 'N/A']:
            logger.debug(f"Skipping invalid role name: '{role_name}' for scope: {scope}")
            continue
        
        resource_type = "Unknown"
        if '/managementGroups/' in scope:
            resource_type = "Management Groups"
        elif '/subscriptions/' in scope and '/resourceGroups/' not in scope:
            resource_type = "Subscriptions"
        elif '/resourceGroups/' in scope:
            resource_type = "Resource Groups"
            
        # Store the display scope for better presentation
        if display_scope not in grouped_roles[role_name][resource_type]:
            grouped_roles[role_name][resource_type].append(display_scope)
        
    # Convert defaultdict to dict for type compatibility
    return {k: dict(v) for k, v in grouped_roles.items()}

def _format_grouped_rbac_roles_for_html(grouped_roles: Dict[str, Dict[str, List[str]]]) -> str:
    """
    Format grouped RBAC roles for HTML display using Tailwind CSS.
    """
    if not grouped_roles:
        return '<span class="no-roles text-gray-500 italic">No RBAC roles</span>'

    html = ""
    for role_name, resource_types in grouped_roles.items():
        html += f'<div class="mb-4 p-3 bg-gray-50 rounded-lg border">'
        html += f'<div class="font-semibold text-gray-800 mb-2">{role_name}</div>'
        
        for resource_type, scopes in resource_types.items():
            html += f'<div class="ml-2 mb-2">'
            html += f'<span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-md mr-2">{resource_type}</span>'
            html += f'<span class="text-gray-600 text-sm">({len(scopes)} scopes)</span>'
            
            if len(scopes) > 3:
                html += f'<div class="mt-1 cursor-pointer text-azure-blue hover:text-azure-blue-dark text-sm" onclick="toggleExpand(this)">'
                html += f'<span class="expand-arrow inline-block transform transition-transform">▶</span>'
                html += f'<span class="expand-text ml-1">Show all</span>'
                html += f'</div>'
                html += f'<div class="expandable-scopes overflow-hidden transition-all duration-300" style="max-height: 0;">'
                for scope in scopes:
                    html += f'<div class="ml-4 text-sm text-gray-600 py-1">{scope}</div>'
                html += f'</div>'
            else:
                for scope in scopes:
                    html += f'<div class="ml-4 text-sm text-gray-600 py-1">{scope}</div>'
            
            html += f'</div>'
        html += f'</div>'
        
    return html

def _format_roles_for_html(roles: List[Dict[str, Any]], role_type: str) -> str:
    """Format roles for HTML display with deduplication and better error handling."""
    if not roles:
        role_type_name = "Entra ID" if role_type == "entra" else "RBAC"
        return f'<span class="no-roles">No {role_type_name} roles</span>'
    
    role_names = set()  # Use set to automatically deduplicate
    for role in roles:
        if isinstance(role, str):
            role_name = role.strip()
        elif isinstance(role, dict):
            # Try multiple keys for role name with better fallback
            role_name = (role.get('role_name') or 
                        role.get('displayName') or 
                        role.get('name') or 
                        'Role Data Missing')
            role_name = role_name.strip() if role_name else 'Role Data Missing'
        else:
            logger.warning(f"Unexpected role type: {type(role)}, value: {role}")
            role_name = str(role).strip()
        
        # Skip empty or invalid role names
        if role_name and role_name not in ['Unknown Role', 'Role Data Missing', 'N/A', '']:
            role_names.add(role_name)
    
    if not role_names:
        role_type_name = "Entra ID" if role_type == "entra" else "RBAC"
        return f'<span class="no-roles text-gray-500 italic">No valid {role_type_name} roles</span>'
    
    # Sort role names for consistent display
    sorted_roles = sorted(list(role_names))
    role_badges = [f'<span class="role-tag {role_type}-role">{role_name}</span>' 
                   for role_name in sorted_roles]
    
    return ' '.join(role_badges)

def _format_scope_for_display(scope: str) -> str:
    """Format Azure resource scope for better display in HTML."""
    if not scope:
        return "Unknown Scope"
    
    parts = scope.split('/')
    if '/resourceGroups/' in scope:
        try:
            rg_index = parts.index('resourceGroups')
            return parts[rg_index + 1]
        except (ValueError, IndexError):
            return "Resource Group"
    elif '/subscriptions/' in scope:
        try:
            sub_index = parts.index('subscriptions')
            return parts[sub_index + 1]
        except (ValueError, IndexError):
            return "Subscription"
    elif '/managementGroups/' in scope:
        try:
            mg_index = parts.index('managementGroups')
            return parts[mg_index + 1]
        except (ValueError, IndexError):
            return "Management Group"
    
    return parts[-1] if parts else scope

def create_user_rows(users_data: List[Dict[str, Any]]) -> str:
    """Generate HTML table rows for each user with improved role deduplication."""
    user_rows = ""
    for i, user in enumerate(users_data):
        entra_roles_raw = user.get('entra_roles', user.get('entraIdRoles', user.get('roles', [])))
        rbac_roles_raw = user.get('rbac_roles', user.get('rbacRoles', []))

        # Convert RBAC roles format
        if isinstance(rbac_roles_raw, dict):
            rbac_roles = _convert_rbac_roles_to_list(rbac_roles_raw)
        else:
            rbac_roles = rbac_roles_raw

        # Process Entra roles and deduplicate
        if entra_roles_raw and isinstance(entra_roles_raw[0], str):
            # Remove duplicates and invalid entries
            unique_entra_roles = []
            seen_roles = set()
            for role in entra_roles_raw:
                role_clean = role.strip() if role else ''
                if role_clean and role_clean not in seen_roles and role_clean not in ['Unknown Role', 'N/A', 'Role Data Missing']:
                    seen_roles.add(role_clean)
                    unique_entra_roles.append({'role_name': role_clean})
            entra_roles = unique_entra_roles
        else:
            # Clean existing dict format
            entra_roles = []
            seen_roles = set()
            for role in (entra_roles_raw or []):
                if isinstance(role, dict):
                    role_name = (role.get('role_name') or role.get('displayName') or role.get('name', '')).strip()
                    if role_name and role_name not in seen_roles and role_name not in ['Unknown Role', 'N/A', 'Role Data Missing']:
                        seen_roles.add(role_name)
                        entra_roles.append({'role_name': role_name})

        user_display = f"""
        <div class="space-y-1">
            <div class="font-medium text-gray-900">{user.get('display_name', user.get('displayName', 'N/A'))}</div>
            <div class="text-sm text-gray-500">{user.get('user_principal_name', user.get('userPrincipalName', 'N/A'))}</div>
        </div>
        """
        
        entra_roles_html = _format_roles_for_html(entra_roles, 'entra')
        
        if rbac_roles:
            grouped_rbac_roles = _group_rbac_roles_by_type(rbac_roles)
            rbac_roles_html = _format_grouped_rbac_roles_for_html(grouped_rbac_roles)
        else:
            rbac_roles_html = '<span class="no-roles text-gray-500 italic">No RBAC roles</span>'

        user_rows += f"""
        <tr class="hover-row">
            <td class="px-4 py-3 text-sm text-gray-700">{i + 1}</td>
            <td class="px-4 py-3">{user_display}</td>
            <td class="px-4 py-3">{entra_roles_html}</td>
            <td class="px-4 py-3">{rbac_roles_html}</td>
        </tr>
        """
    return user_rows

def get_report_stats(users_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate statistics for the report header."""
    total_users = len(users_data)
    users_with_entra_roles = 0
    users_with_rbac_roles = 0
    users_with_both = 0
    unique_rbac_roles = set()

    for user in users_data:
        entra_roles = user.get('entra_roles', user.get('entraIdRoles', user.get('roles', [])))
        rbac_roles_raw = user.get('rbac_roles', user.get('rbacRoles', []))

        if isinstance(rbac_roles_raw, dict):
            rbac_roles = _convert_rbac_roles_to_list(rbac_roles_raw)
        else:
            rbac_roles = rbac_roles_raw

        has_entra = bool(entra_roles)
        has_rbac = bool(rbac_roles)

        if has_entra:
            users_with_entra_roles += 1
        if has_rbac:
            users_with_rbac_roles += 1
        if has_entra and has_rbac:
            users_with_both += 1
        
        if rbac_roles:
            for role in rbac_roles:
                if isinstance(role, dict):
                    unique_rbac_roles.add(role.get('role_name', 'Unknown'))

    return {
        "total_users": total_users,
        "users_with_entra_roles": users_with_entra_roles,
        "users_with_rbac_roles": users_with_rbac_roles,
        "users_with_both": users_with_both,
        "unique_rbac_roles": len(unique_rbac_roles)
    }

# Contains AI-generated edits.
