#!/usr/bin/env python3
"""
Azure Private DNS and related resources HTML report generator.
"""
import os
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def _calculate_dns_statistics(subs_data):
    """Calculate statistics for DNS private resources data."""
    # Define resource type mappings
    type_labels = {
        'zones': 'Private DNS Zones',
        'vnet_links': 'VNet Links',
        'endpoints': 'Private Endpoints',
        'resolvers': 'Private Resolvers',
        'forwarding_rules': 'Forwarding Rulesets',
        'forwarding_rules_detail': 'Forwarding Rules'
    }
    
    stats = {
        'total_resources': 0,
        'subscriptions': len(subs_data),
        'locations': set(),
        'resource_counts': {}
    }
    
    # Initialize resource counts
    for rtype in type_labels.keys():
        stats['resource_counts'][rtype] = 0
    
    # Calculate totals and collect locations
    for sub in subs_data:
        for rtype in type_labels.keys():
            resources = sub.get(rtype, [])
            count = len(resources)
            stats['resource_counts'][rtype] += count
            stats['total_resources'] += count
            
            # Collect unique locations
            for res in resources:
                location = res.get('location', '')
                if location:
                    stats['locations'].add(location)
    
    stats['locations'] = len(stats['locations'])
    return stats

def generate_dns_private_html_report(subs_data, output_file="azure_dns_private_report.html", cloud_region="global"):
    html_dir = "html"
    if not os.path.exists(html_dir):
        os.makedirs(html_dir)
        logger.info(f"Created HTML output directory: {html_dir}")
    
    # Create dns_private subdirectory if it doesn't exist
    dns_private_dir = os.path.join(html_dir, "dns_private")
    if not os.path.exists(dns_private_dir):
        os.makedirs(dns_private_dir)
        logger.info(f"Created DNS private assets directory: {dns_private_dir}")
    
    base_name = os.path.basename(output_file).split(".")[0]
    final_filename = f"{base_name}_{cloud_region}.html"
    output_path = os.path.join(html_dir, final_filename)

    # Build minimal HTML structure that uses external CSS and JS
    import json
    html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure DNS Private Resources Report - {cloud_region}</title>
    <link rel="stylesheet" href="dns_private/dns_private_report.css">
</head>
<body>
    <div class="container">
        <div class="header">
            🌐 <h1>Azure DNS Private Resources Report</h1>
            <p>Comprehensive overview of Private DNS resources across your Azure subscriptions</p>
            <p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>
        
        <div class="nav-bar" id="nav-container">
            <!-- Navigation will be populated by JavaScript -->
        </div>
        
        <div id="content">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Embed the data as a global variable for the JavaScript to use
        window.dnsPrivateData = {json.dumps(subs_data, indent=2)};
    </script>
    <script src="dns_private/dns_private_report.js"></script>
</body>
</html>'''

    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"Dynamic HTML report generated: {output_path}")
    except Exception as e:
        logger.error(f"Failed to write HTML report: {e}")
        raise
    
    # return the absolute path to the generated report
    return os.path.abspath(output_path)

# Contains AI-generated edits.