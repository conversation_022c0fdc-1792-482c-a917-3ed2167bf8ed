#!/usr/bin/env python3
"""
Azure Private DNS and related resources data retrieval.
"""
import logging
import asyncio
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional
import httpx
from providers.azure.lib.common.config import get_current_cloud_info
from providers.azure.lib.common.token_manager import get_cached_token

logger = logging.getLogger(__name__)

def _list_resources(credential, resource_path: str, api_version: str) -> List[Dict]:
    _, cloud_config = get_current_cloud_info()
    token = get_cached_token(credential, cloud_config['management_scope'])
    if not token or not token.token:
        logger.error("Failed to acquire token for resource listing")
        return []
    endpoint = f"{cloud_config['management_endpoint']}/{resource_path}?api-version={api_version}"
    headers = {"Authorization": f"Bearer {token.token}"}
    logger.debug(f"Making request to: {endpoint}")
    try:
        with httpx.Client() as client:
            resp = client.get(endpoint, headers=headers)
        if resp.status_code == 200:
            data = resp.json().get('value', [])
            logger.debug(f"Successfully retrieved {len(data)} resources from {resource_path}")
            return data
        elif resp.status_code == 404:
            logger.debug(f"Resource type not found (404) for {resource_path} - this may be expected")
            return []
        else:
            logger.error(f"Listing {resource_path} failed: HTTP {resp.status_code}, Response: {resp.text[:200]}")
    except Exception as e:
        logger.error(f"Error listing {resource_path}: {e}")
    return []

def _list_resources_parallel(tasks: List[tuple], credential, max_workers: int = 10) -> Dict[str, List[Dict]]:
    """
    Execute multiple resource listing tasks in parallel.
    
    Args:
        tasks: List of tuples (task_id, resource_path, api_version)
        credential: Azure credential
        max_workers: Maximum number of concurrent workers
    
    Returns:
        Dictionary mapping task_id to list of resources
    """
    results = {}
    
    def fetch_resources(task_id: str, resource_path: str, api_version: str) -> tuple:
        try:
            resources = _list_resources(credential, resource_path, api_version)
            return task_id, resources
        except Exception as e:
            logger.error(f"Error in parallel fetch for {task_id}: {e}")
            return task_id, []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_task = {
            executor.submit(fetch_resources, task_id, resource_path, api_version): task_id
            for task_id, resource_path, api_version in tasks
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_task):
            try:
                task_id, resources = future.result()
                results[task_id] = resources
            except Exception as e:
                task_id = future_to_task[future]
                logger.error(f"Task {task_id} generated an exception: {e}")
                results[task_id] = []
    
    return results

def list_private_dns_zones(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    path = f"subscriptions/{subscription_id}/providers/Microsoft.Network/privateDnsZones"
    zones = _list_resources(credential, path, "2018-09-01")
    
    # Enrich with subscription information and prepare parallel record count tasks
    api_version = "2018-09-01"
    record_count_tasks = []
    
    for zone in zones:
        # Subscription context
        zone['subscription'] = subscription_name or subscription_id
        zone['subscription_id'] = subscription_id
        # Extract resource group from ID if not already present
        if 'resource_group' not in zone and zone.get('id'):
            parts = zone['id'].split('/')
            for i, part in enumerate(parts):
                if part == 'resourceGroups' and i + 1 < len(parts):
                    zone['resource_group'] = parts[i + 1]
                    break
            if 'resource_group' not in zone:
                zone['resource_group'] = 'undefined'
        
        # Prepare task for parallel record count fetching
        if zone.get('name') and zone.get('resource_group'):
            rs_path = (
                f"subscriptions/{subscription_id}/resourceGroups/{zone['resource_group']}"
                f"/providers/Microsoft.Network/privateDnsZones/{zone.get('name')}/recordsets"
            )
            record_count_tasks.append((zone.get('name'), rs_path, api_version))
    
    # Execute record count fetching in parallel
    if record_count_tasks:
        record_results = _list_resources_parallel(record_count_tasks, credential)
        # Apply results back to zones
        zone_map = {zone.get('name'): zone for zone in zones}
        for zone_name, recordsets in record_results.items():
            if zone_name in zone_map:
                zone_map[zone_name]['record_count'] = len(recordsets or [])
    
    # Set default record count for any zones that didn't get processed
    for zone in zones:
        if 'record_count' not in zone:
            zone['record_count'] = 0
    
    return zones

def list_virtual_network_links(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    # Virtual network links are scoped per private DNS zone; list links under each zone
    zones = list_private_dns_zones(credential, subscription_id, subscription_name)
    api_version = "2018-09-01"
    
    # Prepare parallel tasks for fetching links from all zones
    link_tasks = []
    zone_map = {}
    
    for zone in zones:
        zid = zone.get('id', '')
        if not zid:
            continue
        # build resource path: remove leading slash if present
        path = zid.lstrip('/') + '/virtualNetworkLinks'
        task_id = f"zone_{zone.get('name', 'unknown')}"
        link_tasks.append((task_id, path, api_version))
        zone_map[task_id] = zone
    
    # Execute link fetching in parallel
    links = []
    if link_tasks:
        link_results = _list_resources_parallel(link_tasks, credential)
        
        for task_id, items in link_results.items():
            zone = zone_map.get(task_id)
            if not zone:
                continue
                
            for itm in items:
                # annotate with parent zone name
                itm.setdefault('zoneName', zone.get('name'))
                # annotate with virtual network name
                vnet_info = itm.get('properties', {}).get('virtualNetwork', {})
                vnet_id = vnet_info.get('id')
                if vnet_id:
                    # extract VNet name from resource ID
                    itm['vnet'] = vnet_id.rstrip('/').split('/')[-1]
                
                # Enrich with subscription information
                itm['subscription'] = subscription_name or subscription_id
                itm['subscription_id'] = subscription_id
                # Extract resource group from ID if not already present
                if 'resource_group' not in itm and itm.get('id'):
                    parts = itm['id'].split('/')
                    for i, part in enumerate(parts):
                        if part == 'resourceGroups' and i + 1 < len(parts):
                            itm['resource_group'] = parts[i + 1]
                            break
                    if 'resource_group' not in itm:
                        itm['resource_group'] = 'undefined'
                
                links.append(itm)
    
    return links

def list_virtual_network_links_optimized(credential, subscription_id: str, subscription_name: str = '', zones: Optional[List[Dict]] = None) -> List[Dict]:
    """
    Optimized version of list_virtual_network_links that can accept pre-fetched zones data
    to avoid duplicate API calls.
    """
    if zones is None:
        zones = list_private_dns_zones(credential, subscription_id, subscription_name)
    
    api_version = "2018-09-01"
    
    # Prepare parallel tasks for fetching links from all zones
    link_tasks = []
    zone_map = {}
    
    for zone in zones:
        zid = zone.get('id', '')
        if not zid:
            continue
        # build resource path: remove leading slash if present
        path = zid.lstrip('/') + '/virtualNetworkLinks'
        task_id = f"zone_{zone.get('name', 'unknown')}"
        link_tasks.append((task_id, path, api_version))
        zone_map[task_id] = zone
    
    # Execute link fetching in parallel
    links = []
    if link_tasks:
        link_results = _list_resources_parallel(link_tasks, credential)
        
        for task_id, items in link_results.items():
            zone = zone_map.get(task_id)
            if not zone:
                continue
                
            for itm in items:
                # annotate with parent zone name
                itm.setdefault('zoneName', zone.get('name'))
                # annotate with virtual network name
                vnet_info = itm.get('properties', {}).get('virtualNetwork', {})
                vnet_id = vnet_info.get('id')
                if vnet_id:
                    # extract VNet name from resource ID
                    itm['vnet'] = vnet_id.rstrip('/').split('/')[-1]
                
                # Enrich with subscription information
                itm['subscription'] = subscription_name or subscription_id
                itm['subscription_id'] = subscription_id
                # Extract resource group from ID if not already present
                if 'resource_group' not in itm and itm.get('id'):
                    parts = itm['id'].split('/')
                    for i, part in enumerate(parts):
                        if part == 'resourceGroups' and i + 1 < len(parts):
                            itm['resource_group'] = parts[i + 1]
                            break
                    if 'resource_group' not in itm:
                        itm['resource_group'] = 'undefined'
                
                links.append(itm)
    
    return links

def list_private_endpoints(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    """
    List Private Endpoints with detailed properties including network interfaces,
    private link service connections, subnet information, and DNS configuration.
    """
    try:
        from azure.mgmt.network import NetworkManagementClient
        _, cloud_config = get_current_cloud_info()
        client = NetworkManagementClient(
            credential, 
            subscription_id,
            base_url=cloud_config['management_endpoint']
        )
        endpoints = []
        
        for endpoint in client.private_endpoints.list_by_subscription():
            endpoint_dict = endpoint.as_dict()
            logger.debug(f"Processing private endpoint: {endpoint_dict.get('name', 'unnamed')} - {endpoint_dict.get('id', 'no-id')}")
            logger.debug(f"Detailed endpoint object: {endpoint_dict}")
            
            # Enrich with subscription information
            endpoint_dict['subscription'] = subscription_name or subscription_id
            endpoint_dict['subscription_id'] = subscription_id
            
            # Extract resource group from ID if not already present
            if 'resource_group' not in endpoint_dict and endpoint_dict.get('id'):
                parts = endpoint_dict['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        endpoint_dict['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in endpoint_dict:
                    endpoint_dict['resource_group'] = 'undefined'
            
            # Extract and enrich properties - SDK provides flattened data in as_dict()
            # The SDK as_dict() method flattens the structure, so properties are directly in endpoint_dict
            
            # Extract network interface information
            network_interfaces = endpoint_dict.get('network_interfaces', [])
            endpoint_dict['network_interface_count'] = len(network_interfaces)
            endpoint_dict['network_interfaces'] = []
            for ni in network_interfaces:
                ni_info = {
                    'id': ni.get('id', '') if isinstance(ni, dict) else getattr(ni, 'id', ''),
                    'name': '',
                    'private_ip_address': 'N/A'  # Will be populated if available
                }
                if ni_info['id']:
                    ni_info['name'] = ni_info['id'].split('/')[-1]
                endpoint_dict['network_interfaces'].append(ni_info)
            
            # Extract subnet information - directly available in endpoint_dict
            subnet_info = endpoint_dict.get('subnet', {})
            endpoint_dict['subnet'] = {
                'id': subnet_info.get('id', '') if subnet_info else '',
                'name': subnet_info.get('id', '').split('/')[-1] if subnet_info and subnet_info.get('id') else '',
                'vnet_name': _extract_vnet_from_subnet_id(subnet_info.get('id', '') if subnet_info else '')
            }
            
            # Extract private link service connections - directly available in endpoint_dict
            pls_connections = endpoint_dict.get('private_link_service_connections', [])
            manual_pls_connections = endpoint_dict.get('manual_private_link_service_connections', [])
            all_connections = (pls_connections or []) + (manual_pls_connections or [])
            
            endpoint_dict['connection_count'] = len(all_connections)
            endpoint_dict['connections'] = []
            
            for conn in all_connections:
                # For SDK as_dict(), connections are already dict format with flattened structure
                connection_state_info = conn.get('private_link_service_connection_state', {})
                connection_info = {
                    'name': conn.get('name', ''),
                    'connection_state': connection_state_info.get('status', 'Unknown'),
                    'connection_description': connection_state_info.get('description', ''),
                    'private_link_service_id': conn.get('private_link_service_id', ''),
                    'group_ids': conn.get('group_ids', []),
                    'target_service': _extract_service_from_resource_id(conn.get('private_link_service_id', '')),
                    'is_manual': conn in (manual_pls_connections or [])
                }
                endpoint_dict['connections'].append(connection_info)
            
            # Extract private DNS zone groups - this might be missing from flattened structure
            # Try both possible locations in the flattened dict
            dns_zone_groups = endpoint_dict.get('private_dns_zone_groups', [])
            if not dns_zone_groups:
                # If not found directly, it might be under a different key or missing
                dns_zone_groups = []
            
            endpoint_dict['dns_zone_group_count'] = len(dns_zone_groups)
            endpoint_dict['dns_zone_groups'] = []
            
            for dzg in dns_zone_groups:
                # For SDK as_dict(), DNS zone groups are already dict format
                dns_configs = dzg.get('private_dns_zone_configs', [])
                dzg_info = {
                    'name': dzg.get('name', ''),
                    'dns_zone_count': len(dns_configs),
                    'dns_zones': [config.get('name', '') for config in dns_configs]
                }
                endpoint_dict['dns_zone_groups'].append(dzg_info)
            
            # Provisioning state and other metadata - directly in endpoint_dict for SDK as_dict()
            endpoint_dict['provisioning_state'] = endpoint_dict.get('provisioning_state', 'Unknown')
            custom_dns_configs = endpoint_dict.get('custom_dns_configs', [])
            endpoint_dict['custom_dns_configs'] = custom_dns_configs
            endpoint_dict['custom_dns_config_count'] = len(custom_dns_configs)
            
            # Additional helpful derived information
            endpoint_dict['primary_connection_state'] = 'Unknown'
            endpoint_dict['primary_target_service'] = 'Unknown'
            if endpoint_dict['connections']:
                primary_conn = endpoint_dict['connections'][0]
                endpoint_dict['primary_connection_state'] = primary_conn['connection_state']
                endpoint_dict['primary_target_service'] = primary_conn['target_service']
            
            # Log the final endpoint data object that will be passed to frontend
            logger.debug(f"Final endpoint data for frontend: {endpoint_dict}")
            endpoints.append(endpoint_dict)
        
        logger.debug(f"Found {len(endpoints)} private endpoints in subscription {subscription_id}")
        logger.debug(f"Private endpoint data structure passed to JS frontend: {endpoints}")
        return endpoints
        
    except Exception as e:
        logger.error(f"Error listing private endpoints with SDK: {e}")
        # Fallback to REST API
        path = f"subscriptions/{subscription_id}/providers/Microsoft.Network/privateEndpoints"
        endpoints = _list_resources(credential, path, "2021-08-01")
        
        # Enrich with subscription information and detailed properties (original logic)
        for endpoint in endpoints:
            endpoint['subscription'] = subscription_name or subscription_id
            endpoint['subscription_id'] = subscription_id
            
            # Extract resource group from ID if not already present
            if 'resource_group' not in endpoint and endpoint.get('id'):
                parts = endpoint['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        endpoint['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in endpoint:
                    endpoint['resource_group'] = 'undefined'
            
            # Extract and enrich properties
            properties = endpoint.get('properties', {})
            
            # Extract network interface information
            network_interfaces = properties.get('networkInterfaces', [])
            endpoint['network_interface_count'] = len(network_interfaces)
            endpoint['network_interfaces'] = []
            for ni in network_interfaces:
                ni_info = {
                    'id': ni.get('id', ''),
                    'name': ni.get('id', '').split('/')[-1] if ni.get('id') else '',
                    'private_ip_address': 'N/A'  # Will be populated if available
                }
                endpoint['network_interfaces'].append(ni_info)
            
            # Extract subnet information
            subnet_info = properties.get('subnet', {})
            endpoint['subnet'] = {
                'id': subnet_info.get('id', ''),
                'name': subnet_info.get('id', '').split('/')[-1] if subnet_info.get('id') else '',
                'vnet_name': _extract_vnet_from_subnet_id(subnet_info.get('id', ''))
            }
            
            # Extract private link service connections
            pls_connections = properties.get('privateLinkServiceConnections', [])
            manual_pls_connections = properties.get('manualPrivateLinkServiceConnections', [])
            all_connections = pls_connections + manual_pls_connections
            
            endpoint['connection_count'] = len(all_connections)
            endpoint['connections'] = []
            
            for conn in all_connections:
                conn_props = conn.get('properties', {})
                connection_info = {
                    'name': conn.get('name', ''),
                    'connection_state': conn_props.get('privateLinkServiceConnectionState', {}).get('status', 'Unknown'),
                    'connection_description': conn_props.get('privateLinkServiceConnectionState', {}).get('description', ''),
                    'private_link_service_id': conn_props.get('privateLinkServiceId', ''),
                    'group_ids': conn_props.get('groupIds', []),
                    'target_service': _extract_service_from_resource_id(conn_props.get('privateLinkServiceId', '')),
                    'is_manual': conn in manual_pls_connections
                }
                endpoint['connections'].append(connection_info)
            
            # Extract private DNS zone groups
            dns_zone_groups = properties.get('privateDnsZoneGroups', [])
            endpoint['dns_zone_group_count'] = len(dns_zone_groups)
            endpoint['dns_zone_groups'] = []
            
            for dzg in dns_zone_groups:
                dzg_props = dzg.get('properties', {})
                dns_configs = dzg_props.get('privateDnsZoneConfigs', [])
                dzg_info = {
                    'name': dzg.get('name', ''),
                    'dns_zone_count': len(dns_configs),
                    'dns_zones': [config.get('name', '') for config in dns_configs]
                }
                endpoint['dns_zone_groups'].append(dzg_info)
            
            # Provisioning state and other metadata
            endpoint['provisioning_state'] = properties.get('provisioningState', 'Unknown')
            endpoint['custom_dns_configs'] = properties.get('customDnsConfigs', [])
            endpoint['custom_dns_config_count'] = len(properties.get('customDnsConfigs', []))
            
            # Additional helpful derived information
            endpoint['primary_connection_state'] = 'Unknown'
            endpoint['primary_target_service'] = 'Unknown'
            if endpoint['connections']:
                primary_conn = endpoint['connections'][0]
                endpoint['primary_connection_state'] = primary_conn['connection_state']
                endpoint['primary_target_service'] = primary_conn['target_service']
        
        logger.debug(f"Found {len(endpoints)} private endpoints in subscription {subscription_id} (REST API fallback)")
        logger.debug(f"Private endpoint data structure passed to JS frontend (REST API): {endpoints}")
        return endpoints

def _extract_vnet_from_subnet_id(subnet_id: str) -> str:
    """Extract VNet name from subnet resource ID."""
    if not subnet_id:
        return ''
    try:
        # Format: /subscriptions/{sub}/resourceGroups/{rg}/providers/Microsoft.Network/virtualNetworks/{vnet}/subnets/{subnet}
        parts = subnet_id.split('/')
        for i, part in enumerate(parts):
            if part == 'virtualNetworks' and i + 1 < len(parts):
                return parts[i + 1]
    except Exception:
        pass
    return ''

def _extract_service_from_resource_id(resource_id: str) -> str:
    """Extract service name and type from private link service resource ID."""
    if not resource_id:
        return 'Unknown'
    try:
        # Extract the service type and name from the resource ID
        parts = resource_id.split('/')
        if len(parts) >= 2:
            service_name = parts[-1]
            # Try to find the service type
            for i, part in enumerate(parts):
                if part == 'providers' and i + 1 < len(parts):
                    service_type = parts[i + 1].split('.')[-1]  # e.g., Microsoft.Storage -> Storage
                    if i + 2 < len(parts):
                        resource_type = parts[i + 2]  # e.g., storageAccounts
                        return f"{service_type}/{resource_type}: {service_name}"
            return service_name
    except Exception:
        pass
    return 'Unknown'

def list_private_resolvers(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    """
    List DNS Private Resolver clusters with detailed properties.
    """
    try:
        from azure.mgmt.dnsresolver import DnsResolverManagementClient
        _, cloud_config = get_current_cloud_info()
        client = DnsResolverManagementClient(
            credential, 
            subscription_id,
            base_url=cloud_config['management_endpoint']
        )
        resolvers = []
        for resolver in client.dns_resolvers.list():
            resolver_dict = resolver.as_dict()
            # Enrich with subscription information
            resolver_dict['subscription'] = subscription_name or subscription_id
            resolver_dict['subscription_id'] = subscription_id
            # Extract resource group from ID if not already present
            if 'resource_group' not in resolver_dict and resolver_dict.get('id'):
                parts = resolver_dict['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        resolver_dict['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in resolver_dict:
                    resolver_dict['resource_group'] = 'undefined'
            resolvers.append(resolver_dict)
        logger.debug(f"Found {len(resolvers)} private resolvers in subscription {subscription_id}")
        return resolvers
    except Exception as e:
        logger.error(f"Error listing private resolvers with SDK: {e}")
        # Fallback to REST API
        path = f"subscriptions/{subscription_id}/providers/Microsoft.Network/dnsResolvers"
        result = _list_resources(credential, path, "2022-07-01")
        
        # Enrich REST API results with subscription information
        for resolver in result:
            resolver['subscription'] = subscription_name or subscription_id
            resolver['subscription_id'] = subscription_id
            # Extract resource group from ID if not already present
            if 'resource_group' not in resolver and resolver.get('id'):
                parts = resolver['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        resolver['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in resolver:
                    resolver['resource_group'] = 'undefined'
        
        logger.debug(f"Found {len(result)} private resolvers in subscription {subscription_id} (REST API)")
        return result

def list_dns_forwarding_rulesets(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    """
    List DNS forwarding rulesets with detailed properties.
    """
    try:
        from azure.mgmt.dnsresolver import DnsResolverManagementClient
        _, cloud_config = get_current_cloud_info()
        client = DnsResolverManagementClient(
            credential, 
            subscription_id,
            base_url=cloud_config['management_endpoint']
        )
        rulesets = []
        for ruleset in client.dns_forwarding_rulesets.list():
            ruleset_dict = ruleset.as_dict()
            # Enrich with subscription information
            ruleset_dict['subscription'] = subscription_name or subscription_id
            ruleset_dict['subscription_id'] = subscription_id
            # Extract resource group from ID if not already present
            if 'resource_group' not in ruleset_dict and ruleset_dict.get('id'):
                parts = ruleset_dict['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        ruleset_dict['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in ruleset_dict:
                    ruleset_dict['resource_group'] = 'undefined'
            
            # Extract outbound endpoints from multiple possible sources
            properties = ruleset_dict.get('properties', {})
            outbound_endpoints = (
                properties.get('dnsResolverOutboundEndpoints') or
                ruleset_dict.get('dns_resolver_outbound_endpoints') or
                ruleset_dict.get('dnsResolverOutboundEndpoints') or
                ruleset_dict.get('outbound_endpoints') or
                []
            )
            
            # Ensure it's a list
            if not isinstance(outbound_endpoints, list):
                outbound_endpoints = []
                
            # Store endpoints in both standard field and dns_resolver_outbound_endpoints field for compatibility
            ruleset_dict['outbound_endpoints'] = outbound_endpoints
            # Keep the original data as well in case frontend code looks for this field
            if not ruleset_dict.get('dns_resolver_outbound_endpoints') and outbound_endpoints:
                ruleset_dict['dns_resolver_outbound_endpoints'] = outbound_endpoints
            
            rulesets.append(ruleset_dict)
        logger.debug(f"Found {len(rulesets)} DNS forwarding rulesets in subscription {subscription_id}")
        return rulesets
    except Exception as e:
        logger.error(f"Error listing forwarding rulesets with SDK: {e}")
        # Fallback to REST API
        path = f"subscriptions/{subscription_id}/providers/Microsoft.Network/dnsForwardingRulesets"
        result = _list_resources(credential, path, "2022-07-01")
        
        # Enrich REST API results with subscription information
        for ruleset in result:
            ruleset['subscription'] = subscription_name or subscription_id
            ruleset['subscription_id'] = subscription_id
            # Extract resource group from ID if not already present
            if 'resource_group' not in ruleset and ruleset.get('id'):
                parts = ruleset['id'].split('/')
                for i, part in enumerate(parts):
                    if part == 'resourceGroups' and i + 1 < len(parts):
                        ruleset['resource_group'] = parts[i + 1]
                        break
                if 'resource_group' not in ruleset:
                    ruleset['resource_group'] = 'undefined'
            
            # Extract outbound endpoints from multiple possible sources
            properties = ruleset.get('properties', {})
            outbound_endpoints = (
                properties.get('dnsResolverOutboundEndpoints') or
                ruleset.get('dns_resolver_outbound_endpoints') or
                ruleset.get('dnsResolverOutboundEndpoints') or
                ruleset.get('outbound_endpoints') or
                []
            )
            
            # Ensure it's a list
            if not isinstance(outbound_endpoints, list):
                outbound_endpoints = []
                
            # Store endpoints in both standard field and dns_resolver_outbound_endpoints field for compatibility
            ruleset['outbound_endpoints'] = outbound_endpoints
            # Keep the original data as well in case frontend code looks for this field
            if not ruleset.get('dns_resolver_outbound_endpoints') and outbound_endpoints:
                ruleset['dns_resolver_outbound_endpoints'] = outbound_endpoints
        
        logger.debug(f"Found {len(result)} DNS forwarding rulesets in subscription {subscription_id} (REST API)")
        return result
 
def list_forwarding_rules(credential, subscription_id: str, subscription_name: str = '') -> List[Dict]:
    """
    List DNS forwarding rules within each forwarding ruleset in the subscription.
    """
    rules = []
    try:
        from azure.mgmt.dnsresolver import DnsResolverManagementClient
        _, cloud_config = get_current_cloud_info()
        client = DnsResolverManagementClient(
            credential, 
            subscription_id,
            base_url=cloud_config['management_endpoint']
        )
        
        # First, collect all rulesets
        rulesets_info = []
        for frs in client.dns_forwarding_rulesets.list():
            # extract resource group and ruleset name from id
            if not frs or not frs.id:
                continue
            parts = frs.id.split('/')
            if len(parts) < 9:
                continue
            rg = parts[4]
            rs_name = parts[-1]
            rulesets_info.append((rg, rs_name))
        
        # Function to fetch rules for a single ruleset
        def fetch_ruleset_rules(rg: str, rs_name: str) -> List[Dict]:
            try:
                ruleset_rules = []
                for rule in client.forwarding_rules.list(rg, rs_name):
                    rd = rule.as_dict()
                    # Add additional context for the rule
                    rd['rulesetName'] = rs_name
                    rd['ruleset_name'] = rs_name  # Alternative naming for compatibility
                    rd['resource_group'] = rg
                    # Enrich with subscription information
                    rd['subscription'] = subscription_name or subscription_id
                    rd['subscription_id'] = subscription_id
                    ruleset_rules.append(rd)
                return ruleset_rules
            except Exception as e:
                logger.error(f"Error fetching rules for ruleset {rs_name} in {rg}: {e}")
                return []
        
        # Execute rule fetching in parallel
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_ruleset = {
                executor.submit(fetch_ruleset_rules, rg, rs_name): (rg, rs_name)
                for rg, rs_name in rulesets_info
            }
            
            for future in as_completed(future_to_ruleset):
                try:
                    ruleset_rules = future.result()
                    rules.extend(ruleset_rules)
                except Exception as e:
                    rg, rs_name = future_to_ruleset[future]
                    logger.error(f"Ruleset {rs_name} in {rg} generated an exception: {e}")
        
        logger.debug(f"Found {len(rules)} forwarding rules across all rulesets in subscription {subscription_id}")
    except Exception as e:
        logger.error(f"Error listing forwarding rules: {e}")
    return rules

def build_resolver_hierarchy(resolvers: List[Dict], rulesets: List[Dict], rules: List[Dict]) -> List[Dict]:
    """
    Build a hierarchical structure of resolvers with their associated rulesets and rules.
    This moves the relationship logic from JavaScript to Python for better maintainability.
    
    Args:
        resolvers: List of DNS resolver resources
        rulesets: List of DNS forwarding rulesets
        rules: List of forwarding rules
    
    Returns:
        List of resolver hierarchy objects with structure:
        [
            {
                "resolver": {...},  # resolver object or None for standalone
                "rulesets": [
                    {
                        "ruleset": {...},
                        "rules": [...]
                    }
                ]
            }
        ]
    """
    logger.debug(f"Building resolver hierarchy: {len(resolvers)} resolvers, {len(rulesets)} rulesets, {len(rules)} rules")

    resolver_map = {resolver.get('name'): {'resolver': resolver, 'rulesets': []} for resolver in resolvers}
    rules_by_ruleset = {}
    for rule in rules:
        ruleset_name = rule.get('ruleset_name') or rule.get('rulesetName')
        if ruleset_name:
            rules_by_ruleset.setdefault(ruleset_name, []).append(rule)

    assigned_rulesets = set()

    for ruleset in rulesets:
        ruleset_name = ruleset.get('name', '')
        assigned_to_resolver = False

        # Strategy 1: Match by outbound endpoints
        for endpoint in ruleset.get('outbound_endpoints', []):
            endpoint_id = endpoint.get('id', '')
            import re
            match = re.search(r'/dnsResolvers/([^/]+)/', endpoint_id)
            if match:
                resolver_name = match.group(1)
                if resolver_name in resolver_map:
                    resolver_map[resolver_name]['rulesets'].append({'ruleset': ruleset, 'rules': rules_by_ruleset.get(ruleset_name, [])})
                    assigned_to_resolver = True
                    assigned_rulesets.add(ruleset_name)

        # Strategy 2: Match by naming pattern
        if not assigned_to_resolver and ruleset_name.startswith('ruleset-'):
            resolver_name = ruleset_name[len('ruleset-'):]
            if resolver_name in resolver_map:
                resolver_map[resolver_name]['rulesets'].append({'ruleset': ruleset, 'rules': rules_by_ruleset.get(ruleset_name, [])})
                assigned_to_resolver = True
                assigned_rulesets.add(ruleset_name)

        # Strategy 3: Match by resource group and subscription
        if not assigned_to_resolver:
            for resolver_name, resolver_data in resolver_map.items():
                resolver = resolver_data['resolver']
                if resolver.get('subscription_id') == ruleset.get('subscription_id') and resolver.get('resource_group') == ruleset.get('resource_group'):
                    resolver_map[resolver_name]['rulesets'].append({'ruleset': ruleset, 'rules': rules_by_ruleset.get(ruleset_name, [])})
                    assigned_to_resolver = True
                    assigned_rulesets.add(ruleset_name)

    hierarchy = list(resolver_map.values())
    
    # Add standalone rulesets
    for ruleset in rulesets:
        if ruleset.get('name') not in assigned_rulesets:
            hierarchy.append({
                'resolver': None,
                'rulesets': [{'ruleset': ruleset, 'rules': rules_by_ruleset.get(ruleset.get('name'), [])}]
            })

    logger.info(f"Built hierarchy: {len(hierarchy)} entries")
    return hierarchy

def list_all_dns_resources_parallel(credential, subscription_id: str, subscription_name: str = '') -> Dict[str, List[Dict]]:
    """
    List all DNS-related resources in parallel for better performance.
    
    Args:
        credential: Azure credential
        subscription_id: Azure subscription ID
        subscription_name: Azure subscription name (optional)
    
    Returns:
        Dictionary containing all resource types:
        {
            'private_dns_zones': [...],
            'virtual_network_links': [...],
            'private_endpoints': [...],
            'private_resolvers': [...],
            'dns_forwarding_rulesets': [...],
            'forwarding_rules': [...]
        }
    """
    logger.info(f"Starting parallel fetch of all DNS resources for subscription {subscription_id}")
    
    # Step 1: Fetch zones first since VNet links depend on them
    zones = list_private_dns_zones(credential, subscription_id, subscription_name)
    
    # Step 2: Define remaining resource fetching functions (excluding zones and VNet links)
    resource_functions = [
        ('private_endpoints', list_private_endpoints),
        ('private_resolvers', list_private_resolvers),
        ('dns_forwarding_rulesets', list_dns_forwarding_rulesets),
        ('forwarding_rules', list_forwarding_rules)
    ]
    
    results = {'private_dns_zones': zones}
    
    # Function wrapper for parallel execution
    def fetch_resource_type(resource_type: str, fetch_func) -> tuple:
        try:
            logger.debug(f"Starting fetch for {resource_type}")
            resources = fetch_func(credential, subscription_id, subscription_name)
            logger.debug(f"Completed fetch for {resource_type}: {len(resources)} items")
            return resource_type, resources
        except Exception as e:
            logger.error(f"Error fetching {resource_type}: {e}")
            return resource_type, []
    
    # Execute resource fetching in parallel (excluding zones which are already fetched)
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_resource = {
            executor.submit(fetch_resource_type, resource_type, fetch_func): resource_type
            for resource_type, fetch_func in resource_functions
        }
        
        for future in as_completed(future_to_resource):
            try:
                resource_type, resources = future.result()
                results[resource_type] = resources
                logger.info(f"Successfully fetched {len(resources)} {resource_type}")
            except Exception as e:
                resource_type = future_to_resource[future]
                logger.error(f"Resource type {resource_type} generated an exception: {e}")
                results[resource_type] = []
    
    # Step 3: Fetch VNet links using the optimized function with pre-fetched zones
    try:
        logger.debug("Starting optimized fetch for virtual_network_links")
        vnet_links = list_virtual_network_links_optimized(credential, subscription_id, subscription_name, zones)
        results['virtual_network_links'] = vnet_links
        logger.info(f"Successfully fetched {len(vnet_links)} virtual_network_links")
    except Exception as e:
        logger.error(f"Error fetching virtual_network_links: {e}")
        results['virtual_network_links'] = []
    
    # Build resolver hierarchy if we have the necessary data
    if all(key in results for key in ['private_resolvers', 'dns_forwarding_rulesets', 'forwarding_rules']):
        try:
            results['resolver_hierarchy'] = build_resolver_hierarchy(
                results['private_resolvers'],
                results['dns_forwarding_rulesets'],
                results['forwarding_rules']
            )
            logger.info(f"Built resolver hierarchy with {len(results['resolver_hierarchy'])} entries")
        except Exception as e:
            logger.error(f"Error building resolver hierarchy: {e}")
            results['resolver_hierarchy'] = []
    
    logger.info(f"Completed parallel fetch of all DNS resources for subscription {subscription_id}")
    return results

# Contains AI-generated edits.
