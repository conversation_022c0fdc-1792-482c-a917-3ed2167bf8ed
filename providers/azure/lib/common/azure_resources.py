#!/usr/bin/env python3
"""
Azure resource management operations for subscriptions, management groups, and resource groups.
"""

import logging
import httpx
from typing import List, Dict

# Import cloud configuration and token management
from .config import get_current_cloud_info
from .token_manager import get_cached_token

logger = logging.getLogger(__name__)


def get_subscriptions(credential) -> List[Dict]:
    """
    Get all accessible subscriptions.
    
    Args:
        credential: Azure credential object
        
    Returns:
        List of subscription dictionaries
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        logger.info(f"📋 Retrieving subscriptions from {cloud_config['name']}...")
        
        # Use cached token for efficient authentication
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            logger.error("❌ Failed to get access token for Azure Management")
            return []
        
        endpoint = f"{cloud_config['management_endpoint']}/subscriptions?api-version=2022-12-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            data = response.json()
            subscriptions = data.get("value", [])
            
            # Handle pagination if nextLink is provided
            next_link = data.get("nextLink")
            while next_link:
                try:
                    with httpx.Client(verify=True) as client:
                        next_response = client.get(next_link, headers=headers)
                    
                    if next_response.status_code == 200:
                        next_data = next_response.json()
                        subscriptions.extend(next_data.get("value", []))
                        next_link = next_data.get("nextLink")
                    else:
                        logger.warning(f"❗ Failed to get next page: HTTP {next_response.status_code}")
                        break
                except Exception as e:
                    logger.warning(f"❗ Error retrieving next page: {str(e)}")
                    break
            
            logger.info(f"✅ Found {len(subscriptions)} subscriptions")
            return subscriptions
        else:
            logger.error(f"❌ Failed to get subscriptions: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error getting subscriptions: {str(e)}")
        return []


def get_resource_groups(credential, subscription_id: str) -> List[Dict]:
    """
    Get all resource groups in a subscription.
    
    Args:
        credential: Azure credential object
        subscription_id: Subscription ID
        
    Returns:
        List of resource group dictionaries
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        # Use cached token for efficient authentication
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            return []
        
        endpoint = f"{cloud_config['management_endpoint']}/subscriptions/{subscription_id}/resourcegroups?api-version=2021-04-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            data = response.json()
            return data.get("value", [])
        else:
            return []
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting resource groups for subscription {subscription_id}: {str(e)}")
        return []


def get_management_groups(credential) -> List[Dict]:
    """
    Get all accessible management groups.
    
    Args:
        credential: Azure credential object
        
    Returns:
        List of management group dictionaries
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        logger.info(f"🏢 Retrieving management groups from {cloud_config['name']}...")
        
        # Use cached token for efficient authentication
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            logger.error("❌ Failed to get access token for Azure Management")
            return []
        
        endpoint = f"{cloud_config['management_endpoint']}/providers/Microsoft.Management/managementGroups?api-version=2020-05-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            data = response.json()
            management_groups = data.get("value", [])
            logger.info(f"✅ Found {len(management_groups)} management groups")
            return management_groups
        else:
            logger.error(f"❌ Failed to get management groups: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error getting management groups: {str(e)}")
        return []


def get_resources_in_resource_group(credential, subscription_id: str, resource_group_name: str) -> List[Dict]:
    """
    Get all resources in a specific resource group.
    
    Args:
        credential: Azure credential object
        subscription_id: Subscription ID
        resource_group_name: Resource group name
        
    Returns:
        List of resource dictionaries
    """
    try:
        # Get cloud configuration
        detected_cloud, cloud_config = get_current_cloud_info()
        
        # Use cached token for efficient authentication
        token = get_cached_token(credential, cloud_config['management_scope'])
        
        if not token or not token.token:
            logger.warning(f"⚠️ Failed to get access token for resource group {resource_group_name}")
            return []
        
        endpoint = f"{cloud_config['management_endpoint']}/subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/resources?api-version=2021-04-01"
        
        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json"
        }
        
        try:
            with httpx.Client(base_url=cloud_config['management_endpoint'], verify=True) as client:
                response = client.get(endpoint, headers=headers)
        except Exception as ssl_error:
            if "certificate" in str(ssl_error).lower() or "ssl" in str(ssl_error).lower():
                with httpx.Client(base_url=cloud_config['management_endpoint'], verify=False) as client:
                    response = client.get(endpoint, headers=headers)
            else:
                raise ssl_error
        
        if response.status_code == 200:
            data = response.json()
            resources = data.get("value", [])
            logger.debug(f"📦 Found {len(resources)} resources in {resource_group_name}")
            return resources
        else:
            logger.warning(f"⚠️ Failed to get resources for resource group {resource_group_name}: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting resources for resource group {resource_group_name}: {str(e)}")
        return []


def get_all_resources_by_subscription(credential, subscription_id: str) -> Dict[str, List[Dict]]:
    """
    Get all resources organized by resource group for a subscription.
    
    Args:
        credential: Azure credential object
        subscription_id: Subscription ID
        
    Returns:
        Dictionary mapping resource group names to lists of resources
    """
    try:
        logger.info(f"📋 Getting all resources for subscription {subscription_id}")
        
        # First get all resource groups
        resource_groups = get_resource_groups(credential, subscription_id)
        
        if not resource_groups:
            logger.warning(f"⚠️ No resource groups found for subscription {subscription_id}")
            return {}
        
        resources_by_rg = {}
        
        # Get resources for each resource group
        for rg in resource_groups:
            rg_name = rg.get('name', '')
            if rg_name:
                resources = get_resources_in_resource_group(credential, subscription_id, rg_name)
                resources_by_rg[rg_name] = resources
                
                if resources:
                    logger.info(f"📦 {rg_name}: {len(resources)} resources")
                    # Log first few resource names for debugging
                    for i, resource in enumerate(resources[:3]):
                        resource_name = resource.get('name', 'Unknown')
                        resource_type = resource.get('type', 'Unknown')
                        logger.debug(f"   🔧 {resource_type}: {resource_name}")
                    if len(resources) > 3:
                        logger.debug(f"   ... and {len(resources) - 3} more resources")
                else:
                    logger.debug(f"📦 {rg_name}: No resources")
        
        total_resources = sum(len(resources) for resources in resources_by_rg.values())
        logger.info(f"✅ Found {total_resources} total resources across {len(resources_by_rg)} resource groups")
        
        return resources_by_rg
        
    except Exception as e:
        logger.error(f"❌ Error getting all resources for subscription {subscription_id}: {str(e)}")
        return {}

# Contains AI-generated edits.
