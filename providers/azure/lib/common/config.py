#!/usr/bin/env python3
"""
Configuration and logging setup for Azure User Role Analysis Tool.
"""

import logging
import argparse
import os
import subprocess
import json
import threading
from azure.identity import AzureAuthorityHosts

# Initialize logger for this module
logger = logging.getLogger(__name__)


def setup_logging(debug=False):
    """Setup logging configuration based on debug flag."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s' if debug else '%(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    logger = logging.getLogger(__name__)
    logger.setLevel(level)  # Explicitly set the logger level
    return logger


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Azure User Role Analysis Tool - Generates HTML report by default (works with Global and China Azure)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python azure_user.py                   # Generate HTML report and open in browser (auto-detect cloud)
  python azure_user.py --debug           # Generate HTML report with debug logging
  python azure_user.py --cloud global    # Force use of Global Azure
  python azure_user.py --cloud china     # Force use of China Azure
  python organization.py --include-resources  # Include all resources within resource groups
        """
    )
    
    # Optional flags
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='Enable debug logging to show detailed debugging information'
    )
    
    parser.add_argument(
        '--cloud', '-c',
        choices=['global', 'china'],
        help='Specify Azure cloud environment (auto-detects if not specified)'
    )
    
    parser.add_argument(
        '--include-resources', '-r',
        action='store_true',
        help='Include all resources within each resource group (enhances organization view with detailed resource information)'
    )
    
    parser.add_argument(
        '--full-list', '-f',
        action='store_true',
        help='Show full list of resources when used with --include-resources (displays complete resource inventory)'
    )
    
    return parser.parse_args()


def print_detailed_performance_summary(start_time: float, end_time: float, total_users: int, operation: str):
    """
    Print performance summary for parallel operations.
    
    Args:
        start_time: Start timestamp
        end_time: End timestamp
        total_users: Number of users processed
        operation: Description of the operation
    """
    duration = end_time - start_time
    users_per_second = total_users / duration if duration > 0 else 0
    
    print(f"\n⚡ Performance Summary for {operation}:")
    print(f"   🕐 Total Time: {duration:.2f} seconds")
    print(f"   👥 Users Processed: {total_users}")
    print(f"   📊 Rate: {users_per_second:.2f} users/second")
    print(f"   📈 Average Time per User: {duration / total_users:.2f} seconds")
    
    if duration < 30:
        print(f"   🚀 Excellent performance!")
    elif duration < 60:
        print(f"   ✅ Good performance")
    else:
        print(f"   ⚠️ Consider optimizing for better performance")
    
    print("=" * 60)


def print_performance_summary():
    """Print a simple performance summary for the main module."""
    print("\n" + "=" * 60)
    print("🎯 Operation completed successfully!")
    print("=" * 60)

# Cloud environment configuration
CLOUD_ENVIRONMENTS = {
    'global': {
        'name': 'Azure Global',
        'authority_base': 'https://login.microsoftonline.com',
        'graph_endpoint': 'https://graph.microsoft.com',
        'management_endpoint': 'https://management.azure.com',
        'graph_scope': 'https://graph.microsoft.com/.default',
        'management_scope': 'https://management.azure.com/.default'
    },
    'china': {
        'name': 'Azure China',
        'authority_base': 'https://login.chinacloudapi.cn',
        'graph_endpoint': 'https://microsoftgraph.chinacloudapi.cn',
        'management_endpoint': 'https://management.chinacloudapi.cn',
        'graph_scope': 'https://microsoftgraph.chinacloudapi.cn/.default',
        'management_scope': 'https://management.chinacloudapi.cn/.default'
    },
    'government': {
        'name': 'Azure Government',
        'authority_base': 'https://login.microsoftonline.us',
        'graph_endpoint': 'https://graph.microsoft.us',
        'management_endpoint': 'https://management.usgovcloudapi.net',
        'graph_scope': 'https://graph.microsoft.us/.default',
        'management_scope': 'https://management.usgovcloudapi.net/.default'
    }
}
# Authority host mapping
AUTHORITY_HOST_MAPPING = {
    'global': AzureAuthorityHosts.AZURE_PUBLIC_CLOUD,
    'china': AzureAuthorityHosts.AZURE_CHINA,
    'government': AzureAuthorityHosts.AZURE_GOVERNMENT
}

# Detailed authority info mapping
AUTHORITY_INFO_MAPPING = {
    'global': {
        'host': AzureAuthorityHosts.AZURE_PUBLIC_CLOUD,
        'name': 'Azure Public Cloud',
        'description': 'Standard Azure cloud for most regions'
    },
    'china': {
        'host': AzureAuthorityHosts.AZURE_CHINA,
        'name': 'Azure China Cloud',
        'description': 'Azure operated by 21Vianet in China'
    },
    'government': {
        'host': AzureAuthorityHosts.AZURE_GOVERNMENT,
        'name': 'Azure Government Cloud',
        'description': 'Azure cloud for US government agencies'
    }
}


# Cloud detection cache to ensure consistency within a session
_cloud_detection_cache = None
_cloud_detection_lock = threading.Lock()


def detect_azure_cloud(force_refresh=False):
    """
    Detect which Azure cloud environment is currently configured.
    
    Args:
        force_refresh (bool): Force refresh of cached detection result
    
    Returns:
        str: 'global', 'china', 'government', or 'unknown'
    """
    global _cloud_detection_cache
    
    with _cloud_detection_lock:
        # Return cached result unless forced refresh
        if _cloud_detection_cache is not None and not force_refresh:
            return _cloud_detection_cache
    
    try:
        # Try to get current Azure CLI cloud configuration
        result = subprocess.run(['az', 'cloud', 'show'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            cloud_info = json.loads(result.stdout)
            cloud_name = cloud_info.get('name', '').lower()
            
            logger.debug(f"🔍 Azure CLI cloud name: {cloud_name}")
            
            if 'china' in cloud_name or 'azurechinacloud' in cloud_name:
                detected = 'china'
            elif 'government' in cloud_name or 'azureusgovernment' in cloud_name or 'usgovernment' in cloud_name:
                detected = 'government'
            elif 'azurecloud' in cloud_name or 'public' in cloud_name:
                detected = 'global'
            else:
                logger.warning(f"⚠️ Unknown cloud name from Azure CLI: {cloud_name}, defaulting to global")
                detected = 'global'
                
            with _cloud_detection_lock:
                _cloud_detection_cache = detected
            
            logger.debug(f"✅ Detected cloud environment via Azure CLI: {detected}")
            return detected
        else:
            logger.debug(f"⚠️ Azure CLI cloud show failed with return code: {result.returncode}")
        
        # Fallback: check environment variables
        env_cloud = os.getenv('AZURE_CLOUD_NAME', '').lower()
        if env_cloud:
            logger.debug(f"🔍 AZURE_CLOUD_NAME environment variable: {env_cloud}")
            
            if env_cloud in ['azurechinacloud', 'china']:
                detected = 'china'
            elif env_cloud in ['azureusgovernment', 'government', 'usgovernment']:
                detected = 'government'
            else:
                detected = 'global'
                
            with _cloud_detection_lock:
                _cloud_detection_cache = detected
                
            logger.debug(f"✅ Detected cloud environment via environment variable: {detected}")
            return detected
        
        # Default to global if detection fails
        detected = 'global'
        with _cloud_detection_lock:
            _cloud_detection_cache = detected
            
        logger.debug("🌐 No specific cloud configuration found, defaulting to global Azure")
        return detected
        
    except Exception as e:
        # If detection fails, default to global Azure
        logger.debug(f"⚠️ Cloud detection failed with error: {e}, defaulting to global Azure")
        detected = 'global'
        with _cloud_detection_lock:
            _cloud_detection_cache = detected
        return detected


def get_cloud_config(cloud_env=None):
    """
    Get cloud configuration for the specified or detected environment.
    
    Args:
        cloud_env (str, optional): Specific cloud environment ('global' or 'china')
                                  If None, will auto-detect
    
    Returns:
        dict: Cloud configuration dictionary
    """
    if cloud_env is None:
        cloud_env = detect_azure_cloud()
    
    return CLOUD_ENVIRONMENTS.get(cloud_env, CLOUD_ENVIRONMENTS['global'])


def get_current_cloud_info():
    """
    Get information about the current cloud environment.
    
    Returns:
        tuple: (cloud_name, cloud_config)
    """
    detected_cloud = detect_azure_cloud()
    cloud_config = get_cloud_config(detected_cloud)
    return detected_cloud, cloud_config


def clear_cloud_detection_cache():
    """
    Clear the cloud detection cache to force re-detection.
    Useful when Azure CLI cloud configuration changes during runtime.
    """
    global _cloud_detection_cache
    
    with _cloud_detection_lock:
        _cloud_detection_cache = None
        logger.debug("🗑️ Cloud detection cache cleared")

# Contains AI-generated edits
