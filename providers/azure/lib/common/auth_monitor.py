#!/usr/bin/env python3
"""
Authentication efficiency monitoring and optimization utilities.
"""

import logging
import time
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class AuthenticationMonitor:
    """
    Monitor authentication performance and efficiency.
    """
    
    def __init__(self):
        self.auth_attempts = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_auth_time = 0.0
        self.start_time = time.time()
    
    def record_auth_attempt(self, duration: float, cache_hit: bool = False):
        """Record an authentication attempt."""
        self.auth_attempts += 1
        self.total_auth_time += duration
        
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
    
    def get_efficiency_stats(self) -> Dict[str, Any]:
        """Get authentication efficiency statistics."""
        uptime = time.time() - self.start_time
        
        stats = {
            "uptime_seconds": uptime,
            "total_auth_attempts": self.auth_attempts,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": f"{(self.cache_hits / max(1, self.auth_attempts) * 100):.1f}%",
            "avg_auth_time": f"{(self.total_auth_time / max(1, self.auth_attempts)):.3f}s",
            "total_auth_time": f"{self.total_auth_time:.3f}s"
        }
        
        return stats
    
    def log_efficiency_report(self):
        """Log comprehensive efficiency report."""
        stats = self.get_efficiency_stats()
        
        logger.info("🎯 Authentication Efficiency Report:")
        logger.info(f"   📊 Cache Hit Rate: {stats['cache_hit_rate']}")
        logger.info(f"   ⚡ Average Auth Time: {stats['avg_auth_time']}")
        logger.info(f"   📈 Total Attempts: {stats['total_auth_attempts']}")
        logger.info(f"   ⏱️  Total Auth Time: {stats['total_auth_time']}")
        logger.info(f"   🕐 Uptime: {stats['uptime_seconds']:.1f}s")


# Global monitor instance
_global_monitor = AuthenticationMonitor()


def get_auth_monitor() -> AuthenticationMonitor:
    """Get the global authentication monitor."""
    return _global_monitor


def record_authentication(duration: float, cache_hit: bool = False):
    """Record an authentication event."""
    _global_monitor.record_auth_attempt(duration, cache_hit)


def log_auth_efficiency():
    """Log authentication efficiency statistics."""
    _global_monitor.log_efficiency_report()


def optimize_authentication_settings() -> Dict[str, Any]:
    """
    Analyze current authentication performance and suggest optimizations.
    
    Returns:
        Dict with optimization recommendations
    """
    stats = _global_monitor.get_efficiency_stats()
    recommendations = []
    
    # Check cache hit rate
    cache_hit_rate = float(stats['cache_hit_rate'].rstrip('%'))
    if cache_hit_rate < 80:
        recommendations.append({
            "issue": "Low cache hit rate",
            "current": stats['cache_hit_rate'],
            "recommendation": "Ensure consistent credential usage across modules",
            "priority": "high"
        })
    
    # Check average authentication time
    avg_time = float(stats['avg_auth_time'].rstrip('s'))
    if avg_time > 2.0:
        recommendations.append({
            "issue": "Slow authentication",
            "current": stats['avg_auth_time'],
            "recommendation": "Check network connectivity and Azure CLI configuration",
            "priority": "medium"
        })
    
    # Check for excessive authentication attempts
    if stats['total_auth_attempts'] > 50 and cache_hit_rate < 50:
        recommendations.append({
            "issue": "Too many authentication attempts",
            "current": f"{stats['total_auth_attempts']} attempts",
            "recommendation": "Review code for redundant credential creation",
            "priority": "high"
        })
    
    return {
        "stats": stats,
        "recommendations": recommendations,
        "overall_efficiency": "excellent" if cache_hit_rate >= 90 and avg_time <= 1.0 
                           else "good" if cache_hit_rate >= 70 and avg_time <= 2.0 
                           else "needs_improvement"
    }

# Contains AI-generated edits.
