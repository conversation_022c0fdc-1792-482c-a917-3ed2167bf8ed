#!/usr/bin/env python3
"""
Token management and caching for Azure authentication to improve efficiency.
Enhanced with performance monitoring and optimization.
"""

import logging
import time
import threading
from typing import Dict, Optional, Tuple, Any
from functools import lru_cache
from azure.core.credentials import AccessToken
from azure.core.exceptions import ClientAuthenticationError

logger = logging.getLogger(__name__)


class TokenManager:
    """
    Centralized token management with caching to avoid redundant authentication requests.
    """
    
    def __init__(self, credential=None):
        """
        Initialize the token manager with an Azure credential.
        
        Args:
            credential: Azure credential object from DefaultAzureCredential
        """
        self.credential = credential
        self._token_cache: Dict[str, Tuple[AccessToken, float]] = {}
        self._lock = threading.Lock()
    def set_token(self, scope: str, token: AccessToken) -> None:
        """
        Manually set a token in the cache for a given scope.
        """
        with self._lock:
            self._token_cache[scope] = (token, time.time())
    
    def get_token(self, scope: str, refresh_threshold_seconds: int = 300) -> Optional[AccessToken]:
        """
        Get a cached token or fetch a new one if needed.
        
        Args:
            scope: The Azure scope to request token for (e.g., 'https://graph.microsoft.com/.default')
            refresh_threshold_seconds: Refresh token if expiring within this many seconds (default: 5 minutes)
            
        Returns:
            AccessToken object or None if authentication fails
        """
        with self._lock:
            current_time = time.time()
            cache_key = scope[:50] + "..." if len(scope) > 50 else scope
            
            # Check if we have a cached token that's still valid
            if scope in self._token_cache:
                cached_token, cache_time = self._token_cache[scope]
                
                # Check if token is still valid (with buffer for refresh)
                if cached_token.expires_on and (cached_token.expires_on - current_time) > refresh_threshold_seconds:
                    logger.debug(f"🔄 Using cached token for scope: {cache_key} (expires in {cached_token.expires_on - current_time:.0f}s)")
                    
                    # Record cache hit for monitoring
                    try:
                        from .auth_monitor import record_authentication
                        record_authentication(0.001, cache_hit=True)  # Minimal time for cache hit
                    except ImportError:
                        pass  # Monitor not available
                    
                    return cached_token
                else:
                    logger.debug(f"🔄 Cached token for scope {cache_key} is expiring soon, refreshing...")
            
            # Fetch new token (if credential available)
            try:
                if not self.credential:
                    return None
                logger.debug(f"🔐 Fetching new token for scope: {cache_key}")
                auth_start_time = time.time()
                new_token = self.credential.get_token(scope)
                auth_duration = time.time() - auth_start_time
                
                if new_token and new_token.token:
                    # Cache the new token
                    self._token_cache[scope] = (new_token, current_time)
                    logger.debug(f"✅ Successfully cached new token for scope: {cache_key} (expires in {new_token.expires_on - current_time:.0f}s, auth took {auth_duration:.2f}s)")
                    
                    # Record cache miss for monitoring
                    try:
                        from .auth_monitor import record_authentication
                        record_authentication(auth_duration, cache_hit=False)
                    except ImportError:
                        pass  # Monitor not available
                    
                    return new_token
                else:
                    logger.error(f"❌ Failed to get token for scope: {cache_key}")
                    return None
                    
            except ClientAuthenticationError as e:
                logger.error(f"❌ Authentication failed for scope {cache_key}: {str(e)}")
                return None
            except Exception as e:
                logger.error(f"❌ Unexpected error getting token for scope {cache_key}: {str(e)}")
                return None
    
    def clear_cache(self):
        """Clear all cached tokens."""
        with self._lock:
            self._token_cache.clear()
            logger.debug("🗑️ Token cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the token cache."""
        with self._lock:
            current_time = time.time()
            valid_tokens = 0
            expired_tokens = 0
            total_cached = len(self._token_cache)
            
            scope_details = {}
            for scope, (token, cache_time) in self._token_cache.items():
                scope_key = scope.split('/')[-1] if '/' in scope else scope
                if token.expires_on and token.expires_on > current_time:
                    valid_tokens += 1
                    scope_details[scope_key] = {
                        "status": "valid",
                        "expires_in": int(token.expires_on - current_time),
                        "cached_for": int(current_time - cache_time)
                    }
                else:
                    expired_tokens += 1
                    scope_details[scope_key] = {
                        "status": "expired",
                        "expired_since": int(current_time - token.expires_on) if token.expires_on else "unknown",
                        "cached_for": int(current_time - cache_time)
                    }
            
            return {
                "total_cached": total_cached,
                "valid_tokens": valid_tokens,
                "expired_tokens": expired_tokens,
                "cache_hit_ratio": f"{(valid_tokens / total_cached * 100):.1f}%" if total_cached > 0 else "0%",
                "scope_details": scope_details
            }


# Global token manager instance - initialized once per credential
_token_managers: Dict[str, TokenManager] = {}
_token_manager_lock = threading.Lock()


def get_token_manager(credential) -> TokenManager:
    """
    Get or create a token manager for the given credential.
    
    Args:
        credential: Azure credential object
        
    Returns:
        TokenManager instance
    """
    # Use credential object id as key to ensure one manager per credential
    credential_id = str(id(credential))
    
    with _token_manager_lock:
        if credential_id not in _token_managers:
            logger.debug(f"🔧 Creating new TokenManager for credential {credential_id}")
            _token_managers[credential_id] = TokenManager(credential)
        
        return _token_managers[credential_id]


def get_cached_token(credential, scope: str) -> Optional[AccessToken]:
    """
    Convenience function to get a cached token using the global token manager.
    
    Args:
        credential: Azure credential object
        scope: The Azure scope to request token for
        
    Returns:
        AccessToken object or None if authentication fails
    """
    token_manager = get_token_manager(credential)
    return token_manager.get_token(scope)


def clear_all_token_caches():
    """Clear all token caches across all token managers."""
    with _token_manager_lock:
        for manager in _token_managers.values():
            manager.clear_cache()
        logger.debug("🗑️ All token caches cleared")


def get_global_cache_stats() -> Dict[str, Any]:
    """Get statistics about all token caches."""
    with _token_manager_lock:
        total_stats = {
            "total_managers": len(_token_managers),
            "total_cached": 0,
            "valid_tokens": 0,
            "expired_tokens": 0,
            "manager_details": {}
        }
        
        for credential_id, manager in _token_managers.items():
            stats = manager.get_cache_stats()
            total_stats["total_cached"] += stats["total_cached"]
            total_stats["valid_tokens"] += stats["valid_tokens"]
            total_stats["expired_tokens"] += stats["expired_tokens"]
            
            # Store per-manager details
            total_stats["manager_details"][f"credential_{credential_id[-8:]}"] = stats
        
        # Calculate overall efficiency
        if total_stats["total_cached"] > 0:
            total_stats["overall_efficiency"] = f"{(total_stats['valid_tokens'] / total_stats['total_cached'] * 100):.1f}%"
        else:
            total_stats["overall_efficiency"] = "0%"
        
        return total_stats


def log_authentication_efficiency():
    """Log authentication efficiency statistics for monitoring."""
    stats = get_global_cache_stats()
    
    if stats["total_cached"] > 0:
        logger.info(f"🎯 Authentication Efficiency: {stats['overall_efficiency']} cache hit rate")
        logger.info(f"📊 Token Cache Stats: {stats['valid_tokens']} valid, {stats['expired_tokens']} expired, {stats['total_managers']} managers")
        
        # Log details if debug enabled
        for manager_id, details in stats["manager_details"].items():
            if details["total_cached"] > 0:
                logger.debug(f"   🔧 {manager_id}: {details['cache_hit_ratio']} hit rate ({details['total_cached']} tokens)")
    else:
        logger.debug("📊 No tokens cached yet")
    
    # Include monitor statistics if available
    try:
        from .auth_monitor import log_auth_efficiency
        log_auth_efficiency()
    except ImportError:
        pass  # Monitor not available


def get_optimization_recommendations():
    """Get recommendations for optimizing authentication performance."""
    try:
        from .auth_monitor import optimize_authentication_settings
        return optimize_authentication_settings()
    except ImportError:
        return {
            "stats": get_global_cache_stats(),
            "recommendations": [],
            "overall_efficiency": "unknown"
        }
