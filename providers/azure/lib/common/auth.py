#!/usr/bin/env python3
"""
Azure authentication and credential management for both Global and China Azure.
Enhanced with singleton pattern and global token caching.
"""

import logging
import threading
import time
from functools import lru_cache
from azure.identity import DefaultAzureCredential, AzureCliCredential, ChainedTokenCredential, AzureAuthorityHosts
from azure.core.exceptions import ClientAuthenticationError
from azure.mgmt.subscription import SubscriptionClient

# Import cloud configuration and token management
from .config import get_current_cloud_info, AUTHORITY_HOST_MAPPING, AUTHORITY_INFO_MAPPING
from .token_manager import get_token_manager, get_global_cache_stats
from .china_cli_credential import ChinaAzureCliCredential

logger = logging.getLogger(__name__)

# Global credential lock for thread safety
_credential_lock = threading.Lock()
_global_credential = None
_current_cloud_env = None  # Track current cloud environment


def get_azure_credential(cloud_env=None):
    """
    Get Azure credentials using DefaultAzureCredential with singleton pattern.
    Enhanced to handle cloud-specific resource endpoints for better Azure CLI compatibility.
    
    Args:
        cloud_env (str, optional): Cloud environment ('global', 'china', 'government')
                                  If None, will auto-detect
    
    This configuration works with both Global Azure and China Azure using
    appropriate authority hosts for enhanced security and compatibility.
    Ensures only one credential instance is created for maximum efficiency.
    """
    global _global_credential, _current_cloud_env
    
    with _credential_lock:
        # Determine the target cloud environment
        if cloud_env:
            target_cloud = cloud_env
        else:
            target_cloud, _ = get_current_cloud_info()
        
        # Reset credential if cloud environment changed
        if _current_cloud_env and _current_cloud_env != target_cloud:
            logger.debug(f"🔄 Cloud environment changed from {_current_cloud_env} to {target_cloud}, resetting credential")
            _global_credential = None
        
        if _global_credential is None:
            logger.debug("🔧 Creating new Azure credential instance")
            
            # Use provided cloud environment or auto-detect
            if cloud_env:
                from .config import get_cloud_config
                cloud_config = get_cloud_config(cloud_env)
                detected_cloud = cloud_env
                logger.debug(f"🌍 Using specified cloud environment: {cloud_config['name']}")
            else:
                # Get current cloud configuration to determine appropriate authority
                detected_cloud, cloud_config = get_current_cloud_info()
                logger.debug(f"🌍 Auto-detected cloud environment: {cloud_config['name']}")
            
            # Get the appropriate authority for the detected cloud
            authority = AUTHORITY_HOST_MAPPING.get(detected_cloud, AzureAuthorityHosts.AZURE_PUBLIC_CLOUD)
            
            logger.debug(f"🔐 Using authority host: {authority}")
            
            # Create appropriate credential based on cloud environment
            if detected_cloud == 'china':
                logger.debug("🇨🇳 China cloud detected, using custom China CLI credential")
                logger.info("💡 Ensure Azure CLI is configured for China: az cloud set --name AzureChinaCloud && az login")
                
                # Use the custom China CLI credential that handles endpoints properly
                _global_credential = ChinaAzureCliCredential()
                
            else:
                # For Global and Government clouds, use standard DefaultAzureCredential
                logger.debug(f"🌍 Creating DefaultAzureCredential for {detected_cloud} cloud")
                _global_credential = DefaultAzureCredential(
                    authority=authority,  # Use cloud-specific authority host
                    exclude_environment_credential=True,
                    exclude_workload_identity_credential=True,
                    exclude_shared_token_cache_credential=True,
                    exclude_azure_powershell_credential=True,
                    exclude_azure_developer_cli_credential=True,
                    exclude_managed_identity_credential=True,
                    exclude_visual_studio_code_credential=True,
                    exclude_azure_cli_credential=False,  # Keep Azure CLI enabled for non-China clouds
                    exclude_interactive_browser_credential=True
                )
            
            # Update the current cloud environment tracker
            _current_cloud_env = detected_cloud
            logger.debug("✅ Azure credential instance created successfully with cloud-specific configuration")
        
        return _global_credential


def get_authentication_stats():
    """
    Get comprehensive authentication and token cache statistics.
    
    Returns:
        dict: Authentication statistics including credential and cache info
    """
    stats = {
        "credential_initialized": _global_credential is not None,
        "cache_stats": get_global_cache_stats()
    }
    
    logger.debug(f"📊 Authentication stats: {stats}")
    return stats


def clear_authentication_cache():
    """
    Clear all authentication caches and reset credential if needed.
    This will force re-authentication on next request.
    """
    global _global_credential
    
    with _credential_lock:
        # Clear all token caches
        from .token_manager import clear_all_token_caches
        clear_all_token_caches()
        
        # Optionally reset credential (uncomment if needed)
        # _global_credential = None
        
        logger.info("🗑️ Authentication cache cleared")


def check_authentication(cloud_env=None, credential=None):
    """
    Test Azure authentication and return success status.
    
    Args:
        cloud_env (str, optional): Cloud environment ('global', 'china', 'government')
                                  If None, will auto-detect
        credential (optional): Existing credential to test, if None will use singleton
    
    Returns:
        bool: True if authentication is successful, False otherwise
    """
    # Initialize defaults to ensure variables are always defined
    detected_cloud = None
    cloud_config = {}
    
    try:
        logger.info("🔐 Attempting Azure authentication...")
        
        # Use provided cloud environment or auto-detect
        if cloud_env:
            from .config import get_cloud_config
            cloud_config = get_cloud_config(cloud_env)
            detected_cloud = cloud_env
            logger.info(f"🌍 Using specified cloud environment: {cloud_config['name']}")
        else:
            # Get cloud configuration through auto-detection
            detected_cloud, cloud_config = get_current_cloud_info()
            logger.info(f"🌍 Auto-detected cloud environment: {cloud_config['name']}")
        
        # Use singleton credential for maximum efficiency
        if credential is None:
            credential = get_azure_credential(detected_cloud)
            logger.debug("🔧 Using singleton credential instance with correct cloud environment")
        else:
            logger.debug("🔧 Using provided credential instance")
        
        # Test authentication using SubscriptionClient, which is a more reliable way
        # to verify that credentials are working properly
        try:
            # Create subscription client to test authentication with correct cloud endpoint
            subscription_client = SubscriptionClient(
                credential,
                base_url=cloud_config['management_endpoint']
            )
            # Try to list subscriptions as a test of authentication
            list(subscription_client.subscriptions.list(top=1))
            
            logger.info("✅ Authentication SUCCESS: Successfully verified credentials using SubscriptionClient")
            # Attempt to log efficiency statistics, ignore errors to not impact authentication result
            try:
                from .token_manager import log_authentication_efficiency
                log_authentication_efficiency()
            except Exception:
                pass
            return True
        except Exception as sub_error:
            # Fall back to token-based verification if subscription client fails
            try:
                # Get token manager for efficient token handling
                token_manager = get_token_manager(credential)
                
                # Test the credential by requesting a token for the appropriate Graph API
                auth_start_time = time.time()
                token = token_manager.get_token(cloud_config['graph_scope'])
                auth_duration = time.time() - auth_start_time
                
                if token and token.token:
                    logger.info("✅ Authentication SUCCESS: Azure credential is working (token-based verification)")
                    logger.info(f"🔑 Token expires at: {token.expires_on}")
                    logger.info(f"🌐 Using endpoints: {cloud_config['graph_endpoint']} | {cloud_config['management_endpoint']}")
                    logger.info(f"⚡ Authentication took {auth_duration:.2f} seconds")
                    # Attempt to log efficiency statistics, ignore errors to not impact authentication result
                    try:
                        from .token_manager import log_authentication_efficiency
                        log_authentication_efficiency()
                    except Exception:
                        pass
                    return True
                else:
                    logger.error("❌ Authentication FAILED: No token received")
                    return False
            except Exception as token_error:
                logger.error(f"❌ Authentication FAILED in both methods: {str(sub_error)} and {str(token_error)}")
                return False
            
    except ClientAuthenticationError as e:
        logger.error(f"❌ Authentication FAILED: {str(e)}")
        logger.info(f"💡 Try running: az login")
        if detected_cloud and 'china' in detected_cloud:
            scope = cloud_config.get('graph_scope', '<graph_scope>')
            logger.info(f"💡 For China Azure, also try: az login --scope {scope}")
        return False
    except Exception as e:
        logger.error(f"❌ Authentication FAILED: Unexpected error - {str(e)}")
        return False


def get_authority_info():
    """
    Get detailed information about the authority being used for authentication.
    
    Returns:
        dict: Information about current authority configuration
    """
    detected_cloud, cloud_config = get_current_cloud_info()
    
    # authority info mapping moved to config
    
    authority_info = AUTHORITY_INFO_MAPPING.get(detected_cloud, AUTHORITY_INFO_MAPPING['global'])
    
    return {
        'detected_cloud': detected_cloud,
        'cloud_name': cloud_config['name'],
        'authority_host': authority_info['host'],
        'authority_name': authority_info['name'],
        'authority_description': authority_info['description'],
        'graph_endpoint': cloud_config['graph_endpoint'],
        'management_endpoint': cloud_config['management_endpoint']
    }


def demonstrate_authority_usage():
    """
    Demonstrate the proper usage of authority with DefaultAzureCredential.
    This function shows how different Azure cloud environments use different authorities.
    """
    logger.info("🔐 Azure Authority Hosts Configuration Demo")
    logger.info("=" * 60)
    
    # Show all available authority hosts
    logger.info("📋 Available Azure Authority Hosts:")
    logger.info(f"   • AZURE_PUBLIC_CLOUD: {AzureAuthorityHosts.AZURE_PUBLIC_CLOUD}")
    logger.info(f"   • AZURE_CHINA: {AzureAuthorityHosts.AZURE_CHINA}")
    logger.info(f"   • AZURE_GOVERNMENT: {AzureAuthorityHosts.AZURE_GOVERNMENT}")
    
    # Show current configuration
    auth_info = get_authority_info()
    logger.info(f"\n🌍 Current Configuration:")
    logger.info(f"   • Detected Cloud: {auth_info['detected_cloud']}")
    logger.info(f"   • Cloud Name: {auth_info['cloud_name']}")
    logger.info(f"   • Authority Host: {auth_info['authority_host']}")
    logger.info(f"   • Authority Name: {auth_info['authority_name']}")
    logger.info(f"   • Description: {auth_info['authority_description']}")
    logger.info(f"   • Graph Endpoint: {auth_info['graph_endpoint']}")
    logger.info(f"   • Management Endpoint: {auth_info['management_endpoint']}")
    
    logger.info("\n💡 Usage Example:")
    logger.info("   from azure.identity import DefaultAzureCredential, AzureAuthorityHosts")
    logger.info(f"   credential = DefaultAzureCredential(authority='{auth_info['authority_host']}')")
    
    logger.info("=" * 60)
