"""
This module provides a simple, threaded web server to serve the HTML report.
"""

import os
import socket
import threading
import time
import logging
from http.server import HTTPServer, SimpleHTTPRequestHandler
# Disable SO_REUSEADDR to avoid permission errors in restricted environments
HTTPServer.allow_reuse_address = False


logger = logging.getLogger(__name__)

def find_available_port(start_port: int = 8000, max_attempts: int = 100) -> int:
    """
    Find an available port starting from start_port.
    Falls back to OS-assigned port if the start_port is not available.
    """
    # In most cases, just let the OS choose an available port
    # This is more reliable than trying to test port availability
    return 0  # OS will choose an available port

def start_web_server(html_file: str, port: int, daemon: bool = True) -> threading.Thread:
    """
    Start a simple HTTP server in a separate thread.
    Returns thread with actual_port attribute set.
    """
    html_dir = os.path.dirname(os.path.abspath(html_file))
    
    class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory=html_dir, **kwargs)
        
        def log_message(self, format, *args):
            # Suppress server log messages
            pass
    
    class ServerThread(threading.Thread):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.actual_port = port
            self.server = None
    
    def run_server(thread_obj):
        try:
            # Try the requested port first
            try:
                with HTTPServer(('127.0.0.1', port), CustomHTTPRequestHandler) as httpd:
                    thread_obj.server = httpd
                    thread_obj.actual_port = httpd.server_address[1]
                    logger.info(f"Starting web server on http://127.0.0.1:{thread_obj.actual_port}")
                    logger.info(f"Serving directory: {html_dir}")
                    httpd.serve_forever()
            except OSError as e:
                if e.errno == 98 or "Address already in use" in str(e):
                    # Port is in use, try with OS-chosen port
                    logger.info(f"Port {port} is in use, letting OS choose a port")
                    with HTTPServer(('127.0.0.1', 0), CustomHTTPRequestHandler) as httpd:
                        thread_obj.server = httpd
                        thread_obj.actual_port = httpd.server_address[1]
                        logger.info(f"Starting web server on http://127.0.0.1:{thread_obj.actual_port}")
                        logger.info(f"Serving directory: {html_dir}")
                        httpd.serve_forever()
                else:
                    logger.error(f"Web server OS error: {e}")
                    raise
        except Exception as e:
            logger.error(f"Web server error: {e}")
            raise
    
    server_thread = ServerThread(target=lambda: run_server(server_thread), daemon=daemon)
    server_thread.start()
    
    # Give the server a moment to start, but don't hang if there are issues
    try:
        time.sleep(0.1)  # Shorter wait time
    except:
        pass
    
    return server_thread
    
    return server_thread

# Contains AI-generated edits.
class SimpleWebServer:
    """
    Simple wrapper around HTTPServer to start and stop serving.
    """
    def __init__(self, host: str = 'localhost', port: int = 0):
        # Create a simple HTTP server on given host and port
        handler = SimpleHTTPRequestHandler
        self.httpd = HTTPServer((host, port), handler)

    def start(self):
        """Start the HTTP server (blocking call)."""
        self.httpd.serve_forever()

    def stop(self):
        """Stop the HTTP server."""
        self.httpd.shutdown()
