#!/usr/bin/env python3
"""
Custom Azure CLI credential that handles resource endpoint issues for different cloud environments.
This credential ensures that Azure CLI uses the correct cloud-specific endpoints.

This module provides CloudAwareAzureCliCredential which automatically detects the cloud environment
and uses the appropriate endpoints from config.py. It supports Azure Global, China, and Government clouds.

For backward compatibility, ChinaAzureCliCredential is provided as an alias.
"""

import logging
import subprocess
import json
from typing import Optional
from azure.core.credentials import AccessToken
from azure.core.exceptions import ClientAuthenticationError
from azure.identity import CredentialUnavailableError

from .config import get_cloud_config, detect_azure_cloud, CLOUD_ENVIRONMENTS

logger = logging.getLogger(__name__)


class CloudAwareAzureCliCredential:
    """
    Custom Azure CLI credential that uses cloud-specific endpoints.
    
    This credential solves the issue where the standard Azure CLI credential
    may use incorrect endpoints. It automatically detects the cloud environment
    or allows explicit cloud specification.
    """
    
    def __init__(self, cloud_env: Optional[str] = None, process_timeout: int = 10):
        """
        Initialize the cloud-aware Azure CLI credential.
        
        Args:
            cloud_env: Specific cloud environment ('global', 'china', 'government')
                      If None, will auto-detect from Azure CLI configuration
            process_timeout: Seconds to wait for Azure CLI process to respond
        """
        self.process_timeout = process_timeout
        self.cloud_env = cloud_env or detect_azure_cloud()
        self.cloud_config = get_cloud_config(self.cloud_env)
        
        logger.debug(f"🌍 Initialized credential for {self.cloud_config['name']} environment")
        
    def get_token(self, *scopes: str, **kwargs) -> AccessToken:
        """
        Request an access token for the specified scopes using Azure CLI with cloud-specific endpoints.
        
        Args:
            *scopes: Desired scopes for the access token
            **kwargs: Additional keyword arguments (ignored for compatibility)
            
        Returns:
            AccessToken: An access token for the specified cloud
            
        Raises:
            CredentialUnavailableError: If Azure CLI is not available or not logged in
            ClientAuthenticationError: If authentication fails
        """
        if not scopes:
            raise ValueError("At least one scope must be specified")
            
        # Determine the appropriate resource endpoint based on scope
        scope = scopes[0]
        resource = self.cloud_config['management_endpoint']  # Default to management endpoint
        
        # Handle different scope types using cloud configuration
        if "graph" in scope.lower():
            resource = self.cloud_config['graph_endpoint']
        elif "management" in scope.lower():
            resource = self.cloud_config['management_endpoint']
        else:
            # For other scopes, extract the base URL if possible
            if scope.endswith("/.default"):
                # Remove '/.default' suffix
                suffix = '/.default'
                resource = scope[:-len(suffix)]
            else:
                # Default to management endpoint
                resource = self.cloud_config['management_endpoint']
        
        logger.debug(f"� Using {self.cloud_config['name']} resource endpoint: {resource}")
        
        try:
            # Build Azure CLI command with cloud-specific resource
            cmd = [
                'az', 'account', 'get-access-token',
                '--output', 'json',
                '--resource', resource
            ]
            
            logger.debug(f"🔧 Executing Azure CLI command: {' '.join(cmd)}")
            
            # Execute the Azure CLI command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.process_timeout,
                check=False  # Don't raise exception on non-zero exit
            )
            
            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                
                if "not logged in" in error_msg.lower() or "please run 'az login'" in error_msg.lower():
                    raise CredentialUnavailableError(
                        "Azure CLI not logged in. Please run 'az login' to authenticate."
                    )
                elif "subscription" in error_msg.lower():
                    raise CredentialUnavailableError(
                        f"Azure CLI error: {error_msg}. Please check your subscription configuration."
                    )
                else:
                    raise ClientAuthenticationError(
                        f"Azure CLI authentication failed: {error_msg}"
                    )
            
            # Parse the JSON response
            try:
                token_data = json.loads(result.stdout)
            except json.JSONDecodeError as e:
                raise ClientAuthenticationError(
                    f"Failed to parse Azure CLI response: {e}"
                )
            
            # Extract token information
            access_token = token_data.get('accessToken')
            expires_on = token_data.get('expiresOn')
            
            if not access_token:
                raise ClientAuthenticationError(
                    "Azure CLI did not return an access token"
                )
            
            # Convert expires_on to timestamp
            try:
                import datetime
                if isinstance(expires_on, str):
                    # Parse the timestamp format from Azure CLI
                    dt = datetime.datetime.fromisoformat(expires_on.replace('Z', '+00:00'))
                    expires_on_timestamp = int(dt.timestamp())
                else:
                    expires_on_timestamp = int(expires_on)
            except (ValueError, TypeError):
                logger.warning(f"⚠️ Failed to parse expires_on: {expires_on}, setting to 1 hour from now")
                import time
                expires_on_timestamp = int(time.time() + 3600)  # 1 hour from now
            
            logger.debug(f"✅ Successfully obtained {self.cloud_config['name']} token (expires: {expires_on_timestamp})")
            
            return AccessToken(access_token, expires_on_timestamp)
            
        except subprocess.TimeoutExpired:
            raise CredentialUnavailableError(
                f"Azure CLI command timed out after {self.process_timeout} seconds"
            )
        except FileNotFoundError:
            raise CredentialUnavailableError(
                "Azure CLI is not installed or not found in PATH"
            )
        except Exception as e:
            if isinstance(e, (CredentialUnavailableError, ClientAuthenticationError)):
                raise
            else:
                raise ClientAuthenticationError(f"Unexpected error in {self.cloud_config['name']} Azure CLI credential: {e}")
    
    def close(self):
        """Close the credential. No-op for this implementation."""
        pass

# Backward compatibility alias
ChinaAzureCliCredential = CloudAwareAzureCliCredential

# Contains AI-generated edits.
