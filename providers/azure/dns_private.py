#!/usr/bin/env python3
"""
Azure Private DNS and related resources reporting tool.
"""
import sys
import os
import time
import logging
import traceback
import webbrowser

from pathlib import Path
import argparse

from providers.azure.lib.common.config import setup_logging, get_current_cloud_info
from providers.azure.lib.common.auth import check_authentication, get_azure_credential
from providers.azure.lib.common.azure_resources import get_subscriptions
from providers.azure.lib.common.web_server import find_available_port, start_web_server
from providers.azure.lib.dns_private.data import (
    list_private_dns_zones,
    list_virtual_network_links,
    list_private_endpoints,
    list_private_resolvers,
    list_dns_forwarding_rulesets,
    list_forwarding_rules,
    build_resolver_hierarchy,
    list_all_dns_resources_parallel
)
from providers.azure.lib.dns_private.html_report import generate_dns_private_html_report

logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(
        description="Azure DNS Private Resources Reporting Tool"
    )
    parser.add_argument('--debug', '-d', action='store_true', help='Enable debug logging')
    parser.add_argument('--cloud', '-c', choices=['global', 'china'], help='Azure cloud environment')
    parser.add_argument('--web', '-w', action='store_true', help='Serve HTML via web server')
    return parser.parse_args()

def main():
    args = None
    try:
        args = parse_args()
        setup_logging(args.debug)

        if args.cloud:
            detected_cloud = args.cloud
            _, cloud_config = get_current_cloud_info()
        else:
            detected_cloud, cloud_config = get_current_cloud_info()
        logger.info(f"🌏 Azure Cloud: {cloud_config['name']}")

        logger.info("🔐 Testing Azure authentication...")
        credential = get_azure_credential(detected_cloud)
        if not check_authentication(detected_cloud, credential):
            logger.error("❌ Authentication failed")
            sys.exit(1)
        logger.info("✅ Authentication successful")

        logger.info("🔍 Retrieving subscriptions...")
        subscriptions = get_subscriptions(credential)
        if not subscriptions:
            logger.warning("No subscriptions found")
            sys.exit(0)

        # Parallel retrieval of DNS private resources per subscription
        from concurrent.futures import ThreadPoolExecutor, as_completed
        def fetch_for_sub(sub):
            sid = sub.get('subscriptionId')
            sname = sub.get('displayName')
            logger.info(f"📡 Processing subscription: {sname} ({sid})")
            
            try:
                # Use the new parallel function to fetch all resources concurrently
                results = list_all_dns_resources_parallel(credential, sid, sname)
                
                # Map results to expected format for backward compatibility
                return {
                    'subscription_id': sid,
                    'subscription_name': sname,
                    'zones': results.get('private_dns_zones', []),
                    'vnet_links': results.get('virtual_network_links', []),
                    'endpoints': results.get('private_endpoints', []),
                    'resolvers': results.get('private_resolvers', []),
                    'forwarding_rules': results.get('dns_forwarding_rulesets', []),
                    'forwarding_rules_detail': results.get('forwarding_rules', []),
                    'resolver_hierarchy': results.get('resolver_hierarchy', [])
                }
            except Exception as e:
                logger.error(f"Error fetching DNS resources for {sname}: {e}")
                return None
        subs_data = []
        with ThreadPoolExecutor() as executor:
            futures = {executor.submit(fetch_for_sub, sub): sub for sub in subscriptions}
            for future in as_completed(futures):
                result = future.result()
                if result:
                    subs_data.append(result)

        logger.debug(f"Data structure being passed to generate_dns_private_html_report: {subs_data}")
        output_file = generate_dns_private_html_report(subs_data, cloud_region=detected_cloud)
        abs_path = os.path.abspath(output_file)

        if args.web:
            port = find_available_port()
            thread = start_web_server(output_file, port, daemon=False)
            url = f"http://localhost:{port}/{os.path.basename(output_file)}"
            logger.info(f"🌐 Report served at: {url}")
            try:
                webbrowser.open(url)
            except Exception:
                pass
            try:
                thread.join()
            except Exception as e: # Change KeyboardInterrupt to Exception
                logger.warning(f"Web server thread error: {e}") # Log the error
        else:
            logger.info(f"📁 Report generated: {abs_path}")

    except Exception as e:
        logger.error(f"❌ Error: {e}")
        if args and args.debug:
            logger.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == '__main__':
    main()

# Contains AI-generated edits.