"""
Common CLI utilities for Alibaba provider subcommands.
"""
import logging

def setup_logging(debug: bool = False) -> logging.Logger:
    """
    Configure and return a logger for Alibaba CLI commands.

    Args:
        debug: If True, set log level to DEBUG; otherwise INFO.
    Returns:
        Configured Logger instance.
    """
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    return logging.getLogger(__name__)
