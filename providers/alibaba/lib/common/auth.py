# Alibaba Cloud authentication singleton for credential management
# Provides a thread-safe singleton for AlibabaCloud ResourceCenter client

import threading
from alibabacloud_resourcecenter20221201.client import Client as ResourceCenter20221201Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models

class AlibabaAuthSingleton:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(AlibabaAuthSingleton, cls).__new__(cls)
                    cls._instance._client = None
        return cls._instance

    def get_client(self):
        if self._client is None:
            credential = CredentialClient()
            config = open_api_models.Config(credential=credential)
            config.endpoint = 'resourcecenter.aliyuncs.com'
            self._client = ResourceCenter20221201Client(config)
        return self._client

# Usage:
# from providers.alibaba.lib.common.auth import AlibabaAuthSingleton
# client = AlibabaAuthSingleton().get_client()

# Contains AI-generated edits.
