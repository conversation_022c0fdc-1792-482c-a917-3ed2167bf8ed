import time
import random

def is_rate_limit_error(error):
    """
    Check if the error is a rate limit error based on error message.
    """
    error_msg = str(error).lower()
    rate_limit_indicators = [
        'user flow control',
        'rate limit',
        'throttling',
        'too many requests',
        'request was denied due to user flow control'
    ]
    return any(indicator in error_msg for indicator in rate_limit_indicators)

def retry_with_backoff(func, max_retries=3, base_delay=1, max_delay=300, rate_limit_delay=5400, logger=None):
    """
    Retry function with exponential backoff and special handling for rate limits.

    Args:
        func: Function to retry
        max_retries: Maximum number of retries for non-rate-limit errors
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds for exponential backoff
        rate_limit_delay: Delay in seconds for rate limit errors (90 minutes = 5400 seconds)
        logger: Logger instance
    """
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as error:
            if is_rate_limit_error(error):
                if logger:
                    logger.warning(f"🚫 Rate limit detected: {error}")
                    logger.warning(f"⏰ Waiting {rate_limit_delay} seconds (90 minutes) before retrying...")
                    logger.info("💡 This is normal behavior when API rate limits are reached. Please be patient.")
                time.sleep(rate_limit_delay)
                # After rate limit wait, try once more
                try:
                    if logger:
                        logger.info("🔄 Retrying after rate limit wait...")
                    return func()
                except Exception as retry_error:
                    if logger:
                        logger.error(f"❌ Failed even after rate limit wait: {retry_error}")
                    raise retry_error
            elif attempt < max_retries:
                delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
                if logger:
                    logger.warning(f"⚠️ Attempt {attempt + 1}/{max_retries + 1} failed: {error}")
                    logger.info(f"🔄 Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
            else:
                if logger:
                    logger.error(f"❌ All {max_retries + 1} attempts failed. Last error: {error}")
                raise error
