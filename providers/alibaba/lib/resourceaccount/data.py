# -*- coding: utf-8 -*-
"""
Alibaba Resource Directory Account data module.
Handles account data pulling and transformation for frontend consumption.
Now supports hierarchical folder structure.
"""
import logging
from alibabacloud_resourcedirectorymaster20220419.client import Client as ResourceDirectoryMaster20220419Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_resourcedirectorymaster20220419 import models as resource_directory_master_20220419_models
from alibabacloud_tea_util import models as util_models
import json
import time
import random

import concurrent.futures
import math

from providers.alibaba.lib.common.retry import is_rate_limit_error, retry_with_backoff


def fetch_resource_directory_accounts(account_filter=None, logger=None):
    """
    Fetch all accounts from Alibaba Resource Directory.
    Returns a list of account dicts.
    Parallelized fetching and normalization.
    """
    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcedirectory.aliyuncs.com'

    rd_client = ResourceDirectoryMaster20220419Client(config)
    runtime = util_models.RuntimeOptions()
    page_size = 100

    def fetch_page(page_number):
        acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
            page_size=page_size,
            page_number=page_number,
            max_results=page_size
        )
        def make_request():
            return rd_client.list_accounts_with_options(acc_request, runtime)
        result = retry_with_backoff(make_request, logger=logger)
        body = getattr(result, 'body', None)
        accounts_obj = getattr(body, 'accounts', None) if body else None
        acc_list = getattr(accounts_obj, 'account', None) if accounts_obj else None
        if isinstance(acc_list, list):
            return acc_list
        elif acc_list is not None:
            return [acc_list]
        return []

    # Fetch first page and metadata
    first_page_objs = fetch_page(1)
    first_result = retry_with_backoff(lambda: rd_client.list_accounts_with_options(
        resource_directory_master_20220419_models.ListAccountsRequest(
            page_size=page_size,
            page_number=1,
            max_results=page_size
        ), runtime), logger=logger)
    body1 = getattr(first_result, 'body', None)
    metadata = {
        'TotalCount': getattr(body1, 'TotalCount', None) or getattr(body1, 'total_count', None),
        'RequestId': getattr(body1, 'RequestId', None) or getattr(body1, 'requestId', None),
        'PageSize': getattr(body1, 'PageSize', None) or getattr(body1, 'pageSize', None),
        'PageNumber': getattr(body1, 'PageNumber', None) or getattr(body1, 'pageNumber', None)
    }
    total_count = metadata.get('TotalCount') or 0
    total_pages = math.ceil(total_count / page_size) if total_count else 1

    # Assemble all account objects
    all_acc_objs = list(first_page_objs)
    if total_pages > 1:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = {executor.submit(fetch_page, pn): pn for pn in range(2, total_pages + 1)}
            for future in concurrent.futures.as_completed(futures):
                pn = futures[future]
                try:
                    page_accs = future.result()
                except Exception as e:
                    if logger:
                        logger.error(f"Error fetching page {pn}: {e}")
                    page_accs = []
                all_acc_objs.extend(page_accs)

    # Normalize accounts in parallel
    with concurrent.futures.ThreadPoolExecutor() as executor:
        normalized = list(executor.map(_normalize_account_object, all_acc_objs))
    accounts = [acc for acc in normalized if acc]

    # Apply account filtering if specified
    if account_filter:
        if logger:
            logger.info(f"Applying account filter: {account_filter}")
        accounts = [acc for acc in accounts if account_filter.lower() in acc.get('display_name', '').lower()]
        if logger:
            logger.info(f"After account filtering, account count: {len(accounts)}")

    if logger:
        logger.info(f"Fetched all accounts, total count: {len(accounts)}")

    return accounts, metadata


def _normalize_account_object(acc):
    """
    Normalize account object to a consistent dict format.
    Handles both ListAccounts and GetAccount response formats.
    """
    if not isinstance(acc, dict) and hasattr(acc, '__dict__'):
        acc = acc.__dict__

    if not isinstance(acc, dict):
        return None

    # Handle different field name variations from different APIs
    account_id = (acc.get('account_id') or acc.get('AccountId') or
                 acc.get('account_id') or acc.get('accountId'))
    display_name = (acc.get('display_name') or acc.get('DisplayName') or
                   acc.get('displayName'))
    account_name = (acc.get('account_name') or acc.get('AccountName') or
                   acc.get('accountName'))
    status = (acc.get('status') or acc.get('Status'))
    type_field = (acc.get('type') or acc.get('Type'))
    join_method = (acc.get('join_method') or acc.get('JoinMethod') or
                  acc.get('joinMethod'))
    join_time = (acc.get('join_time') or acc.get('JoinTime') or
                acc.get('joinTime'))
    modify_time = (acc.get('modify_time') or acc.get('ModifyTime') or
                  acc.get('modifyTime'))

    # Additional fields that might be available from GetAccount
    folder_id = (acc.get('folder_id') or acc.get('FolderId') or
                acc.get('folderId'))
    payer_account_id = (acc.get('payer_account_id') or acc.get('PayerAccountId') or
                       acc.get('payerAccountId'))
    resource_directory_id = (acc.get('resource_directory_id') or
                             acc.get('ResourceDirectoryId') or
                             acc.get('resourceDirectoryId'))
    identity_information = acc.get('identity_information') or acc.get('IdentityInformation')
    resource_directory_path = acc.get('resource_directory_path') or acc.get('ResourceDirectoryPath')
    tags = acc.get('tags') or acc.get('Tags')
    location = acc.get('location') or acc.get('Location')
    email_status = acc.get('email_status') or acc.get('EmailStatus')
    has_secure_mobile_phone = acc.get('has_secure_mobile_phone') or acc.get('HasSecureMobilePhone')
    request_id = acc.get('request_id') or acc.get('RequestId')

    if not account_id:
        return None

    return {
        'account_id': str(account_id),
        'display_name': display_name or account_name or f'Account-{account_id}',
        'account_name': account_name or display_name or f'Account-{account_id}',
        'status': status,
        'type': type_field,
        'join_method': join_method,
        'join_time': str(join_time) if join_time else None,
        'modify_time': str(modify_time) if modify_time else None,
        'folder_id': str(folder_id) if folder_id else None,
        'payer_account_id': str(payer_account_id) if payer_account_id else None,
        'resource_directory_id': str(resource_directory_id) if resource_directory_id else None,
        'identity_information': identity_information,
        'resource_directory_path': resource_directory_path,
        'tags': tags,
        'location': location,
        'email_status': email_status,
        'has_secure_mobile_phone': has_secure_mobile_phone,
        'request_id': request_id
    }


def calculate_account_stats(accounts):
    """
    Calculate summary statistics for accounts.
    Returns a dict of stats.
    """
    stats = {
        'total_accounts': len(accounts),
        'active_accounts': 0,
        'suspended_accounts': 0,
        'invited_accounts': 0,
        'created_accounts': 0
    }

    for acc in accounts:
        status = acc.get('status', '').lower()
        join_method = acc.get('join_method', '').lower()

        if status == 'createdsuccess':
            stats['active_accounts'] += 1
        elif status == 'suspended':
            stats['suspended_accounts'] += 1

        if join_method == 'invited':
            stats['invited_accounts'] += 1
        elif join_method == 'created':
            stats['created_accounts'] += 1

    return stats


def create_resource_directory_client():
    """
    Create and return a Resource Directory client.
    """
    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcedirectory.aliyuncs.com'
    return ResourceDirectoryMaster20220419Client(config)


def fetch_root_folders(client, runtime, logger=None):
    """
    Fetch root folders (folders without parent).
    Returns a list of folder dicts.
    """
    if logger:
        logger.info("Fetching root folders...")
    page_number = 1
    page_size = 50
    all_folders = []
    while True:
        request = resource_directory_master_20220419_models.ListFoldersForParentRequest()
        request.page_number = page_number
        request.page_size = page_size

        def make_request():
            return client.list_folders_for_parent_with_options(request, runtime)

        try:
            result = retry_with_backoff(make_request, logger=logger)
            body = getattr(result, 'body', None)

            folders = []
            folders_obj = getattr(body, 'folders', None)
            if folders_obj:
                folder_list = getattr(folders_obj, 'folder', None)
                if isinstance(folder_list, list):
                    for folder in folder_list:
                        folder_dict = _normalize_folder_object(folder)
                        if folder_dict:
                            folders.append(folder_dict)
                elif folder_list:
                    folder_dict = _normalize_folder_object(folder_list)
                    if folder_dict:
                        folders.append(folder_dict)

            if not folders:
                break
            all_folders.extend(folders)
            if logger:
                logger.info(f"Found {len(folders)} folders on page {page_number}")
            if len(folders) < page_size:
                break
            page_number += 1
        except Exception as e:
            if logger:
                logger.error(f"Error fetching root folders on page {page_number}: {e}")
            break
    return all_folders


def fetch_folders_for_parent(client, runtime, parent_folder_id, logger=None):
    """
    Fetch child folders for a given parent folder.
    Returns a list of folder dicts.
    """
    if logger:
        logger.debug(f"Fetching folders for parent: {parent_folder_id}")
    page_number = 1
    page_size = 50
    all_folders = []
    while True:
        request = resource_directory_master_20220419_models.ListFoldersForParentRequest(
            parent_folder_id=parent_folder_id
        )
        request.page_number = page_number
        request.page_size = page_size

        def make_request():
            return client.list_folders_for_parent_with_options(request, runtime)

        try:
            result = retry_with_backoff(make_request, logger=logger)
            body = getattr(result, 'body', None)

            folders = []
            folders_obj = getattr(body, 'folders', None)
            if folders_obj:
                folder_list = getattr(folders_obj, 'folder', None)
                if isinstance(folder_list, list):
                    for folder in folder_list:
                        folder_dict = _normalize_folder_object(folder)
                        if folder_dict:
                            folders.append(folder_dict)
                elif folder_list:
                    folder_dict = _normalize_folder_object(folder_list)
                    if folder_dict:
                        folders.append(folder_dict)

            if not folders:
                break
            all_folders.extend(folders)
            if logger:
                logger.debug(f"Found {len(folders)} folders for parent {parent_folder_id} on page {page_number}")
            if len(folders) < page_size:
                break
            page_number += 1
        except Exception as e:
            if logger:
                logger.debug(f"Error fetching folders for parent {parent_folder_id} on page {page_number}: {e}")
            break
    return all_folders


def fetch_accounts_for_parent(client, runtime, parent_folder_id, logger=None):
    """
    Fetch accounts directly under a given parent folder using ListAccountsForParent.
    Handles pagination by page_number and page_size.
    Returns a list of account dicts.
    """
    if logger:
        logger.debug(f"Fetching accounts for parent: {parent_folder_id}")

    account_ids = []
    page_number = 1
    page_size = 100

    while True:
        if logger:
            logger.debug(f"Fetching accounts for parent: {parent_folder_id}, page: {page_number}")
        request = resource_directory_master_20220419_models.ListAccountsForParentRequest(
            parent_folder_id=parent_folder_id,
            page_size=page_size,
            page_number=page_number
        )

        def make_request():
            return client.list_accounts_for_parent_with_options(request, runtime)

        try:
            result = retry_with_backoff(make_request, logger=logger)
            body = getattr(result, 'body', None)
        except Exception as e:
            if logger:
                logger.debug(f"Error fetching accounts for parent {parent_folder_id}, page {page_number}: {e}")
            break

        accounts_obj = getattr(body, 'accounts', None)
        current_page_ids = []
        if accounts_obj:
            account_list = getattr(accounts_obj, 'account', None)
            if isinstance(account_list, list):
                for account in account_list:
                    if not isinstance(account, dict) and hasattr(account, '__dict__'):
                        account = account.__dict__
                    account_id = account.get('account_id') or account.get('AccountId')
                    if account_id:
                        account_ids.append(str(account_id))
                        current_page_ids.append(account_id)
            elif account_list:
                if not isinstance(account_list, dict) and hasattr(account_list, '__dict__'):
                    account_list = account_list.__dict__
                account_id = account_list.get('account_id') or account_list.get('AccountId')
                if account_id:
                    account_ids.append(str(account_id))
                    current_page_ids.append(account_id)

        # Stop if no more accounts on this page
        if len(current_page_ids) < page_size:
            break
        page_number += 1

    # Retrieve detailed account info
    detailed_accounts = []
    for account_id in account_ids:
        account_detail = get_account_details(client, runtime, account_id, logger)
        if account_detail:
            detailed_accounts.append(account_detail)

    return detailed_accounts


def get_account_details(client, runtime, account_id, logger=None):
    """
    Get detailed account information using GetAccount API.
    Returns a normalized account dict.
    """
    if logger:
        logger.debug(f"Getting details for account: {account_id}")

    request = resource_directory_master_20220419_models.GetAccountRequest(
        account_id=account_id
    )

    def make_request():
        return client.get_account_with_options(request, runtime)

    try:
        result = retry_with_backoff(make_request, logger=logger)
        body = getattr(result, 'body', None)

        account_obj = getattr(body, 'account', None)
        if account_obj:
            return _normalize_account_object(account_obj)

        return None

    except Exception as e:
        if logger:
            logger.debug(f"Error getting details for account {account_id}: {e}")
        return None


def _normalize_folder_object(folder):
    """
    Normalize folder object to a consistent dict format.
    """
    if not isinstance(folder, dict) and hasattr(folder, '__dict__'):
        folder = folder.__dict__

    if not isinstance(folder, dict):
        return None

    folder_id = folder.get('folder_id') or folder.get('FolderId')
    folder_name = folder.get('folder_name') or folder.get('FolderName')
    create_time = folder.get('create_time') or folder.get('CreateTime')

    if not folder_id or not folder_name:
        return None

    return {
        'folder_id': str(folder_id),
        'folder_name': folder_name,
        'create_time': str(create_time) if create_time else None,
        'type': 'folder'
    }


def build_hierarchical_tree(client, runtime, account_filter=None, logger=None):
    """
    Build a hierarchical tree structure of folders and accounts.
    Returns a tree structure starting from root folders.
    """
    if logger:
        logger.info("Building hierarchical folder and account tree...")

    # Start with root folders
    root_folders = fetch_root_folders(client, runtime, logger)
    tree = []
    all_accounts = []

    def build_folder_tree(folder, depth=0):
        """Recursively build tree for a folder and its children."""
        if logger and depth < 3:  # Avoid too much debug output for deep trees
            logger.debug(f"{'  ' * depth}Processing folder: {folder['folder_name']}")

        # Get accounts directly under this folder
        accounts = fetch_accounts_for_parent(client, runtime, folder['folder_id'], logger)

        # Filter accounts if account_filter is provided
        if account_filter:
            filtered_accounts = []
            for acc in accounts:
                display_name = acc.get('display_name', '').lower()
                account_name = acc.get('account_name', '').lower()
                if (account_filter.lower() in display_name or
                    account_filter.lower() in account_name):
                    filtered_accounts.append(acc)
            accounts = filtered_accounts

        # Add accounts to global list for stats calculation
        all_accounts.extend(accounts)

        # Get child folders
        child_folders = fetch_folders_for_parent(client, runtime, folder['folder_id'], logger)

        # Build tree structure for this folder
        folder_node = {
            **folder,
            'accounts': accounts,
            'children': []
        }

        # Recursively process child folders
        for child_folder in child_folders:
            child_node = build_folder_tree(child_folder, depth + 1)
            folder_node['children'].append(child_node)

        return folder_node

    # Build tree for each root folder
    for root_folder in root_folders:
        tree_node = build_folder_tree(root_folder)
        tree.append(tree_node)

    # Also get accounts at the root level (not in any folder)
    try:
        root_accounts = fetch_accounts_for_parent(client, runtime, None, logger)
        if account_filter:
            filtered_root_accounts = []
            for acc in root_accounts:
                display_name = acc.get('display_name', '').lower()
                account_name = acc.get('account_name', '').lower()
                if (account_filter.lower() in display_name or
                    account_filter.lower() in account_name):
                    filtered_root_accounts.append(acc)
            root_accounts = filtered_root_accounts

        all_accounts.extend(root_accounts)

        if root_accounts:
            # Add a virtual root node for accounts not in folders
            root_node = {
                'folder_id': 'root',
                'folder_name': 'Root (No Folder)',
                'create_time': None,
                'type': 'folder',
                'accounts': root_accounts,
                'children': []
            }
            tree.insert(0, root_node)  # Insert at beginning

    except Exception as e:
        if logger:
            logger.debug(f"Could not fetch root-level accounts: {e}")

    if logger:
        logger.info(f"Built hierarchical tree with {len(tree)} root nodes and {len(all_accounts)} total accounts")

    return tree, all_accounts


def fetch_resource_directory_accounts_hierarchical(account_filter=None, logger=None):
    """
    Fetch all accounts from Alibaba Resource Directory in hierarchical structure.
    Returns a tuple of (hierarchical_tree, flat_account_list, metadata).

    Args:
        account_filter: Account name/display name to filter accounts (optional)
        logger: Logger instance (optional)
    """
    client = create_resource_directory_client()
    runtime = util_models.RuntimeOptions()

    try:
        tree, all_accounts = build_hierarchical_tree(client, runtime, account_filter, logger)

        # Create metadata (simplified since we're not using pagination for the tree)
        metadata = {
            'TotalCount': len(all_accounts),
            'RequestId': 'hierarchical-request',
            'PageSize': len(all_accounts),
            'PageNumber': 1
        }

        return tree, all_accounts, metadata

    except Exception as e:
        if logger:
            logger.error(f"Error fetching hierarchical accounts: {e}")
        raise


def calculate_hierarchical_stats(tree, all_accounts):
    """
    Calculate summary statistics for hierarchical account data.
    Returns a dict of stats including folder counts.
    """
    def count_folders(node):
        """Recursively count folders in tree."""
        count = 1  # Count this folder
        for child in node.get('children', []):
            count += count_folders(child)
        return count

    total_folders = sum(count_folders(node) for node in tree)

    stats = calculate_account_stats(all_accounts)
    stats.update({
        'total_folders': total_folders,
        'root_folders': len(tree)
    })

    return stats


def group_accounts_by_status(accounts):
    """
    Group accounts by status.
    Returns a dict: {status: [accounts]}
    """
    grouped = {}
    for acc in accounts:
        status = acc.get('status', 'Unknown')
        if status not in grouped:
            grouped[status] = []
        grouped[status].append(acc)
    return grouped
