#!/usr/bin/env python3
"""
Alibaba Resource Directory Account HTML report generator.
"""
import os
import json
from datetime import datetime
from typing import List, Dict, Any

def generate_resourceaccount_html_report(
    tree: List[Dict[str, Any]],
    accounts: List[Dict[str, Any]],
    stats: Dict[str, int]
) -> str:
    html_dir = "html"
    os.makedirs(html_dir, exist_ok=True)

    resourceaccount_dir = os.path.join(html_dir, "resourceaccount")
    os.makedirs(resourceaccount_dir, exist_ok=True)

    html_content = _generate_html_template(tree, accounts, stats)

    output_path = os.path.join(html_dir, "alibaba_resourceaccount_report.html")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    return os.path.abspath(output_path)

def _generate_html_template(
    tree: List[Dict[str, Any]],
    accounts: List[Dict[str, Any]],
    stats: Dict[str, Any]
) -> str:
    import re

    # Transform tree structure for frontend consumption
    def transform_tree_node(node):
        """Transform a tree node for frontend display."""
        return {
            'folder_id': node.get('folder_id', ''),
            'folder_name': node.get('folder_name', 'Unknown'),
            'create_time': node.get('create_time', ''),
            'type': node.get('type', 'folder'),
            'accounts': [
                {
                    'name': acc.get('display_name', 'Unknown'),
                    'id': acc.get('account_id', ''),
                    'status': acc.get('status', 'Unknown'),
                    'type': acc.get('type', 'Unknown'),
                    'join_method': acc.get('join_method', 'Unknown'),
                    'join_time': acc.get('join_time', ''),
                    'modify_time': acc.get('modify_time', ''),
                    'display_name': acc.get('display_name', 'Unknown'),
                    'account_id': acc.get('account_id', ''),
                    'account_name': acc.get('account_name', 'Unknown'),
                    'folder_id': acc.get('folder_id', ''),
                    'resource_directory_id': acc.get('resource_directory_id', ''),
                    'identity_information': acc.get('identity_information', ''),
                    'resource_directory_path': acc.get('resource_directory_path', ''),
                    'tags': acc.get('tags', []),
                    'location': acc.get('location', ''),
                    'email_status': acc.get('email_status', ''),
                    'has_secure_mobile_phone': acc.get('has_secure_mobile_phone', False),
                    'request_id': acc.get('request_id', '')
                }
                for acc in node.get('accounts', [])
            ],
            'children': [transform_tree_node(child) for child in node.get('children', [])]
        }

    # Transform the entire tree
    hierarchical_tree = [transform_tree_node(node) for node in tree]

    data_json = json.dumps(hierarchical_tree, indent=2, default=str)

    stats_payload = {
        'total_accounts': stats.get('total_accounts', 0),
        'active_accounts': stats.get('active_accounts', 0),
        'suspended_accounts': stats.get('suspended_accounts', 0),
        'invited_accounts': stats.get('invited_accounts', 0),
        'created_accounts': stats.get('created_accounts', 0),
        'total_folders': stats.get('total_folders', 0),
        'root_folders': stats.get('root_folders', 0),
        'TotalCount': stats.get('TotalCount'),
        'RequestId': stats.get('RequestId'),
        'PageSize': stats.get('PageSize'),
        'PageNumber': stats.get('PageNumber')
    }
    stats_json = json.dumps(stats_payload, indent=2, default=str)

    html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alibaba Resource Directory Accounts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="resourceaccount/resourceaccount_report.css">
</head>
<body class="bg-orange-50 text-gray-900 font-sans">
    <div class="min-h-screen">
        <header class="bg-orange-600 shadow-lg border-b border-orange-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold text-white">👥 Alibaba Resource Directory Accounts</h1>
                    <div class="text-sm text-orange-100">Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</div>
                </div>
            </div>
        </header>
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">📊 Summary Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="summaryStats"></div>
            </div>
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">📋 Metadata</h2>
                <div class="bg-white p-4 rounded shadow">
                    <ul class="list-disc list-inside">
                        <li>Total Count: {stats_payload['TotalCount']}</li>
                        <li>Request ID: {stats_payload['RequestId']}</li>
                        <li>Page Size: {stats_payload['PageSize']}</li>
                        <li>Page Number: {stats_payload['PageNumber']}</li>
                        <li>Total Folders: {stats_payload['total_folders']}</li>
                        <li>Root Folders: {stats_payload['root_folders']}</li>
                    </ul>
                </div>
            </div>
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">🌳 Hierarchical Account Structure</h2>
                <div id="accountTree"></div>
            </div>
        </main>
    </div>
    <script>
        window.resourceAccountTree = {data_json};
        window.resourceAccountStats = {stats_json};
    </script>
    <script src="resourceaccount/resourceaccount_report.js"></script>
</body>
</html>"""
    return html_template
