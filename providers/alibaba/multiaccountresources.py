# -*- coding: utf-8 -*-
# Alibaba multiaccountresources subcommand
# This file is auto-generated, don't edit it. Thanks.

from providers.alibaba.lib.common.auth import AlibabaAuthSingleton
from alibabacloud_tea_util import models as util_models
import sys
import argparse
import traceback

import os
from providers.alibaba.lib.multiaccountresource import data as multi_data
from providers.alibaba.lib.multiaccountresource import html_report as multi_html
from providers.alibaba.lib.common.cli import setup_logging

def check_multi_account_resource_status(logger):
    """
    Returns True if Alibaba Multi-Account Resource Center service is enabled, False otherwise.
    """
    client = AlibabaAuthSingleton().get_client()
    runtime = util_models.RuntimeOptions()
    try:
        logger.debug("Invoking get_multi_account_resource_center_service_status_with_options...")
        result = client.get_multi_account_resource_center_service_status_with_options(runtime)
        logger.debug(f"Raw result: {result}")
        status = None
        import json
        # Normalize result to dict
        if isinstance(result, str):
            try:
                result = json.loads(result)
                logger.debug(f"Parsed result from JSON string: {result}")
            except Exception as e:
                logger.error(f"Failed to parse result as JSON: {e}")
        elif not isinstance(result, dict) and hasattr(result, '__dict__'):
            result = result.__dict__
            logger.debug(f"Converted result to dict via __dict__: {result}")
        # Extract body
        body = result.get('body') if isinstance(result, dict) else None
        logger.debug(f"body: {body}")
        if isinstance(body, str):
            try:
                body = json.loads(body)
                logger.debug(f"Parsed body from JSON string: {body}")
            except Exception as e:
                logger.error(f"Failed to parse body as JSON: {e}")
        elif not isinstance(body, dict) and hasattr(body, '__dict__'):
            body = body.__dict__
            logger.debug(f"Converted body to dict via __dict__: {body}")
        if isinstance(body, dict):
            status = body.get('ServiceStatus') or body.get('service_status')
            logger.debug(f"Extracted ServiceStatus from body: {status}")
        else:
            logger.warning(f"body is not a dict, cannot extract ServiceStatus: {body}")
        enabled = str(status).lower() == 'enabled'
        logger.info(f"Multi-Account Resource Center service enabled: {enabled}")
        return enabled
    except Exception as error:
        logger.error(f"Exception occurred: {error}")
        return False

def parse_args():
    """Parse command line arguments for Alibaba Multi-Account Resource Center status."""
    parser = argparse.ArgumentParser(
        description="Alibaba Multi-Account Resource Center status checker",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m providers.alibaba.multiaccountresources                           # Check if service is enabled
  python -m providers.alibaba.multiaccountresources --debug                  # Enable debug output
  python -m providers.alibaba.multiaccountresources -r cn-beijing            # Filter by region
  python -m providers.alibaba.multiaccountresources -a blz-p-ow-game         # Filter by account name
  python -m providers.alibaba.multiaccountresources -t ECS::Instance         # Filter by resource type
  python -m providers.alibaba.multiaccountresources -r cn-beijing -a blz-p-ow-game -t ECS::Instance  # Filter by region, account and resource type
        """
    )
    parser.add_argument(
        "--debug", "-d",
        action="store_true",
        help="Enable debug logging to show detailed debugging information"
    )
    parser.add_argument(
        "--region", "-r",
        type=str,
        default=None,
        help="Specify region to filter resources (e.g. cn-beijing). If not set, data for default regions (cn-beijing, cn-chengdu, cn-shenzhen, cn-hangzhou) will be fetched."
    )
    parser.add_argument(
        "--account-id",
        type=str,
        default=None,
        help="Filter resources by account ID (e.g. ***************). If not set, fetch data for all accounts."
    )
    parser.add_argument(
        "--resource-type", "-t",
        type=str,
        default=None,
        help="Filter resources by resource type (e.g. ECS::Instance, RDS::Database). If not set, fetch data for all resource types."
    )
    return parser.parse_args()


def get_rd_scope_from_env(logger):
    """
    Check and return RD_SCOPE from environment variables. Log a warning if not set.
    """
    import os
    rd_scope = os.environ.get('RD_SCOPE')
    if not rd_scope:
        logger.error("RD_SCOPE environment variable is not set. Exiting.")
        print("Error: RD_SCOPE environment variable is not set. Exiting.")
        sys.exit(1)
    logger.debug(f"RD_SCOPE found in environment: {rd_scope}")
    return rd_scope




def main():
    """Main entry point for Alibaba Multi-Account Resource Center status checker."""
    args = None
    logger = None
    try:
        args = parse_args()
        logger = setup_logging(args.debug)
        # Attach region, account, and resource_type to logger for downstream usage
        setattr(logger, 'region', args.region)
        setattr(logger, 'account_id', args.account_id)
        setattr(logger, 'resource_type', args.resource_type)
        logger.info("Starting Alibaba Multi-Account Resource Center status check...")
        enabled = check_multi_account_resource_status(logger)
        if enabled:
            logger.info("✅ Multi-Account Resource Center service is ENABLED.")
            print("✅ Multi-Account Resource Center service is ENABLED.")
            if args.account_id:
                rd_scope = args.account_id
                account_filter = None
            else:
                rd_scope = get_rd_scope_from_env(logger)
                account_filter = args.account_id
            region = getattr(logger, 'region', None)
            resource_type = getattr(logger, 'resource_type', None)
            resources = multi_data.fetch_multi_account_resources(rd_scope, region=region, account_filter=account_filter, resource_type_filter=resource_type, logger=logger)
            stats = multi_data.calculate_multiaccount_stats(resources)
            output_file = multi_html.generate_multiaccountresource_html_report(resources, stats)
            abs_path = os.path.abspath(output_file)
            logger.info(f"📄 Report generated successfully: {abs_path}")
            print(f"📄 Report generated: {abs_path}")
        else:
            logger.warning("❌ Multi-Account Resource Center service is DISABLED or unavailable.")
            print("❌ Multi-Account Resource Center service is DISABLED or unavailable.")
    except KeyboardInterrupt:
        if logger is None:
            logger = logging.getLogger(__name__)
        logger.info("🛑 Operation cancelled by user")
        print("🛑 Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        if logger is None:
            logger = logging.getLogger(__name__)
        logger.error(f"❌ Unexpected error: {e}")
        if args is not None and getattr(args, "debug", False):
            logger.debug(f"Traceback: {traceback.format_exc()}")
            print(f"Traceback: {traceback.format_exc()}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Contains AI-generated edits.
