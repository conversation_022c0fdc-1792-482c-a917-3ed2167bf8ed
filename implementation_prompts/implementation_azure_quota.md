# Azure Quota and Usage Report Implementation

## Overview
Implement a comprehensive Azure quota and usage report feature that follows the established development pattern in this project, specifically following the DNS private implementation structure and styling.

## Requirements

### 1. Follow DNS Private Implementation Pattern

**Main Module Structure** (`providers/azure/quota.py`):
- Follow the exact same structure as `providers/azure/dns_private.py`
- Include the same CLI argument pattern with `--debug`, `--cloud`, `--web` options
- Use identical authentication flow with `check_authentication()` and `get_azure_credential()`
- Implement parallel processing for multiple subscriptions using `ThreadPoolExecutor`
- Use the same logging pattern with emojis for user feedback (🔐, ✅, ❌, 📡, 🌐, etc.)
- Include web server integration for HTML report serving
- Follow the same error handling and graceful exit patterns

**Library Structure** (`providers/azure/lib/quota/`):
- Create `data.py` - for quota and usage data retrieval functions
- Create `html_report.py` - for HTML report generation
- Follow the same function naming conventions as DNS private implementation

**Data Module** (`providers/azure/lib/quota/data.py`):
- Implement helper functions to initialize SDK clients (`QuotaRequestApi`, `ComputeManagementClient`, etc.).
- Create specific functions for each quota type using the relevant SDK clients:
  - `list_compute_quotas()` - VM cores, instances, etc. (using `ComputeManagementClient`)
  - `list_network_quotas()` - VNets, public IPs, load balancers, etc. (using `NetworkManagementClient`)
  - `list_storage_quotas()` - Storage accounts, disk quotas (using `StorageManagementClient`)
  - `list_subscription_usage()` - Current usage vs quotas (using `ConsumptionManagementClient`)
  - `list_subscription_quotas()` - Service-specific limits (using `QuotaRequestApi`)
- Each function should:
  - Accept `credential`, `subscription_id`, and other necessary parameters.
  - Initialize the appropriate SDK client.
  - Use proper error handling and logging for SDK calls.
  - Return enriched data with subscription context.
  - Handle SDK exceptions gracefully.

**HTML Report Module** (`providers/azure/lib/quota/html_report.py`):
- Follow the same pattern as `dns_private/html_report.py`
- Implement `_calculate_quota_statistics()` function
- Generate comprehensive HTML report with embedded CSS and JavaScript
- Use the exact same CSS styling structure as DNS private

### 2. Azure SDKs to Implement

Use these Azure Python SDKs and their respective clients and methods:

**Azure Quota SDK (`azure-mgmt-quota`):**
- **Client**: `azure.mgmt.quota.QuotaRequestApi`
- **Operations**:
  - `quota.list(scope)`: To get quota information for a specific scope (e.g., subscription, provider).
  - `usages.list(scope)`: To get usage information for a specific scope.
- **Models**:
  - `azure.mgmt.quota.models.CurrentQuotaLimitBase`
  - `azure.mgmt.quota.models.CurrentUsagesBase`

**Compute Quotas (`azure-mgmt-compute`):**
- **Client**: `azure.mgmt.compute.ComputeManagementClient`
- **Operations**:
  - `usage.list(location)`
  - `virtual_machine_sizes.list(location)`

**Network Quotas (`azure-mgmt-network`):**
- **Client**: `azure.mgmt.network.NetworkManagementClient`
- **Operations**:
  - `usages.list(location)`

**Storage Quotas (`azure-mgmt-storage`):**
- **Client**: `azure.mgmt.storage.StorageManagementClient`
- **Operations**:
  - `usages.list_by_location(location)`

**Subscription Usage (`azure-mgmt-consumption`):**
- **Client**: `azure.mgmt.consumption.ConsumptionManagementClient`
- **Operations**:
  - `usage_details.list(scope)`

### 3. CSS Styling Requirements

**MUST use the exact same CSS styling as DNS private:**
- Copy the complete CSS structure from `html/dns_private/dns_private_report.css`
- Maintain the same color scheme: `#0078d4` primary blue, `#106ebe` secondary
- Use identical grid layouts, card designs, and responsive breakpoints
- Keep the same typography: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto`
- Maintain the same header gradient: `linear-gradient(135deg, #0078d4, #106ebe)`
- Use the same stat card styling, table layouts, and hover effects

**Report Sections to Include:**
1. **Overview Statistics** - Total quotas vs usage across all services
2. **Compute Resources** - VM cores, instances, availability sets
3. **Network Resources** - VNets, subnets, public IPs, load balancers
4. **Storage Resources** - Storage accounts, disks, file shares
5. **Subscription Limits** - Service-specific limits and consumption
6. **Usage Trends** - Historical usage data if available
7. **Recommendations** - Quota increase suggestions based on current usage

### 4. Integration Requirements

**Main Azure Module Integration** (`providers/azure/__main__.py`):
- Add 'quota' to the supported module list alongside 'organization', 'user', 'vnet', 'dns-private'
- Follow the same import and execution pattern

**CLI Integration** (`cloudqx.py`):
- Add quota command support to the main CLI interface
- Follow the same pattern as existing Azure commands

### 5. Security and Best Practices

**Follow Combined Prompt Guidelines:**
- Implement input validation using `pydantic` or similar for structured data
- Use parameterized queries and avoid string concatenation
- Implement proper error handling and logging
- Use secure token management through existing `token_manager.py`
- Follow the project's established authentication patterns

**Python Best Practices:**
- Use type hints for all function parameters and return values
- Implement proper async/await patterns where beneficial
- Use context managers for resource handling
- Follow PEP 8 coding standards
- Include comprehensive docstrings

**Security Considerations:**
- Never log sensitive quota data or subscription details
- Use the existing credential management system
- Implement rate limiting for API calls
- Handle API quotas and throttling gracefully from the SDKs

### 6. Testing Requirements

**Create comprehensive tests** (`tests/test_quota_*.py`):
- `test_quota_data.py` - Test data retrieval functions
- `test_quota_html_report.py` - Test HTML report generation
- `test_quota_integration.py` - Test end-to-end functionality
- Follow the same testing patterns as existing DNS private tests
- Mock Azure SDK clients and their responses for reliable testing
- Test both successful scenarios and error conditions

### 7. Performance Requirements

**Parallel Processing:**
- Use `ThreadPoolExecutor` for concurrent SDK calls across subscriptions
- Implement proper resource batching to avoid API rate limits
- Cache quota data appropriately to minimize SDK calls
- Use async patterns where beneficial for I/O operations

**Monitoring and Logging:**
- Use the existing logging infrastructure
- Provide clear progress indicators for long-running operations
- Include performance metrics in debug mode
- Implement proper timeout handling for SDK clients

### 8. Output and Deployment

**HTML Report Features:**
- Interactive charts for quota vs usage visualization
- Sortable and filterable tables
- Drill-down capabilities by subscription/resource group
- Export functionality for data sharing
- Responsive design for mobile/tablet viewing

**File Organization:**
- Generate reports in `html/` directory following existing pattern
- Use descriptive filenames: `azure_quota_report_global.html`
- Include timestamp and cloud region in filename
- Support web server mode for local viewing

### 9. Documentation

**Include comprehensive documentation:**
- Update README.md with quota reporting instructions
- Add usage examples and common scenarios
- Document all API endpoints and data structures
- Include troubleshooting guide for common issues

### 10. Implementation Order

1. **Core Data Layer** - Implement `providers/azure/lib/quota/data.py`
2. **HTML Report Generator** - Create `providers/azure/lib/quota/html_report.py`
3. **Main Module** - Build `providers/azure/quota.py`
4. **Integration** - Update `__main__.py` and CLI integration
5. **Testing Suite** - Comprehensive test coverage
6. **Documentation** - Update all relevant documentation

## Success Criteria

- Report generates successfully for all Azure cloud environments (global, china)
- HTML output matches DNS private styling exactly
- All quota types are captured and displayed accurately
- Performance is acceptable for large enterprise tenants
- Error handling is robust and user-friendly
- Code follows all established patterns and best practices
- Tests provide comprehensive coverage
- Documentation is complete and actionable

## Notes

- Pay special attention to Azure API rate limits and implement appropriate throttling/backoff strategies with the SDKs
- Consider implementing caching for quota data that doesn't change frequently
- Ensure the report provides actionable insights, not just raw data
- Follow the existing pattern of enriching raw SDK data with additional context
- Maintain backward compatibility with existing authentication and configuration systems

<!-- Contains AI-generated edits. -->