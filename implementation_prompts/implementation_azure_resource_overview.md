# Azure Resource Overview - Product Requirements Document

## Executive Summary

Implement a comprehensive Azure Resource Overview feature that provides a hierarchical view of all Azure resources across subscriptions, organized by subscription → resource group → resource type → individual resources, with interactive filtering and sorting capabilities.

## Business Requirements

### Primary Goals
1. **Comprehensive Resource Visibility**: Provide a single view of all Azure resources across all accessible subscriptions
2. **Hierarchical Organization**: Structure resources in a logical hierarchy sorted by resource count (descending)
3. **Interactive Filtering**: Enable filtering by subscription, resource group, and resource type
4. **Visual Clarity**: Use intuitive icons, count bubbles, and modern UI design
5. **Performance**: Efficiently handle large numbers of resources using Azure Resource Graph API

### Success Criteria
- Successfully retrieve and display resources from all accessible subscriptions
- Interactive filtering updates counts and hierarchy dynamically
- Consistent sorting by resource count at all hierarchy levels
- Responsive design that works across different screen sizes
- Web server integration with automatic browser launch

## Technical Requirements

### Core Functionality

#### Data Collection
- **Azure Resource Graph API**: Use Resource Graph client for efficient cross-subscription queries
- **Authentication**: Leverage existing authentication infrastructure (DefaultAzureCredential)
- **Pagination**: Handle large result sets with proper pagination
- **Error Handling**: Graceful error handling for inaccessible subscriptions or resources

#### Data Structure
```
Subscription Level:
  - subscription_id: string
  - subscription_name: string
  - resource_count: integer
  - resource_groups: array

Resource Group Level:
  - name: string
  - resource_count: integer
  - resource_types: array

Resource Type Level:
  - type: string
  - resource_count: integer
  - resources: array

Resource Level:
  - id: string
  - name: string
  - type: string
  - location: string
  - resource_group: string
```

#### Hierarchy and Sorting
- **Sort Order**: All levels sorted by resource count in descending order
- **Count Propagation**: Parent counts include all descendant resources
- **Dynamic Updates**: Counts update when filters are applied

### User Interface Requirements

#### Summary Section
- **Total Statistics**: Display overall counts with visual count bubbles
- **Filter Status**: Show current filter selections
- **Visual Indicators**: Use consistent color scheme and typography

#### Filtering Controls
- **Subscription Filter**: Dropdown with all available subscriptions
- **Resource Group Filter**: Dropdown populated based on subscription selection
- **Resource Type Filter**: Dropdown with all unique resource types
- **Clear Filters**: Easy way to reset all filters

#### Hierarchical Display
- **Collapsible Sections**: All hierarchy levels should be expandable/collapsible
- **Visual Hierarchy**: Proper indentation and visual cues for hierarchy levels
- **Count Badges**: Resource count displays as badges/bubbles
- **Icons**: Appropriate emoji icons for different hierarchy levels (📁, 📂, 📊)

### Technical Implementation

#### Backend (Python)

**File Structure**:
```
providers/azure/resource_overview.py              # Main entry point
providers/azure/lib/resource_overview/
  ├── __init__.py                                  # Package initialization
  ├── data.py                                      # Resource data collection
  └── html_report.py                               # HTML report generation
```

**Dependencies**:
- `azure.identity.DefaultAzureCredential`
- `azure.mgmt.resource.subscriptions.SubscriptionClient`
- `azure.mgmt.resourcegraph.ResourceGraphClient`
- `azure.mgmt.resourcegraph.models.QueryRequest`

#### Frontend (HTML/JavaScript)

**File Structure**:
```
html/
  ├── resource_overview_report.html               # Main report file
  ├── resource_overview/
      ├── resource_overview_report.css            # Styles
      └── resource_overview_report.js             # Interactive functionality
```

**JavaScript Features**:
- Dynamic filtering with real-time count updates  
- Collapsible hierarchy sections
- Responsive design with Tailwind CSS
- Search functionality within hierarchies
- **CRITICAL**: Non-destructive filtering implementation to prevent filter reset bugs

### Design Patterns

#### Follow DNS-Private Pattern
- **Authentication Flow**: Reuse existing auth patterns
- **HTML Generation**: Follow same template structure
- **Web Server**: Use same port detection and server startup
- **Error Handling**: Consistent error handling patterns
- **Logging**: Same logging patterns and levels

#### Security Considerations
- **Input Validation**: Validate all user inputs and API responses
- **Output Encoding**: Properly encode all HTML output
- **Credential Security**: Never log or expose credentials
- **Resource Access**: Respect Azure RBAC permissions

## User Experience

### Command Line Interface
```bash
# Basic usage
./cloudqx.py azure resource-overview

# With web server
./cloudqx.py azure resource-overview -web

# With debug logging
./cloudqx.py azure resource-overview --debug

# Specify cloud environment
./cloudqx.py azure resource-overview --cloud global
```

### Web Interface Flow
1. **Page Load**: Display loading indicator while fetching data
2. **Summary Display**: Show total counts and statistics
3. **Hierarchy Navigation**: Allow expanding/collapsing sections
4. **Filtering**: Apply filters and see real-time updates
5. **Export Options**: Potential future feature for data export

### Performance Requirements
- **Load Time**: Initial page load under 30 seconds for 10,000+ resources
- **Filter Response**: Filter application under 1 second
- **Memory Usage**: Efficient memory usage for large datasets
- **Responsiveness**: UI remains responsive during operations

## Quality Assurance

### Testing Strategy
- **Unit Tests**: Test all data collection and processing functions
- **Integration Tests**: Test end-to-end functionality with mock data
- **Error Handling Tests**: Test various error scenarios
- **Performance Tests**: Test with large datasets

### Code Quality
- **Security**: Follow secure coding practices per coding guidelines
- **Documentation**: Comprehensive code documentation
- **Type Hints**: Full type hint coverage
- **Error Messages**: Clear, actionable error messages

## Deployment and Configuration

### File Locations
- **HTML Output**: Generated directly in `./html/` directory
- **Templates**: Embedded in Python code following existing patterns
- **Static Assets**: Inline CSS/JS for portability

### Configuration Options
- **Debug Mode**: Enhanced logging and error reporting
- **Cloud Selection**: Support for different Azure cloud environments
- **Web Server**: Optional web server with port auto-detection

## Future Enhancements

### Phase 2 Features
- **Resource Details**: Click-through to detailed resource information
- **Cost Information**: Integration with Azure cost APIs
- **Resource Health**: Display resource health status
- **Export Functionality**: CSV/JSON export capabilities
- **Bookmarkable URLs**: Support for bookmarked filter states

### Integration Opportunities
- **Dashboard Integration**: Embed in larger dashboard views
- **Alerting**: Resource count threshold alerts
- **Automation**: API endpoints for programmatic access

## Dependencies and Prerequisites

### Azure Permissions
- **Reader Role**: Minimum reader access on target subscriptions
- **Resource Graph**: Access to Azure Resource Graph API
- **Authentication**: Valid Azure credentials (service principal, managed identity, or user)

### Python Dependencies
```
azure-identity>=1.12.0
azure-mgmt-resource>=21.0.0
azure-mgmt-resourcegraph>=8.0.0
```

### Browser Support
- Modern browsers with JavaScript enabled
- Responsive design for mobile/tablet viewing

## Risk Management

### Technical Risks
- **API Limits**: Azure Resource Graph API rate limiting
- **Large Datasets**: Performance with thousands of resources
- **Authentication**: Credential expiration or permission issues

### Mitigation Strategies
- **Retry Logic**: Implement exponential backoff for API calls
- **Pagination**: Proper handling of large result sets
- **Caching**: Consider caching for repeated queries
- **Graceful Degradation**: Partial results if some subscriptions fail

---

## Implementation Checklist

- [ ] Create core module structure
- [ ] Implement Azure Resource Graph integration
- [ ] Build hierarchical data processing
- [ ] Create HTML template with Tailwind CSS
- [ ] Implement interactive JavaScript functionality
- [ ] Add filtering and sorting capabilities
- [ ] Integrate with existing web server infrastructure
- [ ] Write comprehensive tests
- [ ] Add error handling and logging
- [ ] Update command-line interface
- [ ] Documentation and user guide
- [ ] **CRITICAL**: Implement non-destructive filtering to prevent filter reset issues

## Common Implementation Pitfalls - LEARN FROM THESE MISTAKES! ⚠️

### 🚨 MOST COMMON JAVASCRIPT FILTERING MISTAKE

**This mistake is so common it deserves special attention - AVOID THIS PATTERN!**

#### The Intuitive but Wrong Approach:
Many developers naturally think to filter data like this:
```javascript
// ❌ INTUITIVE BUT WRONG - DO NOT COPY THIS CODE:
getFilteredData() {
    const clonedData = JSON.parse(JSON.stringify(this.data));
    return clonedData.filter(subscription => {
        // ❌ This permanently removes items from the data structure!
        subscription.resource_groups = subscription.resource_groups.filter(rg => {
            return this.filters.resourceGroup ? rg.name === this.filters.resourceGroup : true;
        });
        return subscription.resource_groups.length > 0;
    });
}
```

**Why This Seems Logical**: 
- You clone the data to avoid modifying the original
- You filter each level of the hierarchy
- It works perfectly... until the user tries to reset filters

**Why This Breaks**: 
- When filters are applied, items are **permanently deleted** from the working dataset
- When user clicks "Clear Filters", the deleted items are **gone forever**
- Filter reset becomes impossible without page refresh
- Users see stale filtered data instead of complete dataset

#### The Correct Implementation Pattern:
```javascript
// ✅ SAFE PATTERN - USE THIS APPROACH:
getFilteredData() {
    const result = [];
    
    for (const subscription of this.data) {
        // Skip items by continuing loop, don't delete them
        if (this.filters.subscription && subscription.subscription_name !== this.filters.subscription) {
            continue;
        }
        
        // Build completely new objects instead of modifying existing ones
        const filteredSubscription = {
            ...subscription,
            resource_groups: [],
            resource_count: 0
        };
        
        for (const rg of subscription.resource_groups) {
            if (this.filters.resourceGroup && rg.name !== this.filters.resourceGroup) {
                continue;
            }
            
            const filteredResourceGroup = { ...rg, resource_types: [], resource_count: 0 };
            // Continue this pattern for all hierarchy levels
            
            if (filteredResourceGroup.resource_types.length > 0) {
                filteredSubscription.resource_groups.push(filteredResourceGroup);
                filteredSubscription.resource_count += filteredResourceGroup.resource_count;
            }
        }
        
        if (filteredSubscription.resource_groups.length > 0) {
            result.push(filteredSubscription);
        }
    }
    
    return result; // Original this.data remains completely unchanged!
}
```

#### Mandatory Pre-Implementation Checklist:
- [ ] ✅ Understand the destructive filtering anti-pattern
- [ ] ✅ Plan to never modify `this.data` in filtering methods
- [ ] ✅ Design filtering to create fresh objects/arrays
- [ ] ✅ Plan to test filter reset functionality thoroughly
- [ ] ✅ Implement comprehensive `clearAllFilters()` method
- [ ] ✅ Plan dependent filter management (subscription → resource group → resource type)

### Other Implementation Notes:
- **Design Pattern**: Follow the DNS-Private module pattern exactly
- **Testing Strategy**: Test filter reset functionality as a primary requirement
- **User Experience**: Ensure filter operations never require page refresh
- **Performance**: Consider efficiency for large datasets while maintaining data integrity

<!-- Contains AI-generated edits. -->
