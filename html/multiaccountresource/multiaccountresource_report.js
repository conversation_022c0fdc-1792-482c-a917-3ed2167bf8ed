/**
 * Multi-Account Resource Center Report - Dynamic JavaScript Renderer
 */
class MultiAccountResourceReport {
    constructor() {
        this.data = window.multiAccountResourceData;
        this.stats = window.multiAccountResourceStats;
        this.filters = {
            account: '',
            resourceType: '',
            resource: '',
            search: ''
        };
        this.expandedItems = new Set();
        this.init();
    }

    init() {
        this.renderSummaryStats();
        this.populateFilters();
        this.renderHierarchy();
        this.setupEventListeners();
        this.updateFilteredCount();
    }

    renderSummaryStats() {
        const container = document.getElementById('summaryStats');
        const statCards = [
            { icon: '📊', number: this.stats.total_resources.toLocaleString(), label: 'Total Resources', color: 'orange' },
            { icon: '📁', number: this.data.length.toLocaleString(), label: 'Accounts', color: 'orange' },
            { icon: '🏷️', number: this.stats.total_resource_types.toLocaleString(), label: 'Resource Types', color: 'orange' }
        ];
        container.innerHTML = statCards.map(card => `
            <div class="stat-card">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">${card.icon}</div>
                    <div>
                        <div class="stat-number text-${card.color}-400">${card.number}</div>
                        <div class="stat-label">${card.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    populateFilters() {
        const accounts = new Set();
        const resourceTypes = new Set();
        const resources = new Set();
        this.data.forEach(acc => {
            accounts.add(acc.account_name);
            acc.resource_types.forEach(rt => {
                resourceTypes.add(rt.type);
                rt.resources.forEach(res => {
                    resources.add(res.name);
                });
            });
        });
        this.populateSelect('subscriptionFilter', Array.from(accounts).sort());
        this.populateSelect('resourceGroupFilter', Array.from(resourceTypes).sort());
        this.populateSelect('resourceTypeFilter', Array.from(resources).sort());
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        select.querySelectorAll('option:not(:first-child)').forEach(o => o.remove());
        options.forEach(opt => {
            const optionEl = document.createElement('option');
            optionEl.value = opt;
            optionEl.textContent = opt;
            select.appendChild(optionEl);
        });
    }

    setupEventListeners() {
        document.getElementById('subscriptionFilter').addEventListener('change', e => { this.filters.account = e.target.value; this.renderHierarchy(); this.updateFilteredCount(); });
        document.getElementById('resourceGroupFilter').addEventListener('change', e => { this.filters.resourceType = e.target.value; this.renderHierarchy(); this.updateFilteredCount(); });
        document.getElementById('resourceTypeFilter').addEventListener('change', e => { this.filters.resource = e.target.value; this.renderHierarchy(); this.updateFilteredCount(); });
        document.getElementById('resourceSearch').addEventListener('input', e => { this.filters.search = e.target.value.toLowerCase(); this.renderHierarchy(); this.updateFilteredCount(); });
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.filters = { account: '', resourceType: '', resource: '', search: '' };
            document.getElementById('subscriptionFilter').value = '';
            document.getElementById('resourceGroupFilter').value = '';
            document.getElementById('resourceTypeFilter').value = '';
            document.getElementById('resourceSearch').value = '';
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        document.getElementById('expandAll').addEventListener('click', () => { this.expandedItems = new Set(this.getAllItemIds()); this.renderHierarchy(); });
        document.getElementById('collapseAll').addEventListener('click', () => { this.expandedItems.clear(); this.renderHierarchy(); });
    }

    getAllItemIds() {
        const ids = [];
        this.data.forEach(acc => {
            const accId = `acc-${acc.account_id}`;
            ids.push(accId);
            acc.resource_types.forEach(rt => {
                const rtId = `${accId}-rt-${rt.type.replace(/[^a-zA-Z0-9]/g, '_')}`;
                ids.push(rtId);
                rt.resources.forEach(res => {
                    const resId = `${rtId}-res-${res.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
                    ids.push(resId);
                });
            });
        });
        return ids;
    }

    getFilteredData() {
        return this.data
            .filter(acc => (!this.filters.account || acc.account_name === this.filters.account))
            .map(acc => ({
                ...acc,
                resource_types: acc.resource_types
                    .filter(rt => (!this.filters.resourceType || rt.type === this.filters.resourceType))
                    .map(rt => ({
                        ...rt,
                        resources: rt.resources.filter(res => 
                            (!this.filters.resource || res.name === this.filters.resource) &&
                            (!this.filters.search || res.name.toLowerCase().includes(this.filters.search))
                        )
                    }))
                    .filter(rt => rt.resources.length > 0)
            }))
            .filter(acc => acc.resource_types.length > 0);
    }

    renderHierarchy() {
        const container = document.getElementById('resourceHierarchy');
        
        const data = this.getFilteredData();

        // Identify redundant fields across resources (filtered)
        const allResources = data.flatMap(acc => acc.resource_types.flatMap(rt => rt.resources));
        const fieldValues = {};
        allResources.forEach(resItem => {
            Object.entries(resItem)
                .filter(([key]) => !['name', 'type', 'info', 'configuration'].includes(key))
                .forEach(([key, value]) => {
                    fieldValues[key] = fieldValues[key] || new Set();
                    fieldValues[key].add(value);
                });
        });
        this.redundantFields = new Set(
            Object.entries(fieldValues)
                .filter(([, values]) => values.size === 1)
                .map(([key]) => key)
        );
        if (data.length === 0) {
            container.innerHTML = `<div class="text-center py-12 text-gray-500"><div class="text-4xl mb-4">🔍</div><h3 class="text-lg font-medium mb-2 text-gray-700">No resources found</h3><p class="text-gray-500">Try adjusting your filters.</p></div>`;
            return;
        }
        container.innerHTML = data.map(acc => this.renderAccount(acc)).join('');
    }

    renderAccount(acc) {
        const accId = `acc-${acc.account_id}`;
        const isExpanded = this.expandedItems.has(accId);
        // Calculate total resource count for this account
        const totalResourceCount = acc.resource_types.reduce((sum, rt) => sum + rt.resources.length, 0);
        return `
            <div class="mb-4">
                <div class="hierarchy-header account" onclick="multiAccountReport.toggleExpand('${accId}')">
                    <div class="hierarchy-content">
                        <div class="hierarchy-left">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}" id="${accId}-icon">▶</span>
                            <span class="ml-3 text-lg font-semibold">📁 ${acc.account_name} (${totalResourceCount.toLocaleString()})</span>
                        </div>
                    </div>
                </div>
                <div id="${accId}-content" class="ml-4 ${isExpanded ? '' : 'hidden'}">
                    ${acc.resource_types.map(rt => this.renderResourceType(rt, accId)).join('')}
                </div>
            </div>`;
    }

    renderResourceType(rt, accId) {
        const rtId = `${accId}-rt-${rt.type.replace(/[^a-zA-Z0-9]/g, '_')}`;
        const isExpanded = this.expandedItems.has(rtId);
        return `
            <div class="mb-3">
                <div class="hierarchy-header resource-type" onclick="multiAccountReport.toggleExpand('${rtId}')">
                    <div class="hierarchy-content">
                        <div class="hierarchy-left">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}" id="${rtId}-icon">▶</span>
                            <span class="ml-3 font-medium">📊 ${rt.type} (${rt.resources.length.toLocaleString()})</span>
                        </div>
                    </div>
                </div>
                <div id="${rtId}-content" class="ml-4 ${isExpanded ? '' : 'hidden'}">
                    ${rt.resources.map(res => this.renderResource(res)).join('')}
                </div>
            </div>`;
    }

    renderResource(res) {
        const resId = `res-${res.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
        const name = res.name || 'Unknown';
        const type = res.type || '';
        const info = res.info || '';
        const configObj = (() => {
            try { return JSON.parse(info); }
            catch (e) { return {}; }
        })();
        // Build rows for all fields except name, type, info, deduplicating keys case-insensitively
        const rows = [];
        const seen = new Set();
        Object.entries(res)
            .filter(([key]) =>
                !['name', 'type', 'info', 'configuration'].includes(key) &&
                !(this.redundantFields && this.redundantFields.has(key))
            )
            .forEach(([key, value]) => {
                const lowerKey = key.toLowerCase();
                if (!seen.has(lowerKey)) {
                    seen.add(lowerKey);
                    rows.push([key, value]);
                }
            });
        // Append configuration fields with deduplication
        Object.entries(configObj)
            .filter(([key]) => !(this.redundantFields && this.redundantFields.has(key)))
            .forEach(([key, value]) => {
            const lowerKey = key.toLowerCase();
            if (!seen.has(lowerKey)) {
                seen.add(lowerKey);
                rows.push([key, value]);
            }
        });
        const fieldTable = rows.length ? `<table class="min-w-full divide-y divide-gray-200 bg-white rounded-md overflow-hidden">
            <thead class="bg-gray-800 text-white">
                <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium">Field</th>
                    <th class="px-4 py-2 text-left text-xs font-medium">Value</th>
                </tr>
            </thead>
            <tbody>
                ${rows.map(([field, value]) => `
                    <tr class="bg-white">
                        <td class="px-4 py-2 text-sm text-gray-600">${field}</td>
                        <td class="px-4 py-2 text-sm text-gray-800">${value}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>` : '';
        return `
            <div class="resource-item bg-white border border-orange-200 rounded-lg p-3 mb-2 hover:bg-orange-50 transition-colors shadow-sm">
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2">
                            <span class="mr-2 text-orange-500">📦</span>
                            <span class="font-medium text-gray-900">${name}</span>
                        </div>
                        ${fieldTable}
                    </div>
                </div>
            </div>`;
    }

    toggleExpand(id) {
        if (this.expandedItems.has(id)) {
            this.expandedItems.delete(id);
        } else {
            this.expandedItems.add(id);
        }
        this.renderHierarchy();
    }

    updateFilteredCount() {
        const count = this.getFilteredData().reduce((total, acc) =>
            total + acc.resource_types.reduce((sum, rt) => sum + rt.resources.length, 0), 0);
        document.getElementById('filteredCount').textContent = count;
    }
}

let multiAccountReport;
document.addEventListener('DOMContentLoaded', () => {
    multiAccountReport = new MultiAccountResourceReport();
});
