/* Azure Organization Report CSS */
/* Tailwind is included via CDN, this contains additional custom styles */

.organization-tree {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
}

/* Tree node styles */
.tree-node {
    margin: 0;
    padding: 0;
    list-style: none;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
    user-select: none;
}

.tree-node-content:hover {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Add visual feedback for clickable rows */
.tree-node-content:active {
    transform: translateX(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tree-node-content.expanded {
    background-color: #eff6ff;
    border-color: #bfdbfe;
}

.tree-node-content.collapsed {
    background-color: #fff;
}

/* Tree node types */
.tree-node-content.management-group {
    border-left: 4px solid #3b82f6;
}

.tree-node-content.subscription {
    border-left: 4px solid #10b981;
}

.tree-node-content.resource-group {
    border-left: 4px solid #8b5cf6;
}

.tree-node-content.resource {
    border-left: 4px solid #f59e0b;
}

/* Expand/collapse icons */
.tree-toggle {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    border-radius: 4px;
    background-color: #f1f5f9;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tree-toggle:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
}

.tree-toggle svg {
    width: 12px;
    height: 12px;
    transition: transform 0.2s ease;
    color: #64748b;
}

.tree-toggle.expanded svg {
    transform: rotate(90deg);
}

.tree-toggle.no-children {
    opacity: 0;
    cursor: default;
}

/* Node icons */
.tree-node-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tree-node-icon.management-group {
    color: #3b82f6;
}

.tree-node-icon.subscription {
    color: #10b981;
}

.tree-node-icon.resource-group {
    color: #8b5cf6;
}

.tree-node-icon.resource {
    color: #f59e0b;
}

/* Node labels */
.tree-node-label {
    flex: 1;
    font-weight: 500;
    color: #1f2937;
    margin-right: 8px;
}

.tree-node-label.management-group {
    color: #1e40af;
    font-weight: 600;
}

.tree-node-label.subscription {
    color: #065f46;
    font-weight: 500;
}

.tree-node-label.resource-group {
    color: #5b21b6;
}

.tree-node-label.resource {
    color: #92400e;
}

/* Node metadata */
.tree-node-meta {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    flex-shrink: 0;
}

.tree-node-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.tree-node-badge.management-groups {
    background-color: #dbeafe;
    color: #1e40af;
}

.tree-node-badge.subscriptions {
    background-color: #d1fae5;
    color: #065f46;
}

.tree-node-badge.resource-groups {
    background-color: #ede9fe;
    color: #5b21b6;
}

.tree-node-badge.resources {
    background-color: #fef3c7;
    color: #92400e;
}

/* Tree children container */
.tree-children {
    margin-left: 28px;
    border-left: 1px solid #e5e7eb;
    padding-left: 8px;
    margin-top: 4px;
    transition: all 0.3s ease;
}

.tree-children.collapsed {
    max-height: 0;
    overflow: hidden;
    margin-top: 0;
    opacity: 0;
}

.tree-children.expanded {
    max-height: none;
    opacity: 1;
}

/* Search highlighting */
.tree-node-content.search-match {
    background-color: #fef3c7;
    border-color: #f59e0b;
}

.tree-node-content.search-match .tree-node-label {
    color: #92400e;
    font-weight: 600;
}

.tree-node-content.search-parent {
    background-color: #fef7ed;
    border-color: #fed7aa;
}

/* Loading states */
.tree-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6b7280;
}

.tree-loading svg {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Empty state */
.tree-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
    color: #6b7280;
}

.tree-empty svg {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tree-node-meta {
        flex-direction: column;
        gap: 4px;
    }
    
    .tree-node-badge {
        font-size: 10px;
        padding: 1px 4px;
    }
    
    .tree-children {
        margin-left: 20px;
    }
}

/* Print styles */
@media print {
    .tree-toggle {
        display: none;
    }
    
    .tree-children.collapsed {
        max-height: none;
        opacity: 1;
        overflow: visible;
    }
    
    .tree-node-content {
        break-inside: avoid;
    }
}

/* Filter controls styling */
.filter-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    color: white;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.filter-controls select {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px 12px;
    color: #374151;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-controls select:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.filter-controls label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
}

.filter-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-actions button {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-actions button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.filtered-count {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    margin-left: auto;
}

/* Contains AI-generated edits. */
