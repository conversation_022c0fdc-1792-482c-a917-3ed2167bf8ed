/**
 * Azure Organization Report JavaScript
 * Interactive tree view functionality matching the resource overview style
 */

class OrganizationReport {
    constructor() {
        // Try to access the global variables directly
        this.data = window.organizationData || organizationData || [];
        this.stats = window.organizationStats || organizationStats || {};
        this.searchTerm = '';
        this.expandedNodes = new Set();
        this.filters = {
            managementGroup: '',
            subscription: '',
            resourceGroup: '',
            resourceType: ''
        };
        
        // Expand root nodes and first level by default for better UX
        this.expandDefaultNodes();
        
        this.init();
    }

    expandDefaultNodes() {
        if (!this.data || this.data.length === 0) return;
        
        // Expand all root management groups
        this.data.forEach(rootNode => {
            if (rootNode.id) {
                this.expandedNodes.add(`node-${rootNode.id}`);
                
                // Also expand first level child management groups
                if (rootNode.children && rootNode.children.length > 0) {
                    rootNode.children.forEach(child => {
                        if (child.id) {
                            this.expandedNodes.add(`node-${child.id}`);
                        }
                    });
                }
                
                // Expand first few subscriptions for visibility
                if (rootNode.subscriptions && rootNode.subscriptions.length > 0) {
                    rootNode.subscriptions.slice(0, 3).forEach(sub => {
                        if (sub.id) {
                            this.expandedNodes.add(`sub-${sub.id}`);
                        }
                    });
                }
            }
        });
    }

    init() {
        this.populateFilters();
        this.bindEvents();
        this.renderTree();
        this.updateStats();
        this.updateFilteredCount();
    }

    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.renderTree();
            });
        }

        // Filter change listeners
        const mgFilter = document.getElementById('managementGroupFilter');
        if (mgFilter) {
            mgFilter.addEventListener('change', (e) => {
                this.applyFilter('managementGroup', e.target.value);
            });
        }

        const subFilter = document.getElementById('subscriptionFilter');
        if (subFilter) {
            subFilter.addEventListener('change', (e) => {
                this.applyFilter('subscription', e.target.value);
            });
        }

        const rgFilter = document.getElementById('resourceGroupFilter');
        if (rgFilter) {
            rgFilter.addEventListener('change', (e) => {
                this.applyFilter('resourceGroup', e.target.value);
            });
        }

        const rtFilter = document.getElementById('resourceTypeFilter');
        if (rtFilter) {
            rtFilter.addEventListener('change', (e) => {
                this.applyFilter('resourceType', e.target.value);
            });
        }

        // Clear filters
        const clearFiltersBtn = document.getElementById('clearFilters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }

        // Control buttons
        const expandAllBtn = document.getElementById('expand-all-btn');
        if (expandAllBtn) {
            expandAllBtn.addEventListener('click', () => this.expandAll());
        }

        const collapseAllBtn = document.getElementById('collapse-all-btn');
        if (collapseAllBtn) {
            collapseAllBtn.addEventListener('click', () => this.collapseAll());
        }

        const resetViewBtn = document.getElementById('reset-view-btn');
        if (resetViewBtn) {
            resetViewBtn.addEventListener('click', () => this.resetView());
        }
    }

    renderTree() {
        const container = document.getElementById('organization-tree');
        if (!container) return;

        const filteredData = this.getFilteredData();

        if (!filteredData || filteredData.length === 0) {
            container.innerHTML = this.renderEmptyState();
            return;
        }

        const treeHtml = filteredData.map(node => this.renderNode(node, 0)).join('');
        container.innerHTML = `<div class="tree-nodes">${treeHtml}</div>`;

        // Bind click events for tree nodes
        this.bindTreeEvents();
    }

    renderNode(node, level) {
        if (!node) return '';

        const nodeId = `node-${node.id || Math.random().toString(36).substr(2, 9)}`;
        const isExpanded = this.expandedNodes.has(nodeId);
        const hasChildren = this.hasChildren(node);
        const isSearchMatch = this.isSearchMatch(node);
        const isSearchParent = this.isSearchParent(node);

        // Skip non-matching nodes during search (unless they have matching children)
        if (this.searchTerm && !isSearchMatch && !isSearchParent) {
            return '';
        }

        const nodeClass = this.getNodeClass(node, isExpanded, isSearchMatch, isSearchParent);
        const icon = this.getNodeIcon(node.type);
        const toggleIcon = hasChildren ? this.getToggleIcon(isExpanded) : '';
        const badges = this.getNodeBadges(node);

        let html = `
            <div class="tree-node" data-node-id="${nodeId}" data-level="${level}">
                <div class="${nodeClass}" data-type="${node.type}">
                    ${toggleIcon}
                    <div class="tree-node-icon ${node.type}">
                        ${icon}
                    </div>
                    <div class="tree-node-label ${node.type}">
                        ${this.highlightSearchTerm(node.name || 'Unknown')}
                    </div>
                    <div class="tree-node-meta">
                        ${badges}
                    </div>
                </div>
        `;

        // Add children if they exist and node is expanded (or if it's a top-level node)
        if (hasChildren) {
            const childrenClass = (isExpanded || level === 0) ? 'tree-children expanded' : 'tree-children collapsed';
            html += `<div class="${childrenClass}">`;
            
            // Add child management groups
            if (node.children && node.children.length > 0) {
                html += node.children.map(child => this.renderNode(child, level + 1)).join('');
            }
            
            // Add subscriptions
            if (node.subscriptions && node.subscriptions.length > 0) {
                html += node.subscriptions.map(sub => this.renderSubscription(sub, level + 1)).join('');
            }
            
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    renderSubscription(subscription, level) {
        const nodeId = `sub-${subscription.id || Math.random().toString(36).substr(2, 9)}`;
        const isExpanded = this.expandedNodes.has(nodeId);
        const hasChildren = subscription.resource_groups && subscription.resource_groups.length > 0;
        const isSearchMatch = this.isSearchMatch(subscription);
        const isSearchParent = this.isSearchParent(subscription);

        // Skip non-matching nodes during search
        if (this.searchTerm && !isSearchMatch && !isSearchParent) {
            return '';
        }

        const nodeClass = this.getNodeClass(subscription, isExpanded, isSearchMatch, isSearchParent);
        const icon = this.getNodeIcon('subscription');
        const toggleIcon = hasChildren ? this.getToggleIcon(isExpanded) : '';
        const badges = this.getSubscriptionBadges(subscription);

        let html = `
            <div class="tree-node" data-node-id="${nodeId}" data-level="${level}">
                <div class="${nodeClass}" data-type="subscription">
                    ${toggleIcon}
                    <div class="tree-node-icon subscription">
                        ${icon}
                    </div>
                    <div class="tree-node-label subscription">
                        ${this.highlightSearchTerm(subscription.name || 'Unknown Subscription')}
                    </div>
                    <div class="tree-node-meta">
                        ${badges}
                    </div>
                </div>
        `;

        // Add resource groups if they exist and subscription is expanded (or if it's level 1-2)
        if (hasChildren) {
            const childrenClass = (isExpanded || level <= 2) ? 'tree-children expanded' : 'tree-children collapsed';
            html += `<div class="${childrenClass}">`;
            html += subscription.resource_groups.map(rg => this.renderResourceGroup(rg, level + 1)).join('');
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    renderResourceGroup(resourceGroup, level) {
        const nodeId = `rg-${resourceGroup.id || resourceGroup.name || Math.random().toString(36).substr(2, 9)}`;
        const isExpanded = this.expandedNodes.has(nodeId);
        const hasChildren = resourceGroup.resources && resourceGroup.resources.length > 0;
        const isSearchMatch = this.isSearchMatch(resourceGroup);
        const isSearchParent = this.isSearchParent(resourceGroup);

        // Skip non-matching nodes during search
        if (this.searchTerm && !isSearchMatch && !isSearchParent) {
            return '';
        }

        const nodeClass = this.getNodeClass(resourceGroup, isExpanded, isSearchMatch, isSearchParent);
        const icon = this.getNodeIcon('resource_group');
        const toggleIcon = hasChildren ? this.getToggleIcon(isExpanded) : '';
        const badges = this.getResourceGroupBadges(resourceGroup);

        let html = `
            <div class="tree-node" data-node-id="${nodeId}" data-level="${level}">
                <div class="${nodeClass}" data-type="resource-group">
                    ${toggleIcon}
                    <div class="tree-node-icon resource-group">
                        ${icon}
                    </div>
                    <div class="tree-node-label resource-group">
                        ${this.highlightSearchTerm(resourceGroup.name || 'Unknown Resource Group')}
                    </div>
                    <div class="tree-node-meta">
                        ${badges}
                    </div>
                </div>
        `;

        // Add resources if they exist and resource group is expanded
        if (hasChildren) {
            const childrenClass = isExpanded ? 'tree-children expanded' : 'tree-children collapsed';
            html += `<div class="${childrenClass}">`;
            html += resourceGroup.resources.map(resource => this.renderResource(resource, level + 1)).join('');
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    renderResource(resource, level) {
        const isSearchMatch = this.isSearchMatch(resource);

        // Skip non-matching resources during search
        if (this.searchTerm && !isSearchMatch) {
            return '';
        }

        const nodeClass = this.getResourceClass(isSearchMatch);
        const icon = this.getNodeIcon('resource');

        return `
            <div class="tree-node" data-level="${level}">
                <div class="${nodeClass}" data-type="resource">
                    <div class="tree-toggle no-children"></div>
                    <div class="tree-node-icon resource">
                        ${icon}
                    </div>
                    <div class="tree-node-label resource">
                        ${this.highlightSearchTerm(resource.name || 'Unknown Resource')}
                    </div>
                    <div class="tree-node-meta">
                        <span class="tree-node-badge resources">${resource.type || 'Unknown Type'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    getNodeClass(node, isExpanded, isSearchMatch, isSearchParent) {
        let classes = ['tree-node-content'];
        
        if (node.type) {
            classes.push(node.type.replace('_', '-'));
        }
        
        if (isExpanded) {
            classes.push('expanded');
        } else {
            classes.push('collapsed');
        }
        
        if (isSearchMatch) {
            classes.push('search-match');
        } else if (isSearchParent) {
            classes.push('search-parent');
        }
        
        return classes.join(' ');
    }

    getResourceClass(isSearchMatch) {
        let classes = ['tree-node-content', 'resource'];
        
        if (isSearchMatch) {
            classes.push('search-match');
        }
        
        return classes.join(' ');
    }

    getNodeIcon(type) {
        switch (type) {
            case 'management_group':
                return `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>`;
            case 'subscription':
                return `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>`;
            case 'resource_group':
                return `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>`;
            case 'resource':
                return `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>`;
            default:
                return `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>`;
        }
    }

    getToggleIcon(isExpanded) {
        return `
            <div class="tree-toggle ${isExpanded ? 'expanded' : ''}">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        `;
    }

    getNodeBadges(node) {
        const badges = [];
        
        if (node.management_group_count > 0) {
            badges.push(`<span class="tree-node-badge management-groups">${node.management_group_count} MG${node.management_group_count !== 1 ? 's' : ''}</span>`);
        }
        
        if (node.subscription_count > 0) {
            badges.push(`<span class="tree-node-badge subscriptions">${node.subscription_count} Sub${node.subscription_count !== 1 ? 's' : ''}</span>`);
        }
        
        if (node.resource_group_count > 0) {
            badges.push(`<span class="tree-node-badge resource-groups">${node.resource_group_count} RG${node.resource_group_count !== 1 ? 's' : ''}</span>`);
        }
        
        if (node.resource_count > 0) {
            badges.push(`<span class="tree-node-badge resources">${node.resource_count} Resource${node.resource_count !== 1 ? 's' : ''}</span>`);
        }
        
        return badges.join('');
    }

    getSubscriptionBadges(subscription) {
        const badges = [];
        
        if (subscription.resource_group_count > 0) {
            badges.push(`<span class="tree-node-badge resource-groups">${subscription.resource_group_count} RG${subscription.resource_group_count !== 1 ? 's' : ''}</span>`);
        }
        
        if (subscription.resource_count > 0) {
            badges.push(`<span class="tree-node-badge resources">${subscription.resource_count} Resource${subscription.resource_count !== 1 ? 's' : ''}</span>`);
        }
        
        return badges.join('');
    }

    getResourceGroupBadges(resourceGroup) {
        const badges = [];
        
        if (resourceGroup.resource_count > 0) {
            badges.push(`<span class="tree-node-badge resources">${resourceGroup.resource_count} Resource${resourceGroup.resource_count !== 1 ? 's' : ''}</span>`);
        }
        
        return badges.join('');
    }

    hasChildren(node) {
        return (node.children && node.children.length > 0) || 
               (node.subscriptions && node.subscriptions.length > 0) ||
               (node.resource_groups && node.resource_groups.length > 0) ||
               (node.resources && node.resources.length > 0);
    }

    isSearchMatch(node) {
        if (!this.searchTerm) return false;
        const name = (node.name || '').toLowerCase();
        const id = (node.id || '').toLowerCase();
        return name.includes(this.searchTerm) || id.includes(this.searchTerm);
    }

    isSearchParent(node) {
        if (!this.searchTerm) return false;
        return this.hasMatchingChildren(node);
    }

    hasMatchingChildren(node) {
        // Check direct children (management groups)
        if (node.children) {
            for (const child of node.children) {
                if (this.isSearchMatch(child) || this.hasMatchingChildren(child)) {
                    return true;
                }
            }
        }
        
        // Check subscriptions
        if (node.subscriptions) {
            for (const sub of node.subscriptions) {
                if (this.isSearchMatch(sub) || this.hasMatchingChildren(sub)) {
                    return true;
                }
            }
        }
        
        // Check resource groups
        if (node.resource_groups) {
            for (const rg of node.resource_groups) {
                if (this.isSearchMatch(rg) || this.hasMatchingChildren(rg)) {
                    return true;
                }
            }
        }
        
        // Check resources
        if (node.resources) {
            for (const resource of node.resources) {
                if (this.isSearchMatch(resource)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    highlightSearchTerm(text) {
        if (!this.searchTerm || !text) return text;
        
        const regex = new RegExp(`(${this.searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
    }

    bindTreeEvents() {
        const container = document.getElementById('organization-tree');
        if (!container) return;

        container.addEventListener('click', (e) => {
            // Check if clicked on toggle button
            const toggle = e.target.closest('.tree-toggle');
            if (toggle && !toggle.classList.contains('no-children')) {
                this.toggleNode(toggle);
                return;
            }

            // Check if clicked on node content (for row-level toggle)
            const nodeContent = e.target.closest('.tree-node-content');
            if (nodeContent) {
                const treeNode = nodeContent.closest('.tree-node');
                if (treeNode) {
                    const nodeToggle = treeNode.querySelector('.tree-toggle');
                    if (nodeToggle && !nodeToggle.classList.contains('no-children')) {
                        this.toggleNode(nodeToggle);
                    }
                }
            }
        });
    }

    toggleNode(toggle) {
        const treeNode = toggle.closest('.tree-node');
        if (!treeNode) return;

        const nodeId = treeNode.dataset.nodeId;
        const childrenContainer = treeNode.querySelector('.tree-children');
        
        if (!childrenContainer) return;

        if (this.expandedNodes.has(nodeId)) {
            this.expandedNodes.delete(nodeId);
            childrenContainer.classList.remove('expanded');
            childrenContainer.classList.add('collapsed');
            toggle.classList.remove('expanded');
        } else {
            this.expandedNodes.add(nodeId);
            childrenContainer.classList.remove('collapsed');
            childrenContainer.classList.add('expanded');
            toggle.classList.add('expanded');
        }
    }

    expandAll() {
        const allNodes = document.querySelectorAll('[data-node-id]');
        allNodes.forEach(node => {
            const nodeId = node.dataset.nodeId;
            const childrenContainer = node.querySelector('.tree-children');
            const toggle = node.querySelector('.tree-toggle');
            
            if (childrenContainer && toggle && !toggle.classList.contains('no-children')) {
                this.expandedNodes.add(nodeId);
                childrenContainer.classList.remove('collapsed');
                childrenContainer.classList.add('expanded');
                toggle.classList.add('expanded');
            }
        });
    }

    collapseAll() {
        this.expandedNodes.clear();
        const allChildrenContainers = document.querySelectorAll('.tree-children');
        const allToggles = document.querySelectorAll('.tree-toggle:not(.no-children)');
        
        allChildrenContainers.forEach(container => {
            container.classList.remove('expanded');
            container.classList.add('collapsed');
        });
        
        allToggles.forEach(toggle => {
            toggle.classList.remove('expanded');
        });
    }

    resetView() {
        this.searchTerm = '';
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
        }
        this.clearAllFilters();
        this.collapseAll();
        this.renderTree();
    }

    updateStats() {
        // Update stat cards with actual values
        const elements = {
            'stat-management-groups': this.stats.total_management_groups || 0,
            'stat-subscriptions': this.stats.total_subscriptions || 0,
            'stat-resource-groups': this.stats.total_resource_groups || 0,
            'stat-resources': this.stats.total_resources || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value.toLocaleString();
            }
        });
    }

    populateFilters() {
        const managementGroups = new Set();
        const subscriptions = new Set();
        const resourceGroups = new Set();
        const resourceTypes = new Set();
        
        this.collectFilterOptions(this.data, managementGroups, subscriptions, resourceGroups, resourceTypes);
        
        this.populateSelect('managementGroupFilter', Array.from(managementGroups).sort());
        this.populateSelect('subscriptionFilter', Array.from(subscriptions).sort());
        this.populateSelect('resourceGroupFilter', Array.from(resourceGroups).sort());
        this.populateSelect('resourceTypeFilter', Array.from(resourceTypes).sort());
    }

    collectFilterOptions(nodes, managementGroups, subscriptions, resourceGroups, resourceTypes) {
        nodes.forEach(node => {
            if (node.type === 'management_group' && node.name) {
                managementGroups.add(node.name);
            }
            
            // Collect from child management groups
            if (node.children && node.children.length > 0) {
                this.collectFilterOptions(node.children, managementGroups, subscriptions, resourceGroups, resourceTypes);
            }
            
            // Collect from subscriptions
            if (node.subscriptions && node.subscriptions.length > 0) {
                node.subscriptions.forEach(sub => {
                    if (sub.name) {
                        subscriptions.add(sub.name);
                    }
                    
                    // Collect from resource groups
                    if (sub.resource_groups && sub.resource_groups.length > 0) {
                        sub.resource_groups.forEach(rg => {
                            if (rg.name) {
                                resourceGroups.add(rg.name);
                            }
                            
                            // Collect from resources
                            if (rg.resources && rg.resources.length > 0) {
                                rg.resources.forEach(resource => {
                                    if (resource.type) {
                                        resourceTypes.add(resource.type);
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        if (!select) return;
        
        const currentOptions = Array.from(select.querySelectorAll('option:not(:first-child)'));
        currentOptions.forEach(option => option.remove());
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });
    }

    applyFilter(filterType, value) {
        this.filters[filterType] = value;
        
        // When a parent filter is applied, reset dependent filters
        if (filterType === 'managementGroup') {
            this.filters.subscription = '';
            this.filters.resourceGroup = '';
            this.filters.resourceType = '';
            this.resetSelectValue('subscriptionFilter');
            this.resetSelectValue('resourceGroupFilter');
            this.resetSelectValue('resourceTypeFilter');
        } else if (filterType === 'subscription') {
            this.filters.resourceGroup = '';
            this.filters.resourceType = '';
            this.resetSelectValue('resourceGroupFilter');
            this.resetSelectValue('resourceTypeFilter');
        } else if (filterType === 'resourceGroup') {
            this.filters.resourceType = '';
            this.resetSelectValue('resourceTypeFilter');
        }
        
        this.renderTree();
        this.updateFilteredCount();
    }

    resetSelectValue(selectId) {
        const select = document.getElementById(selectId);
        if (select) {
            select.value = '';
        }
    }

    clearAllFilters() {
        this.filters = {
            managementGroup: '',
            subscription: '',
            resourceGroup: '',
            resourceType: ''
        };
        
        this.resetSelectValue('managementGroupFilter');
        this.resetSelectValue('subscriptionFilter');
        this.resetSelectValue('resourceGroupFilter');
        this.resetSelectValue('resourceTypeFilter');
        
        this.populateFilters();
        this.renderTree();
        this.updateFilteredCount();
    }

    getFilteredData() {
        if (!this.data) return [];
        
        return this.data.map(node => this.filterNode(node)).filter(Boolean);
    }

    filterNode(node) {
        if (!node) return null;
        
        // Check if this management group matches filter
        if (this.filters.managementGroup && node.name !== this.filters.managementGroup) {
            return null;
        }
        
        const filteredNode = { ...node };
        
        // Filter children (management groups)
        if (node.children && node.children.length > 0) {
            filteredNode.children = node.children.map(child => this.filterNode(child)).filter(Boolean);
        }
        
        // Filter subscriptions
        if (node.subscriptions && node.subscriptions.length > 0) {
            filteredNode.subscriptions = node.subscriptions.map(sub => this.filterSubscription(sub)).filter(Boolean);
        }
        
        // Show node if it has any matching children/subscriptions or matches search
        const hasMatchingChildren = (filteredNode.children && filteredNode.children.length > 0) ||
                                   (filteredNode.subscriptions && filteredNode.subscriptions.length > 0);
        const isSearchMatch = this.isSearchMatch(node);
        
        if (hasMatchingChildren || isSearchMatch || !this.searchTerm) {
            return filteredNode;
        }
        
        return null;
    }

    filterSubscription(subscription) {
        if (!subscription) return null;
        
        // Check if this subscription matches filter
        if (this.filters.subscription && subscription.name !== this.filters.subscription) {
            return null;
        }
        
        const filteredSub = { ...subscription };
        
        // Filter resource groups
        if (subscription.resource_groups && subscription.resource_groups.length > 0) {
            filteredSub.resource_groups = subscription.resource_groups.map(rg => this.filterResourceGroup(rg)).filter(Boolean);
        }
        
        // Show subscription if it has matching resource groups or matches search
        const hasMatchingChildren = filteredSub.resource_groups && filteredSub.resource_groups.length > 0;
        const isSearchMatch = this.isSearchMatch(subscription);
        
        if (hasMatchingChildren || isSearchMatch || (!this.filters.resourceGroup && !this.filters.resourceType && !this.searchTerm)) {
            return filteredSub;
        }
        
        return null;
    }

    filterResourceGroup(resourceGroup) {
        if (!resourceGroup) return null;
        
        // Check if this resource group matches filter
        if (this.filters.resourceGroup && resourceGroup.name !== this.filters.resourceGroup) {
            return null;
        }
        
        const filteredRG = { ...resourceGroup };
        
        // Filter resources
        if (resourceGroup.resources && resourceGroup.resources.length > 0) {
            filteredRG.resources = resourceGroup.resources.filter(resource => {
                if (this.filters.resourceType && resource.type !== this.filters.resourceType) {
                    return false;
                }
                return !this.searchTerm || this.isSearchMatch(resource);
            });
        }
        
        // Show resource group if it has matching resources or matches search
        const hasMatchingChildren = filteredRG.resources && filteredRG.resources.length > 0;
        const isSearchMatch = this.isSearchMatch(resourceGroup);
        
        if (hasMatchingChildren || isSearchMatch || (!this.filters.resourceType && !this.searchTerm)) {
            return filteredRG;
        }
        
        return null;
    }

    updateFilteredCount() {
        const filteredCountElement = document.getElementById('filteredCount');
        if (!filteredCountElement) return;
        
        const filteredData = this.getFilteredData();
        let totalCount = 0;
        
        filteredData.forEach(node => {
            totalCount += this.countNodesRecursively(node);
        });
        
        filteredCountElement.textContent = totalCount;
    }

    countNodesRecursively(node) {
        let count = 1; // Count this node
        
        if (node.children) {
            count += node.children.reduce((sum, child) => sum + this.countNodesRecursively(child), 0);
        }
        
        if (node.subscriptions) {
            node.subscriptions.forEach(sub => {
                count += 1; // Count subscription
                if (sub.resource_groups) {
                    sub.resource_groups.forEach(rg => {
                        count += 1; // Count resource group
                        if (rg.resources) {
                            count += rg.resources.length; // Count resources
                        }
                    });
                }
            });
        }
        
        return count;
    }

    renderEmptyState() {
        return `
            <div class="tree-empty">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">No Organization Data</h3>
                <p class="text-gray-500">No management groups or subscriptions found.</p>
            </div>
        `;
    }
}

// Initialize the report when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new OrganizationReport();
});

// Contains AI-generated edits.
