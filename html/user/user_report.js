/**
 * User Report - Dynamic JavaScript Renderer
 * This script handles the dynamic rendering and interaction for User reports
 */

let sortDirection = {}; // Track sort direction for each column

// Table sorting functionality
function sortTable(columnIndex) {
    const table = document.getElementById('userTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));
    const headers = table.getElementsByTagName('th');
    
    // Remove sort classes from all headers
    for (let header of headers) {
        if (header.cellIndex !== columnIndex) {
            header.classList.remove('sort-asc', 'sort-desc');
        }
    }
    
    // Determine sort direction
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;
    
    // Add sort class to current header
    headers[columnIndex].classList.remove('sort-asc', 'sort-desc');
    headers[columnIndex].classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');
    
    // Sort rows
    rows.sort((a, b) => {
        let aText = '';
        let bText = '';
        
        if (columnIndex === 0) {
            // For the # column, use numeric sorting
            aText = parseInt(a.cells[columnIndex].textContent) || 0;
            bText = parseInt(b.cells[columnIndex].textContent) || 0;
            
            if (newDirection === 'asc') {
                return aText - bText;
            } else {
                return bText - aText;
            }
        } else {
            // For other columns, use text content
            aText = a.cells[columnIndex].textContent.trim().toLowerCase();
            bText = b.cells[columnIndex].textContent.trim().toLowerCase();
            
            if (newDirection === 'asc') {
                return aText.localeCompare(bText);
            } else {
                return bText.localeCompare(aText);
            }
        }
    });
    
    // Re-append sorted rows to tbody
    rows.forEach(row => tbody.appendChild(row));
}

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('userTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });
    }
});

// Filter functionality with Tailwind CSS classes
function filterTable(filter) {
    const table = document.getElementById('userTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    const buttons = document.querySelectorAll('.filter-btn');
    
    // Update button styles using Tailwind classes
    buttons.forEach(btn => {
        btn.classList.remove('bg-azure-blue', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-700');
    });
    
    // Highlight active button
    const activeButton = document.querySelector(`[onclick="filterTable('${filter}')"]`);
    if (activeButton) {
        activeButton.classList.remove('bg-gray-200', 'text-gray-700');
        activeButton.classList.add('bg-azure-blue', 'text-white');
    }
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const entraCell = row.cells[2];
        const rbacCell = row.cells[3];
        
        const hasEntra = !entraCell.textContent.includes('No Entra ID roles');
        const hasRbac = !rbacCell.textContent.includes('No RBAC roles');
        
        let shouldShow = true;
        
        switch (filter) {
            case 'entra':
                shouldShow = hasEntra;
                break;
            case 'rbac':
                shouldShow = hasRbac;
                break;
            case 'no-roles':
                shouldShow = !hasEntra && !hasRbac;
                break;
            case 'all':
            default:
                shouldShow = true;
                break;
        }
        
        row.style.display = shouldShow ? '' : 'none';
    }
}

// Toggle expand/collapse functionality for RBAC roles with Tailwind CSS
function toggleExpand(element) {
    const expandableScopes = element.nextElementSibling;
    const arrow = element.querySelector('.expand-arrow');
    const text = element.querySelector('.expand-text');

    // Check if the element is currently collapsed (maxHeight is "0px" or not set)
    if (!expandableScopes.style.maxHeight || expandableScopes.style.maxHeight === "0px") {
        // Expand
        expandableScopes.style.maxHeight = expandableScopes.scrollHeight + "px";
        arrow.classList.add('rotate-90');
        text.textContent = 'Show less';
    } else {
        // Collapse
        expandableScopes.style.maxHeight = "0px";
        arrow.classList.remove('rotate-90');
        text.textContent = 'Show all';
    }
}

/* Contains AI-generated edits. */
