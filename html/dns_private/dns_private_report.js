/**
 * DNS Private Resources Report - Dynamic JavaScript Renderer
 * This script handles the dynamic rendering of DNS private resources data
 */

class DNSPrivateReport {
    constructor() {
        this.data = null;
        this.currentPage = this.getPageFromHash() || 'overview';
        this.sortDirections = {}; // Store sort directions per table
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
        
        // Listen for hash changes to update page when browser back/forward buttons are used
        window.addEventListener('hashchange', () => this.handleHashChange());
    }
    
    // Get the current page from URL hash
    getPageFromHash() {
        const hash = window.location.hash.substring(1); // Remove the # character
        const validPages = ['overview', 'zones', 'vnet_links', 'endpoints', 'resolvers'];
        return validPages.includes(hash) ? hash : null;
    }
    
    // Handle hash changes (back/forward browser navigation)
    handleHashChange() {
        const newPage = this.getPageFromHash() || 'overview';
        if (newPage !== this.currentPage) {
            // Use showPage to update both the current page and navigation state
            this.showPage(newPage);
        }
    }

    setup() {
        // Get data from global variable set by HTML
        this.data = window.dnsPrivateData || [];
        
        console.log('DNS Private Report - Setup called');
        console.log('Available data:', this.data);
        console.log('Data type:', typeof this.data);
        console.log('Is array:', Array.isArray(this.data));
        
        if (!this.data || this.data.length === 0) {
            console.warn('No DNS private data available');
            // Show a message to the user
            const content = document.getElementById('content');
            if (content) {
                content.innerHTML = `
                    <div style="padding: 40px; text-align: center; color: #666;">
                        <h2>⚠️ No Data Available</h2>
                        <p>No DNS private resources data was found. This could mean:</p>
                        <ul style="text-align: left; display: inline-block; margin-top: 20px;">
                            <li>No DNS private resources exist in your Azure subscriptions</li>
                            <li>The data collection process encountered an error</li>
                            <li>There was an issue loading the report data</li>
                        </ul>
                        <p style="margin-top: 20px;">Check the browser console for more details.</p>
                        <p style="margin-top: 10px; font-size: 0.9em; color: #888;">
                            Debug info: Data type: ${typeof this.data}, Length: ${this.data ? this.data.length : 'N/A'}
                        </p>
                    </div>
                `;
            }
            return;
        }
        
        this.setupNavigation();
        
        // Show the current page based on hash or default to overview
        this.showPage(this.currentPage);
    }

    setupNavigation() {
        const navContainer = document.getElementById('nav-container');
        if (!navContainer) return;

        // Calculate resource counts for navigation
        const stats = this.calculateStatistics();
        
        const navItems = [
            { id: 'overview', label: '📊 Overview', count: null },
            { id: 'zones', label: '🌐 Private DNS Zones', count: stats.resource_counts.zones },
            { id: 'vnet_links', label: '🔗 VNet Links', count: stats.resource_counts.vnet_links },
            { id: 'endpoints', label: '🔌 Private Endpoints', count: stats.resource_counts.endpoints },
            { id: 'resolvers', label: '🔍 Resolvers & Rules', count: stats.resource_counts.resolvers + stats.resource_counts.forwarding_rules + stats.resource_counts.forwarding_rules_detail }
        ];

        const navHTML = navItems.map(item => {
            const countBadge = item.count !== null ? `<span class="count-badge">${item.count}</span>` : '';
            return `<div class="nav-link ${item.id === 'overview' ? 'active' : ''}" 
                          onclick="dnsReport.showPage('${item.id}', event)">${item.label}${countBadge}</div>`;
        }).join('');

        navContainer.innerHTML = navHTML;
    }

    showPage(pageId, sourceEvent = null) {
        this.currentPage = pageId;
        
        // Update URL hash without triggering a page reload
        window.location.hash = pageId;
        
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // If we have a source event with a target, use that
        if (sourceEvent && sourceEvent.target) {
            sourceEvent.target.classList.add('active');
        } else {
            // When loading from hash directly or programmatically, find and activate the correct tab
            const activeTab = document.querySelector(`.nav-link[onclick*="${pageId}"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }

        this.updatePageContent();
    }
    
    updatePageContent() {
        // Render appropriate page based on current page
        switch(this.currentPage) {
            case 'overview':
                this.renderOverview();
                break;
            case 'zones':
                this.renderResourcePage('zones', 'Private DNS Zone');
                break;
            case 'vnet_links':
                this.renderResourcePage('vnet_links', 'VNet Link');
                break;
            case 'endpoints':
                this.renderResourcePage('endpoints', 'Private Endpoint');
                break;
            case 'resolvers':
                this.renderResolversPage();
                break;
        }
    }

    calculateStatistics() {
        const stats = {
            total_resources: 0,
            subscriptions: this.data.length,
            locations: new Set(),
            resource_counts: {
                zones: 0,
                vnet_links: 0,
                endpoints: 0,
                resolvers: 0,
                forwarding_rules: 0,
                forwarding_rules_detail: 0
            }
        };

        // Debug logging to see what data we have
        console.log('Raw data structure:', this.data);

        this.data.forEach(sub => {
            console.log(`Processing subscription: ${sub.subscription_name}`);
            Object.keys(stats.resource_counts).forEach(type => {
                const resources = sub[type] || [];
                console.log(`  ${type}: ${resources.length} items`);
                stats.resource_counts[type] += resources.length;
                stats.total_resources += resources.length;

                resources.forEach(res => {
                    if (res.location) {
                        stats.locations.add(res.location);
                    }
                });
            });
        });

        stats.locations = stats.locations.size;
        console.log('Calculated statistics:', stats);
        return stats;
    }

    renderOverview() {
        const stats = this.calculateStatistics();
        const content = document.getElementById('content');
        
        content.innerHTML = `
            <div class="overview-section">
                <h2 class="section-title">📊 Summary Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card highlight">
                        <div class="stat-number">${stats.total_resources}</div>
                        <div class="stat-label">Total Resources</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.subscriptions}</div>
                        <div class="stat-label">Subscriptions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.locations}</div>
                        <div class="stat-label">Locations</div>
                    </div>
                </div>
            </div>

            <div class="overview-section">
                <h2 class="section-title">🌐 Resource Type Breakdown</h2>
                <div class="resource-counts-grid">
                    <div class="resource-count-card">
                        <div class="resource-icon">🌐</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.zones}</div>
                            <div class="resource-label">Private DNS Zones</div>
                        </div>
                    </div>
                    <div class="resource-count-card">
                        <div class="resource-icon">🔗</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.vnet_links}</div>
                            <div class="resource-label">VNet Links</div>
                        </div>
                    </div>
                    <div class="resource-count-card">
                        <div class="resource-icon">🔌</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.endpoints}</div>
                            <div class="resource-label">Private Endpoints</div>
                        </div>
                    </div>
                    <div class="resource-count-card">
                        <div class="resource-icon">🔍</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.resolvers}</div>
                            <div class="resource-label">Private Resolvers</div>
                        </div>
                    </div>
                    <div class="resource-count-card">
                        <div class="resource-icon">📋</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.forwarding_rules}</div>
                            <div class="resource-label">Forwarding Rulesets</div>
                        </div>
                    </div>
                    <div class="resource-count-card">
                        <div class="resource-icon">📝</div>
                        <div class="resource-info">
                            <div class="resource-number">${stats.resource_counts.forwarding_rules_detail}</div>
                            <div class="resource-label">Forwarding Rules</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overview-section">
                <h2 class="section-title">📈 Resource Distribution</h2>
                <div class="distribution-info">
                    <p>Your Azure environment contains <strong>${stats.total_resources} total DNS-related resources</strong> distributed across <strong>${stats.subscriptions} subscription${stats.subscriptions !== 1 ? 's' : ''}</strong> and <strong>${stats.locations} location${stats.locations !== 1 ? 's' : ''}</strong>.</p>
                    ${stats.resource_counts.zones > 0 ? `<p>• <strong>${stats.resource_counts.zones}</strong> Private DNS Zones for internal name resolution</p>` : ''}
                    ${stats.resource_counts.vnet_links > 0 ? `<p>• <strong>${stats.resource_counts.vnet_links}</strong> VNet Links connecting zones to virtual networks</p>` : ''}
                    ${stats.resource_counts.endpoints > 0 ? `<p>• <strong>${stats.resource_counts.endpoints}</strong> Private Endpoints for secure service access</p>` : ''}
                    ${stats.resource_counts.resolvers > 0 ? `<p>• <strong>${stats.resource_counts.resolvers}</strong> Private Resolvers for conditional forwarding</p>` : ''}
                    ${stats.resource_counts.forwarding_rules > 0 ? `<p>• <strong>${stats.resource_counts.forwarding_rules}</strong> Forwarding Rulesets for DNS routing</p>` : ''}
                    ${stats.resource_counts.forwarding_rules_detail > 0 ? `<p>• <strong>${stats.resource_counts.forwarding_rules_detail}</strong> Individual Forwarding Rules with domain mappings</p>` : ''}
                </div>
            </div>
        `;
    }

    renderResourcePage(resourceType, resourceLabel) {
        const resources = this.flattenResources(resourceType);
        const content = document.getElementById('content');
        
        // Add console logging for private endpoints data
        if (resourceType === 'endpoints') {
            console.log('Private Endpoints - Resource count:', resources.length);
            console.log('Private Endpoints - Raw data:', resources);
            resources.forEach((endpoint, index) => {
                console.log(`=== Endpoint ${index + 1} - Complete Data Object ===`);
                console.log(JSON.stringify(endpoint, null, 2));
                console.log(`=== End of Endpoint ${index + 1} ===`);
            });
        }
        
        // Special handling for endpoints (Private Endpoints) - use dynamic headers
        if (resourceType === 'endpoints') {
            this.renderPrivateEndpointsPage(resources, resourceLabel);
            return;
        }
        
        // Define extra columns based on resource type with sortable functionality for other types
        let extraHeader = '';
        let extraHeaderCount = 0;
        if (resourceType === 'vnet_links') {
            extraHeader = '<th class="sortable" onclick="dnsReport.sortTable(5)">VNet</th><th class="sortable" onclick="dnsReport.sortTable(6)">Private Zone</th>';
            extraHeaderCount = 2;
        } else if (resourceType === 'zones') {
            extraHeader = '<th class="sortable" onclick="dnsReport.sortTable(5)">Record Count</th>';
            extraHeaderCount = 1;
        }
        
        // Create a count header for the current resource type
        const countHeader = `
            <div class="resource-count-header">
                <h2>📊 ${resourceLabel}s</h2>
                <div class="count-summary">
                    <span class="total-count">${resources.length}</span> ${resourceLabel}${resources.length !== 1 ? 's' : ''} found across 
                    <span class="subscription-count">${new Set(resources.map(r => r.subscription)).size}</span> subscription${new Set(resources.map(r => r.subscription)).size !== 1 ? 's' : ''}
                </div>
            </div>
        `;
        
        content.innerHTML = `
            ${countHeader}
            <div class="search-container">
                <input type="text" class="search-box" id="searchInput" 
                       placeholder="🔍 Search ${resourceLabel}s by name, subscription, resource group, location...">
            </div>
            <div class="table-container">
                <table id="resourceTable">
                    <thead>
                        <tr>
                            <th class="sortable" onclick="dnsReport.sortTable(0)">Subscription</th>
                            <th class="sortable" onclick="dnsReport.sortTable(1)">Name</th>
                            <th class="sortable" onclick="dnsReport.sortTable(2)">Resource Group</th>
                            <th class="sortable" onclick="dnsReport.sortTable(3)">Location</th>
                            <th class="sortable" onclick="dnsReport.sortTable(4)">Resource ID</th>
                            ${extraHeader}
                        </tr>
                    </thead>
                    <tbody>
                        ${resources.map(res => `
                            <tr style="font-family: monospace; font-size: 0.85em;">
                                <td>${res.subscription}</td>
                                <td>${res.name}</td>
                                <td>${res.resource_group}</td>
                                <td>${res.location}</td>
                                <td style="word-break: break-all;">${res.id}</td>
                                ${resourceType === 'zones' ? `<td>${res.record_count || 0}</td>` : ''}
                                ${resourceType === 'vnet_links' ? `<td>${res.vnet || ''}</td><td>${res.zoneName || ''}</td>` : ''}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        // Store original count and summary for filtering
        this.tableOriginalCount = resources.length;
        const summaryEl = content.querySelector('.count-summary');
        this.tableOriginalSummary = summaryEl ? summaryEl.innerHTML : '';
        this.setupSearch();
        this.sortTable(0); // Sort by subscription by default
    }
    
    renderPrivateEndpointsPage(resources, resourceLabel) {
        const content = document.getElementById('content');
        
        // Define the specific headers we want to show for Private Endpoints
        // Adjusted widths to fit without horizontal scrolling (total ~1400px for typical screens)
        const endpointHeaders = [
            { key: "id", label: "ID", width: "180px" },
            { key: "name", label: "Name", width: "120px" },
            { key: "type", label: "Type", width: "100px" },
            { key: "resource_group", label: "Resource Group", width: "120px" },
            { key: "subscription_id", label: "Subscription ID", width: "180px" },
            { key: "location", label: "Location", width: "80px" },
            { key: "subnet", label: "Subnet", width: "140px" },
            { key: "network_interfaces", label: "Network Interfaces", width: "120px" },
            { key: "private_link_service_connections", label: "Private Link Connections", width: "140px" },
            { key: "connections", label: "Connections", width: "120px" },
            { key: "primary_connection_state", label: "Connection State", width: "100px" },
            { key: "primary_target_service", label: "Target Service", width: "140px" },
            { key: "custom_dns_configs", label: "Custom DNS Configs", width: "120px" }
        ];
        
        // Create count header
        const countHeader = `
            <div class="resource-count-header">
                <h2>🔌 ${resourceLabel}s</h2>
                <div class="count-summary">
                    <span class="total-count">${resources.length}</span> ${resourceLabel}${resources.length !== 1 ? 's' : ''} found across 
                    <span class="subscription-count">${new Set(resources.map(r => r.subscription)).size}</span> subscription${new Set(resources.map(r => r.subscription)).size !== 1 ? 's' : ''}
                </div>
            </div>
        `;
        
        // Generate table headers
        const tableHeaders = endpointHeaders.map((header, index) => 
            `<th class="sortable" onclick="dnsReport.sortTable(${index})" style="min-width: ${header.width}; width: ${header.width};">${header.label}</th>`
        ).join('');
        
        // Generate table rows
        const tableRows = resources.map(endpoint => {
            return `
                <tr>
                    ${endpointHeaders.map(header => {
                        const value = endpoint[header.key];
                        return `<td style="min-width: ${header.width}; width: ${header.width}; max-width: ${header.width}; word-wrap: break-word;">${this.formatEndpointValue(value, header.key)}</td>`;
                    }).join('')}
                </tr>
            `;
        }).join('');
        
        content.innerHTML = `
            ${countHeader}
            <div class="search-container">
                <input type="text" class="search-box" id="searchInput" 
                       placeholder="🔍 Search Private Endpoints by name, resource group, connection state...">
            </div>
            <div class="table-container">
                <table id="resourceTable" class="private-endpoints-table" style="table-layout: fixed; width: 100%; max-width: 100%;">
                    <thead>
                        <tr>
                            ${tableHeaders}
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        `;

        // Store original count and summary for filtering
        this.tableOriginalCount = resources.length;
        const summaryEl = content.querySelector('.count-summary');
        this.tableOriginalSummary = summaryEl ? summaryEl.innerHTML : '';
        this.setupSearch();
        this.sortTable(0); // Sort by first column by default
    }
    
    formatEndpointValue(value, key) {
        if (value === null || value === undefined) {
            return '<span class="null-value" style="font-family: monospace; font-size: 0.85em;">N/A</span>';
        }
        
        // Apply consistent monospace font to all fields
        const monoStyle = "font-family: monospace; font-size: 0.85em;";
        
        switch (key) {
            case 'id':
                // Show full ID with monospace font
                return `<span style="word-break: break-all; ${monoStyle}">${value}</span>`;
                
            case 'subnet':
                if (typeof value === 'object' && value) {
                    const subnetInfo = [];
                    if (value.name) subnetInfo.push(`Name: ${value.name}`);
                    if (value.vnet_name) subnetInfo.push(`VNet: ${value.vnet_name}`);
                    if (value.id) subnetInfo.push(`ID: ${value.id}`);
                    if (subnetInfo.length > 0) {
                        return `<div class="foldable-content" onclick="this.classList.toggle('expanded')" style="${monoStyle}">
                            <div class="summary">${value.name || 'Subnet'}</div>
                            <div class="details">${subnetInfo.join('<br>')}</div>
                        </div>`;
                    }
                }
                return `<span style="${monoStyle}">${this.formatComplexValue(value)}</span>`;
                
            case 'network_interfaces':
                if (Array.isArray(value) && value.length > 0) {
                    const count = value.length;
                    const details = value.map(ni => `${ni.name || 'Unknown'} (${ni.private_ip_address || 'N/A'})`).join('<br>');
                    return `<div class="foldable-content" onclick="this.classList.toggle('expanded')" style="${monoStyle}">
                        <div class="summary">${count} interface${count !== 1 ? 's' : ''}</div>
                        <div class="details">${details}</div>
                    </div>`;
                }
                return `<span class="null-value" style="${monoStyle}">None</span>`;
                
            case 'private_link_service_connections':
                if (Array.isArray(value) && value.length > 0) {
                    const count = value.length;
                    const details = value.map(conn => {
                        const state = conn.private_link_service_connection_state?.status || 'Unknown';
                        const serviceId = conn.private_link_service_id || 'Unknown';
                        const description = conn.private_link_service_connection_state?.description || '';
                        return `State: ${state}<br>Service: ${serviceId}<br>Description: ${description}`;
                    }).join('<br><br>');
                    return `<div class="foldable-content" onclick="this.classList.toggle('expanded')" style="${monoStyle}">
                        <div class="summary">${count} connection${count !== 1 ? 's' : ''}</div>
                        <div class="details">${details}</div>
                    </div>`;
                }
                return `<span class="null-value" style="${monoStyle}">None</span>`;
                
            case 'connections':
                if (Array.isArray(value) && value.length > 0) {
                    const count = value.length;
                    const details = value.map(conn => {
                        return `${conn.name || 'Unknown'}: ${conn.connection_state || 'Unknown'}`;
                    }).join('<br>');
                    return `<div class="foldable-content" onclick="this.classList.toggle('expanded')" style="${monoStyle}">
                        <div class="summary">${count} connection${count !== 1 ? 's' : ''}</div>
                        <div class="details">${details}</div>
                    </div>`;
                }
                return `<span class="null-value" style="${monoStyle}">None</span>`;
                
            case 'custom_dns_configs':
                if (Array.isArray(value) && value.length > 0) {
                    const count = value.length;
                    const details = value.map(config => {
                        const fqdn = config.fqdn || 'Unknown';
                        const ips = config.ip_addresses ? config.ip_addresses.join(', ') : 'No IPs';
                        return `${fqdn} → ${ips}`;
                    }).join('<br>');
                    return `<div class="foldable-content" onclick="this.classList.toggle('expanded')" style="${monoStyle}">
                        <div class="summary">${count} DNS config${count !== 1 ? 's' : ''}</div>
                        <div class="details">${details}</div>
                    </div>`;
                }
                return `<span class="null-value" style="${monoStyle}">None</span>`;
                
            case 'primary_connection_state':
                if (value) {
                    const stateClass = this.getConnectionStateClass(value);
                    return `<span class="status-badge ${stateClass}" style="${monoStyle}">${value}</span>`;
                }
                return `<span class="null-value" style="${monoStyle}">Unknown</span>`;
                
            case 'primary_target_service':
                // Show full target service name
                return `<span style="${monoStyle}">${value}</span>`;
                
            case 'subscription_id':
                // Show full subscription ID with monospace font
                return `<span style="word-break: break-all; ${monoStyle}">${value}</span>`;
                
            default:
                if (typeof value === 'object') {
                    return `<span style="${monoStyle}">${this.formatComplexValue(value)}</span>`;
                }
                return `<span style="${monoStyle}">${this.escapeHtml(String(value))}</span>`;
        }
    }
    
    formatComplexValue(value) {
        if (Array.isArray(value)) {
            if (value.length === 0) return '<span class="null-value">Empty Array</span>';
            return `<div class="foldable-content" onclick="this.classList.toggle('expanded')">
                <div class="summary">Array (${value.length} items)</div>
                <div class="details"><pre>${JSON.stringify(value, null, 2)}</pre></div>
            </div>`;
        } else if (typeof value === 'object' && value !== null) {
            const keys = Object.keys(value);
            if (keys.length === 0) return '<span class="null-value">Empty Object</span>';
            return `<div class="foldable-content" onclick="this.classList.toggle('expanded')">
                <div class="summary">Object (${keys.length} properties)</div>
                <div class="details"><pre>${JSON.stringify(value, null, 2)}</pre></div>
            </div>`;
        }
        return String(value);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    renderEndpointExtraColumns(endpoint) {
        // Target Service
        const targetService = endpoint.primary_target_service || 'Unknown';
        
        // Connection State with status badge
        const connectionState = endpoint.primary_connection_state || 'Unknown';
        const connectionStateClass = this.getConnectionStateClass(connectionState);
        const connectionStateBadge = `<span class="status-badge ${connectionStateClass}">${connectionState}</span>`;
        
        // Subnet/VNet information
        const subnet = endpoint.subnet || {};
        const subnetVnet = subnet.vnet_name && subnet.name ? `${subnet.vnet_name}/${subnet.name}` : 'N/A';
        
        // Network Interfaces count and details
        const networkInterfaces = endpoint.network_interfaces || [];
        const niCount = networkInterfaces.length;
        const niDisplay = niCount > 0 ? `${niCount} interface${niCount !== 1 ? 's' : ''}` : 'N/A';
        
        return `
            <td title="${targetService}">${this.truncateText(targetService, 30)}</td>
            <td>${connectionStateBadge}</td>
            <td title="${subnetVnet}">${this.truncateText(subnetVnet, 25)}</td>
            <td title="${networkInterfaces.map(ni => ni.name).join(', ')}">${niDisplay}</td>
        `;
    }
    
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
    }

    renderResolversPage() {
        console.log('Rendering resolvers page');
        
        try {
            const hierarchicalData = this.buildResolverHierarchy();
            console.log('Hierarchical data for rendering:', hierarchicalData);
            
            // Calculate total counts for resolvers and rules
            const resolverCount = this.flattenResources('resolvers').length;
            const rulesetCount = this.flattenResources('forwarding_rules').length;
            const rulesDetailCount = this.flattenResources('forwarding_rules_detail').length;
            const totalCount = resolverCount + rulesetCount + rulesDetailCount;
            
            const content = document.getElementById('content');
            
            // Create count header
            const countHeader = `
                <div class="resource-count-header">
                    <h2>🔍 DNS Resolvers & Forwarding Rules</h2>
                    <div class="count-summary">
                        <span class="total-count">${totalCount}</span> total resources: 
                        <span class="resolver-breakdown">
                            ${resolverCount} Resolver${resolverCount !== 1 ? 's' : ''}, 
                            ${rulesetCount} Ruleset${rulesetCount !== 1 ? 's' : ''}, 
                            ${rulesDetailCount} Rule${rulesDetailCount !== 1 ? 's' : ''}
                        </span>
                    </div>
                </div>
            `;
            
            if (!hierarchicalData || hierarchicalData.length === 0) {
                content.innerHTML = `
                    ${countHeader}
                    <div style="padding: 40px; text-align: center; color: #666;">
                        <h2>📍 No DNS Resolvers Found</h2>
                        <p>No DNS Private Resolvers, Forwarding Rulesets, or Forwarding Rules were found in your Azure subscriptions.</p>
                        <p style="margin-top: 20px;">This could mean:</p>
                        <ul style="text-align: left; display: inline-block; margin-top: 20px;">
                            <li>No DNS Private Resolvers are configured</li>
                            <li>No Forwarding Rulesets exist</li>
                            <li>Access permissions prevent data retrieval</li>
                            <li>Resources exist in subscriptions not included in this report</li>
                        </ul>
                        <details style="margin-top: 20px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto;">
                            <summary style="cursor: pointer; font-weight: bold;">🔍 Debug Information</summary>
                            <div style="margin-top: 10px; font-family: monospace; font-size: 0.85em; background: #f5f5f5; padding: 10px; border-radius: 4px;">
                                <p>Raw resource counts:</p>
                                <p>• Resolvers: ${resolverCount}</p>
                                <p>• Forwarding Rulesets: ${rulesetCount}</p>
                                <p>• Forwarding Rules Detail: ${rulesDetailCount}</p>
                            </div>
                        </details>
                    </div>
                `;
                return;
            }
            
            content.innerHTML = `
                ${countHeader}
                <div class="search-container">
                    <input type="text" class="search-box" id="searchInput" 
                           placeholder="🔍 Search resolvers, rulesets, and forwarding rules...">
                    <div class="controls-container">
                        <button class="control-btn" onclick="dnsReport.expandAll()">📖 Expand All</button>
                        <button class="control-btn" onclick="dnsReport.collapseAll()">📕 Collapse All</button>
                    </div>
                </div>
                
                <div class="hierarchical-container">
                    ${hierarchicalData.map(resolver => this.renderResolverSection(resolver)).join('')}
                </div>
            `;

            this.setupSearch();
            this.setupExpandCollapse();
            // Persist original resolver counts and breakdown for filtering
            (function() {
                const totalCountEl = content.querySelector('.total-count');
                const breakdownEl = content.querySelector('.resolver-breakdown');
                if (totalCountEl) totalCountEl.dataset.originalCount = totalCountEl.textContent;
                if (breakdownEl) breakdownEl.dataset.originalText = breakdownEl.innerHTML;
            })();
            
        } catch (error) {
            console.error('Error rendering resolvers page:', error);
            const content = document.getElementById('content');
            content.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #d32f2f;">
                    <h2>❌ Error Rendering Resolvers</h2>
                    <p>There was an error building the resolver hierarchy.</p>
                    <p style="margin-top: 20px; font-family: monospace; font-size: 0.85em; background: #f5f5f5; padding: 10px; border-radius: 4px;">
                        ${error.message}
                    </p>
                    <p style="margin-top: 20px;">Check the browser console for more details.</p>
                </div>
            `;
        }
    }

    buildResolverHierarchy() {
        // Use the pre-built hierarchy from the Python backend
        // instead of building it client-side
        try {
            const hierarchyData = [];
            
            // Collect hierarchy data from all subscriptions
            if (this.data && Array.isArray(this.data)) {
                this.data.forEach(subscription => {
                    const subHierarchy = subscription.resolver_hierarchy || [];
                    subHierarchy.forEach(entry => {
                        // Add subscription context to each entry
                        const enrichedEntry = {
                            ...entry,
                            subscription_id: subscription.subscription_id,
                            subscription_name: subscription.subscription_name
                        };
                        
                        // Add subscription context to resolver if it exists
                        if (enrichedEntry.resolver) {
                            enrichedEntry.resolver.subscription = subscription.subscription_id;
                            enrichedEntry.resolver.subscription_name = subscription.subscription_name;
                        }
                        
                        // Add subscription context to all rulesets and rules
                        enrichedEntry.rulesets.forEach(rulesetEntry => {
                            if (rulesetEntry.ruleset) {
                                rulesetEntry.ruleset.subscription = subscription.subscription_id;
                                rulesetEntry.ruleset.subscription_name = subscription.subscription_name;
                            }
                            rulesetEntry.rules.forEach(rule => {
                                rule.subscription = subscription.subscription_id;
                                rule.subscription_name = subscription.subscription_name;
                            });
                        });
                        
                        hierarchyData.push(enrichedEntry);
                    });
                });
            }
            
            // Log final statistics
            const totalRulesets = hierarchyData.reduce((sum, entry) => sum + entry.rulesets.length, 0);
            const totalRules = hierarchyData.reduce((sum, entry) => 
                sum + entry.rulesets.reduce((ruleSum, rs) => ruleSum + rs.rules.length, 0), 0);
            
            console.log(`Loaded pre-built hierarchy: ${hierarchyData.length} entries, ${totalRulesets} rulesets, ${totalRules} rules`);
            
            return hierarchyData;
        } catch (error) {
            console.error('Error processing pre-built resolver hierarchy:', error);
            // Fallback to empty array if there's an error
            return [];
        }
    }

    renderResolverSection(resolverData) {
        const resolver = resolverData.resolver;
        const rulesets = resolverData.rulesets || []; // rulesets is now an array
        
        if (!resolver) {
            // Standalone rulesets without resolver
            return rulesets.map(rulesetData => this.renderStandaloneRuleset(rulesetData)).join('');
        }
        
        const resolverId = `resolver_${resolver.resource_guid || resolver.name}`;
        const totalRules = rulesets.reduce((sum, rs) => sum + rs.rules.length, 0);
        
        return `
            <div class="resolver-section">
                <div class="resolver-header" onclick="dnsReport.toggleSection('${resolverId}')">
                    <div class="resolver-info">
                        <span class="expand-icon" id="${resolverId}_icon">▶</span>
                        <div class="resolver-title">
                            <h3>🔍 ${resolver.name}</h3>
                            <div class="resolver-meta">
                                <span class="meta-item">📍 ${resolver.location}</span>
                                <span class="meta-item">📋 ${rulesets.length} ruleset${rulesets.length !== 1 ? 's' : ''}</span>
                                <span class="meta-item">📝 ${totalRules} rule${totalRules !== 1 ? 's' : ''}</span>
                                <span class="status-badge ${this.getStatusClass(resolver.provisioning_state)}">${resolver.provisioning_state || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="resolver-details">
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Subscription:</strong> ${resolver.subscription}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Resource Group:</strong> ${resolver.resource_group}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Resource GUID:</strong> ${resolver.resource_guid || 'N/A'}
                        </div>
                    </div>
                </div>
                <div class="resolver-content" id="${resolverId}_content" style="display: none;">
                    ${rulesets.map(rulesetData => this.renderRulesetSection(rulesetData, resolverId)).join('')}
                </div>
            </div>
        `;
    }

    renderStandaloneRuleset(rulesetData) {
        const ruleset = rulesetData.ruleset;
        const rulesetId = `standalone_ruleset_${ruleset.name}`;
        
        return `
            <div class="resolver-section">
                <div class="resolver-header" onclick="dnsReport.toggleSection('${rulesetId}')">
                    <div class="resolver-info">
                        <span class="expand-icon" id="${rulesetId}_icon">▶</span>
                        <div class="resolver-title">
                            <h3>📋 ${ruleset.name} <span class="standalone-label">(Standalone Ruleset)</span></h3>
                            <div class="resolver-meta">
                                <span class="meta-item">📍 ${ruleset.location}</span>
                                <span class="meta-item">📝 ${rulesetData.rules.length} rule${rulesetData.rules.length !== 1 ? 's' : ''}</span>
                                <span class="status-badge ${this.getStatusClass(ruleset.provisioning_state)}">${ruleset.provisioning_state || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="resolver-details">
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Subscription:</strong> ${ruleset.subscription}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Resource Group:</strong> ${ruleset.resource_group}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Outbound Endpoints:</strong> ${this.formatOutboundEndpoints(ruleset)}
                        </div>
                    </div>
                </div>
                <div class="resolver-content" id="${rulesetId}_content" style="display: none;">
                    ${this.renderRulesTable(rulesetData.rules)}
                </div>
            </div>
        `;
    }

    renderRulesetSection(rulesetData, parentId) {
        const ruleset = rulesetData.ruleset;
        const rulesetId = `${parentId}_ruleset_${ruleset.name}`;
        
        return `
            <div class="ruleset-section">
                <div class="ruleset-header" onclick="dnsReport.toggleSection('${rulesetId}')">
                    <div class="ruleset-info">
                        <span class="expand-icon" id="${rulesetId}_icon">▶</span>
                        <div class="ruleset-title">
                            <h4>📋 ${ruleset.name}</h4>
                            <div class="ruleset-meta">
                                <span class="meta-item">📝 ${rulesetData.rules.length} rule${rulesetData.rules.length !== 1 ? 's' : ''}</span>
                                <span class="status-badge ${this.getStatusClass(ruleset.provisioning_state)}">${ruleset.provisioning_state || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="ruleset-details">
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Resource Group:</strong> ${ruleset.resource_group}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Outbound Endpoints:</strong> ${this.formatOutboundEndpoints(ruleset)}
                        </div>
                        <div class="detail-item" style="font-family: monospace; font-size: 0.85em;">
                            <strong>Resource GUID:</strong> ${ruleset.resource_guid || 'N/A'}
                        </div>
                    </div>
                </div>
                <div class="ruleset-content" id="${rulesetId}_content" style="display: none;">
                    ${this.renderRulesTable(rulesetData.rules)}
                </div>
            </div>
        `;
    }

    renderRulesTable(rules) {
        if (!rules || rules.length === 0) {
            return '<div class="no-rules">No forwarding rules configured</div>';
        }
        
        // Generate unique table ID to support multiple rules tables on the same page
        const tableId = `rules-table-${Math.random().toString(36).substr(2, 9)}`;
        
        return `
            <div class="rules-table-container">
                <table class="rules-table" id="${tableId}">
                    <thead>
                        <tr>
                            <th class="sortable" onclick="dnsReport.sortTable(0, '${tableId}')">Rule Name</th>
                            <th class="sortable" onclick="dnsReport.sortTable(1, '${tableId}')">Domain Name</th>
                            <th class="sortable" onclick="dnsReport.sortTable(2, '${tableId}')">State</th>
                            <th class="sortable" onclick="dnsReport.sortTable(3, '${tableId}')">Target DNS Servers</th>
                            <th class="sortable" onclick="dnsReport.sortTable(4, '${tableId}')">Provisioning State</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${rules.map(rule => `
                            <tr style="font-family: monospace; font-size: 0.85em;">
                                <td>${rule.name}</td>
                                <td style="color: #2196F3;">${rule.domain_name || 'N/A'}</td>
                                <td><span class="status-badge ${this.getRuleStateClass(rule.forwarding_rule_state)}">${rule.forwarding_rule_state || 'Unknown'}</span></td>
                                <td>${this.formatTargetDnsServers(rule.target_dns_servers)}</td>
                                <td><span class="status-badge ${this.getStatusClass(rule.provisioning_state)}">${rule.provisioning_state || 'Unknown'}</span></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    toggleSection(sectionId) {
        const content = document.getElementById(`${sectionId}_content`);
        const icon = document.getElementById(`${sectionId}_icon`);
        
        if (content && icon) {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            icon.textContent = isHidden ? '▼' : '▶';
        }
    }

    expandAll() {
        document.querySelectorAll('.resolver-content, .ruleset-content').forEach(content => {
            content.style.display = 'block';
        });
        document.querySelectorAll('.expand-icon').forEach(icon => {
            icon.textContent = '▼';
        });
    }

    collapseAll() {
        document.querySelectorAll('.resolver-content, .ruleset-content').forEach(content => {
            content.style.display = 'none';
        });
        document.querySelectorAll('.expand-icon').forEach(icon => {
            icon.textContent = '▶';
        });
    }

    setupExpandCollapse() {
        // Initial state - collapse all
        this.collapseAll();
    }

    // Helper method to get CSS class for provisioning state
    getStatusClass(state) {
        if (!state) return 'status-unknown';
        const normalizedState = state.toLowerCase();
        switch (normalizedState) {
            case 'succeeded':
                return 'status-success';
            case 'failed':
                return 'status-error';
            case 'creating':
            case 'updating':
            case 'deleting':
                return 'status-pending';
            case 'canceled':
                return 'status-warning';
            default:
                return 'status-unknown';
        }
    }

    // Helper method to get CSS class for forwarding rule state
    getRuleStateClass(state) {
        if (!state) return 'status-unknown';
        const normalizedState = state.toLowerCase();
        switch (normalizedState) {
            case 'enabled':
                return 'status-success';
            case 'disabled':
                return 'status-warning';
            default:
                return 'status-unknown';
        }
    }

    // Helper method to format outbound endpoints
    formatOutboundEndpoints(endpoints) {
        // Check for DNS resolver outbound endpoints stored in alternative fields
        let actualEndpoints;
        
        if (endpoints && endpoints.dns_resolver_outbound_endpoints && Array.isArray(endpoints.dns_resolver_outbound_endpoints)) {
            // Handle the case where we're passed an object with dns_resolver_outbound_endpoints field
            actualEndpoints = endpoints.dns_resolver_outbound_endpoints;
        } else if (Array.isArray(endpoints)) {
            // Handle the case where we're passed an array directly
            actualEndpoints = endpoints;
        } else {
            // Default to empty array for any other case
            actualEndpoints = [];
        }
        
        // If we still have no endpoints, return N/A
        if (actualEndpoints.length === 0) {
            return '<span style="font-family: monospace; font-size: 0.85em;">N/A</span>';
        }
        
        return actualEndpoints.map(ep => {
            const id = ep.id || '';
            const name = id.split('/').pop() || 'Unknown';
            return `<span class="endpoint-tag" style="font-family: monospace; font-size: 0.85em;">${name}</span>`;
        }).join(' ');
    }

    // Helper method to format target DNS servers
    formatTargetDnsServers(servers) {
        if (!servers || !Array.isArray(servers) || servers.length === 0) {
            return '<span style="font-family: monospace; font-size: 0.85em;">N/A</span>';
        }
        
        return servers.map(server => {
            const ip = server.ip_address || 'Unknown';
            const port = server.port || 53;
            return `<span class="dns-server-tag" style="font-family: monospace; font-size: 0.85em;">${ip}:${port}</span>`;
        }).join('<br>');
    }
    
    getConnectionStateClass(state) {
        if (!state) return 'status-unknown';
        const normalizedState = state.toLowerCase();
        switch (normalizedState) {
            case 'approved':
                return 'status-success';
            case 'pending':
                return 'status-pending';
            case 'rejected':
                return 'status-error';
            case 'disconnected':
                return 'status-warning';
            default:
                return 'status-unknown';
        }
    }
    
    // Contains AI-generated edits.

    flattenResources(resourceType) {
        const resources = [];
        
        this.data.forEach(sub => {
            const subName = sub.subscription_name;
            const items = sub[resourceType] || [];
            
            console.log(`Flattening ${resourceType} for subscription ${subName}: ${items.length} items`);
            
            items.forEach(item => {
                const id = item.id || '';
                const parts = id.split('/');
                
                // Extract resource group from Azure resource ID pattern
                // Pattern: /subscriptions/{subscription-id}/resourceGroups/{resource-group-name}/...
                let resourceGroup = '';
                const rgIndex = parts.indexOf('resourceGroups');
                if (rgIndex !== -1 && rgIndex + 1 < parts.length) {
                    resourceGroup = parts[rgIndex + 1];
                } else {
                    // Fallback: check if it's already set in the item
                    resourceGroup = item.resource_group || 'undefined';
                }
                
                const base = {
                    subscription: subName,
                    name: item.name || '',
                    resource_group: resourceGroup,
                    location: item.location || '',
                    id: id
                };

                // Add resource-specific properties
                if (resourceType === 'vnet_links') {
                    base.vnet = item.vnet || '';
                    base.zoneName = item.zoneName || '';
                } else if (resourceType === 'resolvers') {
                    // DNS Resolver specific properties
                    const properties = item.properties || {};
                    base.provisioning_state = properties.provisioningState || item.provisioning_state;
                    base.resource_guid = properties.resourceGuid || item.resource_guid;
                } else if (resourceType === 'forwarding_rules') {
                    // DNS Forwarding Ruleset specific properties
                    const properties = item.properties || {};
                    base.provisioning_state = properties.provisioningState || item.provisioning_state;
                    base.resource_guid = properties.resourceGuid || item.resource_guid;
                    
                    // Handle outbound endpoints with multiple possible sources
                    base.outbound_endpoints = (
                        properties.dnsResolverOutboundEndpoints ||
                        item.dnsResolverOutboundEndpoints ||
                        item.outbound_endpoints ||
                        properties.outbound_endpoints ||
                        []
                    );
                    
                    // Ensure outbound_endpoints is an array
                    if (!Array.isArray(base.outbound_endpoints)) {
                        base.outbound_endpoints = [];
                    }
                    
                    console.log(`Ruleset ${base.name} has ${base.outbound_endpoints.length} outbound endpoints:`, base.outbound_endpoints);
                } else if (resourceType === 'forwarding_rules_detail') {
                    // Individual forwarding rule specific properties
                    const properties = item.properties || {};
                    base.domain_name = properties.domainName || item.domain_name;
                    base.target_dns_servers = properties.targetDnsServers || item.target_dns_servers || [];
                    base.forwarding_rule_state = properties.forwardingRuleState || item.forwarding_rule_state;
                    base.provisioning_state = properties.provisioningState || item.provisioning_state;
                    base.ruleset_name = item.rulesetName || item.ruleset_name;
                    
                    console.log(`Rule ${base.name} belongs to ruleset: ${base.ruleset_name}`);
                    
                    // Handle metadata if present
                    if (properties.metadata || item.metadata) {
                        base.metadata = properties.metadata || item.metadata;
                    }
                } else if (resourceType === 'endpoints') {
                    // Pass through raw data for dynamic JS parsing and display
                    // Copy all properties from the original item to preserve the complete structure
                    Object.assign(base, item);
                    
                    console.log(`Endpoint ${base.name}: raw data preserved for dynamic display`);
                }
                
                resources.push(base);
            });
        });
        
        console.log(`Flattened ${resourceType}: ${resources.length} total resources`);
        return resources;
    }

    sortTable(columnIndex, tableId = 'resourceTable') {
        const table = document.getElementById(tableId);
        if (!table) return;

        const tbody = table.tBodies[0];
        const rows = Array.from(tbody.rows);
        const headers = table.tHead.rows[0].cells;

        // Initialize sort direction array if needed for this specific table
        if (!this.sortDirections[tableId] || this.sortDirections[tableId].length === 0) {
            this.sortDirections[tableId] = Array.from(headers).map(() => 0);
        }

        // Toggle sort direction
        this.sortDirections[tableId][columnIndex] = this.sortDirections[tableId][columnIndex] === 1 ? -1 : 1;

        // Update header classes
        for (let i = 0; i < headers.length; i++) {
            headers[i].classList.remove('sort-asc', 'sort-desc');
        }
        headers[columnIndex].classList.add(
            this.sortDirections[tableId][columnIndex] === 1 ? 'sort-asc' : 'sort-desc'
        );

        // Sort rows
        rows.sort((a, b) => {
            let aText, bText;
            const aCell = a.cells[columnIndex];
            const bCell = b.cells[columnIndex];
            
            // Handle special cases for different content types
            const aStatusBadge = aCell.querySelector('.status-badge');
            const bStatusBadge = bCell.querySelector('.status-badge');
            
            if (aStatusBadge && bStatusBadge) {
                // Sort by status badge text content
                aText = aStatusBadge.textContent.trim().toLowerCase();
                bText = bStatusBadge.textContent.trim().toLowerCase();
            } else if (aCell.querySelector('.dns-server-tag') && bCell.querySelector('.dns-server-tag')) {
                // Sort by first DNS server IP for Target DNS Servers column
                const aDnsTag = aCell.querySelector('.dns-server-tag');
                const bDnsTag = bCell.querySelector('.dns-server-tag');
                aText = aDnsTag ? aDnsTag.textContent.trim().toLowerCase() : '';
                bText = bDnsTag ? bDnsTag.textContent.trim().toLowerCase() : '';
            } else {
                // Default text content sorting
                aText = aCell.textContent.trim().toLowerCase();
                bText = bCell.textContent.trim().toLowerCase();
            }
            
            // Handle numeric sorting for record count and similar numeric fields
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return (aNum - bNum) * this.sortDirections[tableId][columnIndex];
            }
            
            // Default string comparison
            return aText.localeCompare(bText) * this.sortDirections[tableId][columnIndex];
        });

        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        // Store original counts and text for filtering if not already stored
        if (this.currentPage !== 'resolvers') {
            const totalCountEl = document.querySelector('.total-count');
            const countSummaryEl = document.querySelector('.count-summary');
            if (totalCountEl && countSummaryEl && !totalCountEl.dataset.originalCount) {
                totalCountEl.dataset.originalCount = totalCountEl.textContent;
                countSummaryEl.dataset.originalText = countSummaryEl.innerHTML;
            }
        } else {
            const totalCountEl = document.querySelector('.total-count');
            const breakdownEl = document.querySelector('.resolver-breakdown');
            if (totalCountEl && breakdownEl && !totalCountEl.dataset.originalCount) {
                totalCountEl.dataset.originalCount = totalCountEl.textContent;
                breakdownEl.dataset.originalText = breakdownEl.innerHTML;
            }
        }
        // Use input event to capture all changes including deletions and clear actions
        searchInput.addEventListener('input', (e) => {
            const filter = e.target.value.toLowerCase();
            
            if (this.currentPage === 'resolvers') {
                // Handle hierarchical search and update counts
                const filteredCounts = this.searchHierarchical(filter);
                this.updateFilteredCounts(filteredCounts);
            } else {
                // Handle table search for other pages
                let visibleCount = 0;
                const tables = document.querySelectorAll('table');
                
                tables.forEach(table => {
                    const rows = table.getElementsByTagName('tr');
                    
                    for (let i = 1; i < rows.length; i++) {
                        const row = rows[i];
                        const cells = row.getElementsByTagName('td');
                        let found = false;
                        
                        for (let j = 0; j < cells.length; j++) {
                            if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                                found = true;
                                break;
                            }
                        }
                        
                        row.style.display = found ? '' : 'none';
                        if (found) visibleCount++;
                    }
                });
                
                // Update count display for table-based pages
                this.updateTableFilteredCount(visibleCount);
            }
        });
    }

    updateFilteredCount(filteredResources) {
        const countElement = document.querySelector('#filtered-count');
        if (countElement) {
            countElement.textContent = `Filtered Resources: ${filteredResources.length}`;
        }
    }

    applySearchFilter(searchString) {
        const filteredResources = this.resources.filter(resource => {
            return Object.values(resource).some(value => 
                typeof value === 'string' && value.includes(searchString)
            );
        });

        this.updateFilteredCount(filteredResources);
        return filteredResources;
    }

    searchHierarchical(filter) {
        const resolverSections = document.querySelectorAll('.resolver-section');
        let visibleResolvers = 0;
        let visibleRulesets = 0;
        let visibleRules = 0;
        
        resolverSections.forEach(section => {
            let sectionVisible = false;
            const header = section.querySelector('.resolver-header');
            const content = section.querySelector('.resolver-content');
            
            // Check if resolver header matches
            const headerText = header.textContent.toLowerCase();
            const headerMatches = !filter || headerText.indexOf(filter) > -1;
            
            if (headerMatches) {
                sectionVisible = true;
                visibleResolvers++;
                // Show all rulesets when resolver matches
                const rulesetSections = section.querySelectorAll('.ruleset-section');
                rulesetSections.forEach(rulesetSection => {
                    rulesetSection.style.display = 'block';
                    visibleRulesets++;
                    const rulesetContent = rulesetSection.querySelector('.ruleset-content');
                    const rulesTable = rulesetContent.querySelector('.rules-table tbody');
                    if (rulesTable) {
                        const rows = rulesTable.getElementsByTagName('tr');
                        for (let i = 0; i < rows.length; i++) {
                            rows[i].style.display = '';
                            visibleRules++;
                        }
                    }
                });
            } else {
                // Check rulesets and rules
                const rulesetSections = section.querySelectorAll('.ruleset-section');
                rulesetSections.forEach(rulesetSection => {
                    let rulesetVisible = false;
                    const rulesetHeader = rulesetSection.querySelector('.ruleset-header');
                    const rulesetContent = rulesetSection.querySelector('.ruleset-content');
                    
                    // Check if ruleset header matches
                    const rulesetHeaderText = rulesetHeader.textContent.toLowerCase();
                    const rulesetMatches = rulesetHeaderText.indexOf(filter) > -1;
                    
                    if (rulesetMatches) {
                        rulesetVisible = true;
                        sectionVisible = true;
                        visibleRulesets++;
                        // Show all rules when ruleset matches
                        const rulesTable = rulesetContent.querySelector('.rules-table tbody');
                        if (rulesTable) {
                            const rows = rulesTable.getElementsByTagName('tr');
                            for (let i = 0; i < rows.length; i++) {
                                rows[i].style.display = '';
                                visibleRules++;
                            }
                        }
                    } else {
                        // Check individual rules
                        const rulesTable = rulesetContent.querySelector('.rules-table tbody');
                        if (rulesTable) {
                            const rows = rulesTable.getElementsByTagName('tr');
                            let anyRuleVisible = false;
                            
                            for (let i = 0; i < rows.length; i++) {
                                const row = rows[i];
                                const cells = row.getElementsByTagName('td');
                                let ruleMatches = false;
                                
                                for (let j = 0; j < cells.length; j++) {
                                    if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                                        ruleMatches = true;
                                        break;
                                    }
                                }
                                
                                if (ruleMatches) {
                                    row.style.display = '';
                                    anyRuleVisible = true;
                                    rulesetVisible = true;
                                    sectionVisible = true;
                                    visibleRules++;
                                } else {
                                    row.style.display = 'none';
                                }
                            }
                            
                            if (anyRuleVisible) {
                                visibleRulesets++;
                            }
                        }
                    }
                    
                    rulesetSection.style.display = rulesetVisible ? 'block' : 'none';
                });
                
                if (sectionVisible) {
                    visibleResolvers++;
                }
            }
            
            section.style.display = sectionVisible ? 'block' : 'none';
            
            // Auto-expand sections that match the search
            if (sectionVisible && filter) {
                const expandIcon = section.querySelector('.expand-icon');
                const resolverContent = section.querySelector('.resolver-content');
                if (resolverContent && expandIcon) {
                    resolverContent.style.display = 'block';
                    expandIcon.textContent = '▼';
                    
                    // Also expand matching rulesets
                    const visibleRulesets = section.querySelectorAll('.ruleset-section[style*="block"]');
                    visibleRulesets.forEach(ruleset => {
                        const rulesetIcon = ruleset.querySelector('.expand-icon');
                        const rulesetContent = ruleset.querySelector('.ruleset-content');
                        if (rulesetContent && rulesetIcon) {
                            rulesetContent.style.display = 'block';
                            rulesetIcon.textContent = '▼';
                        }
                    });
                }
            }
        });
        
        return {
            resolvers: visibleResolvers,
            rulesets: visibleRulesets,
            rules: visibleRules,
            total: visibleResolvers + visibleRulesets + visibleRules
        };
    }

    // Helper method to update filtered counts for table-based pages
    updateTableFilteredCount(visibleCount) {
        // Update filtered counts display for table pages
        const countSummaryEl = document.querySelector('.resource-count-header .count-summary');
        if (!countSummaryEl) return;
        // Initialize original summary and count
        if (!countSummaryEl.dataset.originalText) {
            countSummaryEl.dataset.originalText = countSummaryEl.innerHTML;
        }
        const totalCountEl = countSummaryEl.querySelector('.total-count');
        if (!totalCountEl) return;
        if (!totalCountEl.dataset.originalCount) {
            totalCountEl.dataset.originalCount = totalCountEl.textContent;
        }
        const originalCount = parseInt(totalCountEl.dataset.originalCount, 10);
        // Reset or update summary
        if (visibleCount === originalCount) {
            countSummaryEl.innerHTML = countSummaryEl.dataset.originalText;
        } else {
            const resourceLabel = this.getResourceLabel();
            const subscriptionCount = this.getUniqueSubscriptionCount();
            const filteredText = countSummaryEl.dataset.originalText
                .replace(new RegExp(`>${originalCount}<`), `>${visibleCount}<`);
            countSummaryEl.innerHTML = `${filteredText} ` +
                `<span class="filter-info">(${visibleCount} of ${originalCount} filtered)</span>`;
        }
    }

    // Helper method to update filtered counts for hierarchical resolver page
    updateFilteredCounts(counts) {
        const totalCountElement = document.querySelector('.total-count');
        const resolverBreakdownElement = document.querySelector('.resolver-breakdown');
        
        if (totalCountElement && resolverBreakdownElement) {
            const originalTotalCount = parseInt(totalCountElement.dataset.originalCount || totalCountElement.textContent);
            
            // Store original counts if not already stored
            if (!totalCountElement.dataset.originalCount) {
                totalCountElement.dataset.originalCount = originalTotalCount;
                resolverBreakdownElement.dataset.originalText = resolverBreakdownElement.textContent;
            }
            
            // Update the count display
            totalCountElement.textContent = counts.total;
            
            // Update the breakdown text to show filtered state
            if (counts.total === originalTotalCount) {
                // Reset to original text when no filter is applied
                resolverBreakdownElement.innerHTML = resolverBreakdownElement.dataset.originalText;
            } else {
                // Show filtered counts with original counts in parentheses
                resolverBreakdownElement.innerHTML = `
                    ${counts.resolvers} Resolver${counts.resolvers !== 1 ? 's' : ''}, 
                    ${counts.rulesets} Ruleset${counts.rulesets !== 1 ? 's' : ''}, 
                    ${counts.rules} Rule${counts.rules !== 1 ? 's' : ''}
                    <span class="filter-info">(filtered from ${originalTotalCount} total)</span>
                `;
            }
        }
    }

    // Helper method to get current resource label
    getResourceLabel() {
        switch(this.currentPage) {
            case 'zones': return 'Private DNS Zone';
            case 'vnet_links': return 'VNet Link';
            case 'endpoints': return 'Private Endpoint';
            default: return 'Resource';
        }
    }

    // Helper method to get unique subscription count for current page
    getUniqueSubscriptionCount() {
        const resourceType = this.currentPage;
        const resources = this.flattenResources(resourceType);
        return new Set(resources.map(r => r.subscription)).size;
    }
}

// Initialize the report when script loads
let dnsReport;
console.log('DNS Private Report script loaded');

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing DNS report');
    try {
        dnsReport = new DNSPrivateReport();
        console.log('DNS report initialized successfully');
    } catch (error) {
        console.error('Error initializing DNS report:', error);
        
        // Show error message to user
        const content = document.getElementById('content');
        if (content) {
            content.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #d32f2f;">
                    <h2>❌ Error Loading Report</h2>
                    <p>There was an error initializing the DNS private resources report.</p>
                    <p style="margin-top: 20px; font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 4px;">
                        ${error.message}
                    </p>
                    <p style="margin-top: 20px;">Check the browser console for more details.</p>
                </div>
            `;
        }
    }
});

/* Contains AI-generated edits. */
