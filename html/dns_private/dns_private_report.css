body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }
            
            .container {
                max-width: 1800px;
                margin: 0 auto;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            
            .header {
                background: linear-gradient(135deg, #0078d4, #106ebe);
                color: white;
                padding: 30px;
                text-align: center;
            }
            
            .header h1 {
                margin: 0;
                font-size: 2.5em;
                font-weight: 300;
            }
            
            .header p {
                margin: 10px 0 0 0;
                opacity: 0.9;
                font-size: 1.1em;
            }
            
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                padding: 30px;
                background: #fafafa;
            }
            
            .stat-card {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-left: 4px solid #0078d4;
            }
            
            .stat-card.highlight {
                background: linear-gradient(135deg, #0078d4, #106ebe);
                color: white;
                transform: scale(1.05);
            }
            
            .stat-card.highlight .stat-number {
                color: white;
            }
            
            .stat-card.highlight .stat-label {
                color: rgba(255, 255, 255, 0.9);
            }
            
            .stat-number {
                font-size: 2.5em;
                font-weight: bold;
                color: #0078d4;
                margin: 0;
            }
            
            .stat-label {
                color: #666;
                margin: 5px 0 0 0;
                font-size: 0.9em;
            }
            
            .table-container {
                padding: 30px;
                overflow-x: auto;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                table-layout: auto;
            }
            
            th {
                background: #0078d4;
                color: white;
                padding: 15px 10px;
                text-align: left;
                font-weight: 500;
                cursor: pointer;
                position: relative;
                user-select: none;
                transition: background-color 0.2s;
            }
            
            th:hover {
                background: #106ebe;
            }
            
            th.sortable:after {
                content: ' ↕';
                opacity: 0.5;
            }
            
            th.sort-asc:after {
                content: ' ↑';
                opacity: 1;
            }
            
            th.sort-desc:after {
                content: ' ↓';
                opacity: 1;
            }
            
            td {
                padding: 12px 10px;
                border-bottom: 1px solid #eee;
                vertical-align: top;
            }
            
            tr:hover {
                background-color: #f8f9fa;
            }
            
            .status-badge {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .status-success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .status-error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .status-warning {
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            .status-pending {
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
            
            .status-unknown {
                background-color: #e2e3e5;
                color: #495057;
                border: 1px solid #ced4da;
            }
            
            /* Endpoint and DNS server tags */
            .endpoint-tag {
                display: inline-block;
                background-color: #e3f2fd;
                color: #1565c0;
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 0.85em;
                font-weight: 500;
                margin: 1px;
                border: 1px solid #bbdefb;
            }
            
            .dns-server-tag {
                display: inline-block;
                background-color: #f3e5f5;
                color: #7b1fa2;
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 0.85em;
                font-weight: 500;
                font-family: monospace;
                margin: 1px;
                border: 1px solid #e1bee7;
            }
            
            /* Enhanced table styling for resolver details */
            .table-container table th:nth-child(5),
            .table-container table th:nth-child(6),
            .table-container table th:nth-child(7) {
                min-width: 120px;
            }
            
            .table-container table td {
                vertical-align: top;
            }
            
            /* Domain name styling */
            td[style*="color: #2196F3"] {
                font-weight: 500;
            }

            .nav-bar {
                display: flex;
                justify-content: center;
                background-color: #e9ecef;
                padding: 10px 0;
            }

            .nav-link {
                padding: 10px 20px;
                cursor: pointer;
                border-radius: 5px;
                margin: 0 10px;
                transition: background-color 0.3s;
                position: relative;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .nav-link:hover, .nav-link.active {
                background-color: #0078d4;
                color: white;
            }
            
            .count-badge {
                background-color: #ff6b35;
                color: white;
                font-size: 0.8em;
                font-weight: 600;
                padding: 2px 6px;
                border-radius: 10px;
                min-width: 16px;
                text-align: center;
                line-height: 1.2;
            }
            
            .nav-link.active .count-badge {
                background-color: rgba(255, 255, 255, 0.9);
                color: #0078d4;
            }
            
            /* Resource Count Header Styles */
            .resource-count-header {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                padding: 20px 30px;
                border-bottom: 2px solid #dee2e6;
                margin-bottom: 20px;
            }
            
            .resource-count-header h2 {
                margin: 0 0 10px 0;
                color: #0078d4;
                font-size: 1.8em;
                font-weight: 500;
            }
            
            .count-summary {
                font-size: 1.1em;
                color: #495057;
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;
            }
            
            .total-count {
                background-color: #0078d4;
                color: white;
                font-weight: 600;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 1.2em;
            }
            
            .subscription-count {
                background-color: #28a745;
                color: white;
                font-weight: 600;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 0.9em;
            }
            
            .resolver-breakdown {
                font-size: 0.95em;
            }
            
            /* Filter info styling */
            .filter-info {
                color: #6c757d;
                font-style: italic;
                font-size: 0.9em;
                margin-left: 8px;
            }
            
            .count-summary .filter-info {
                background-color: #f8f9fa;
                padding: 2px 6px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }
            
            /* Overview Section Styles */
            .overview-section {
                margin-bottom: 30px;
            }
            
            .section-title {
                margin: 0 0 20px 0;
                padding: 20px 30px 10px 30px;
                font-size: 1.5em;
                color: #0078d4;
                border-bottom: 2px solid #e5e5e5;
                font-weight: 500;
            }
            
            .resource-counts-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                padding: 0 30px 20px 30px;
            }
            
            .resource-count-card {
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 10px;
                padding: 20px;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            
            .resource-count-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-color: #0078d4;
            }
            
            .resource-icon {
                font-size: 2.5em;
                margin-right: 15px;
                opacity: 0.8;
            }
            
            .resource-info {
                flex: 1;
            }
            
            .resource-number {
                font-size: 2em;
                font-weight: bold;
                color: #0078d4;
                margin: 0;
                line-height: 1;
            }
            
            .resource-label {
                color: #666;
                margin: 5px 0 0 0;
                font-size: 0.95em;
                font-weight: 500;
            }
            
            /* Distribution Info */
            .distribution-info {
                background: #f8f9fa;
                padding: 25px;
                margin: 0 30px 20px 30px;
                border-radius: 8px;
                border-left: 4px solid #0078d4;
            }
            
            .distribution-info p {
                margin: 0 0 10px 0;
                color: #555;
                line-height: 1.6;
            }
            
            .distribution-info p:last-child {
                margin-bottom: 0;
            }
            
            .distribution-info strong {
                color: #0078d4;
                font-weight: 600;
            }
            
            /* Hierarchical Structure Styles for Resolvers */
            .hierarchical-container {
                padding: 20px 30px;
            }
            
            .controls-container {
                display: flex;
                gap: 10px;
                margin-left: 20px;
            }
            
            .control-btn {
                background: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 0.9em;
                transition: background-color 0.2s;
            }
            
            .control-btn:hover {
                background: #106ebe;
            }
            
            .resolver-section {
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                overflow: hidden;
            }
            
            .resolver-header {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                padding: 20px;
                cursor: pointer;
                border-bottom: 1px solid #e1e5e9;
                transition: background-color 0.2s;
            }
            
            .resolver-header:hover {
                background: linear-gradient(135deg, #e9ecef, #dee2e6);
            }
            
            .resolver-info {
                display: flex;
                align-items: flex-start;
                gap: 15px;
            }
            
            .expand-icon {
                font-size: 1.2em;
                color: #0078d4;
                font-weight: bold;
                margin-top: 2px;
                min-width: 20px;
            }
            
            .resolver-title h3 {
                margin: 0 0 10px 0;
                color: #0078d4;
                font-size: 1.4em;
                font-weight: 600;
            }
            
            .resolver-meta {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                align-items: center;
            }
            
            .meta-item {
                font-size: 0.9em;
                color: #666;
                font-weight: 500;
            }
            
            .resolver-details {
                margin-top: 15px;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }
            
            .detail-item {
                font-size: 0.9em;
                color: #555;
            }
            
            .detail-item strong {
                color: #333;
                font-weight: 600;
            }
            
            .standalone-label {
                font-size: 0.8em;
                color: #666;
                font-weight: normal;
                opacity: 0.8;
            }
            
            .resolver-content {
                padding: 0;
            }
            
            .ruleset-section {
                border-top: 1px solid #f0f0f0;
                margin: 0;
            }
            
            .ruleset-header {
                background: #fafbfc;
                padding: 15px 20px;
                cursor: pointer;
                border-bottom: 1px solid #f0f0f0;
                transition: background-color 0.2s;
                margin-left: 30px;
            }
            
            .ruleset-header:hover {
                background: #f1f3f4;
            }
            
            .ruleset-info {
                display: flex;
                align-items: flex-start;
                gap: 15px;
            }
            
            .ruleset-title h4 {
                margin: 0 0 8px 0;
                color: #333;
                font-size: 1.2em;
                font-weight: 600;
            }
            
            .ruleset-meta {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                align-items: center;
            }
            
            .ruleset-details {
                margin-top: 12px;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 12px;
            }
            
            .ruleset-content {
                padding: 0;
                margin-left: 30px;
            }
            
            .rules-table-container {
                padding: 20px;
                background: #ffffff;
            }
            
            .rules-table {
                width: 100%;
                border-collapse: collapse;
                margin: 0;
                border: 1px solid #e1e5e9;
                border-radius: 6px;
                overflow: hidden;
            }
            
            .rules-table th {
                background: #f8f9fa;
                color: #333;
                padding: 12px 10px;
                text-align: left;
                font-weight: 600;
                font-size: 0.9em;
                border-bottom: 2px solid #e1e5e9;
            }
            
            .rules-table td {
                padding: 12px 10px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 0.9em;
            }
            
            .rules-table tr:hover {
                background-color: #f8f9fa;
            }
            
            .rules-table tr:last-child td {
                border-bottom: none;
            }
            
            .no-rules {
                padding: 20px;
                text-align: center;
                color: #666;
                font-style: italic;
                background: #f8f9fa;
                border-radius: 6px;
                margin: 20px;
            }
            
            /* Search container styling */
            .search-container {
                padding: 20px 30px;
                background: #f8f9fa;
                border-bottom: 1px solid #e1e5e9;
                display: flex;
                align-items: center;
                gap: 20px;
                flex-wrap: wrap;
            }

            .search-box {
                flex: 1;
                min-width: 300px;
                padding: 12px 16px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 1em;
                transition: border-color 0.2s;
                background: white;
            }

            .search-box:focus {
                outline: none;
                border-color: #0078d4;
                box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.25);
            }

            .search-box::placeholder {
                color: #6c757d;
            }
            
            /* Responsive Design */
            @media (max-width: 768px) {
                .rule-flow {
                    flex-direction: column;
                    gap: 8px;
                }
                
                .flow-section {
                    width: 100%;
                    margin: 0;
                }
                
                .flow-arrow {
                    min-width: auto;
                    width: 100%;
                }
                
                .arrow-line {
                    width: 100%;
                    transform: rotate(90deg);
                    margin: 4px 0;
                }
                
                .arrow-line::after {
                    transform: rotate(90deg);
                    right: calc(50% - 4px);
                    top: -6px;
                }
                
                .rule-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
                
                .rule-status {
                    width: 100%;
                    justify-content: space-between;
                }
                
                .subnets-container {
                    max-width: none;
                    min-width: auto;
                }
                
                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
                
                .table-container {
                    padding: 15px;
                }
                
                th, td {
                    padding: 8px 6px;
                    font-size: 0.9em;
                }
                
                .hierarchical-container {
                    padding: 15px;
                }
                
                .resolver-header {
                    padding: 15px;
                }
                
                .resolver-meta,
                .ruleset-meta {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
                
                .resolver-details,
                .ruleset-details {
                    grid-template-columns: 1fr;
                    gap: 10px;
                }
                
                .ruleset-header {
                    margin-left: 15px;
                    padding: 12px 15px;
                }
                
                .ruleset-content {
                    margin-left: 15px;
                }
                
                .rules-table-container {
                    padding: 15px;
                }
                
                .rules-table th,
                .rules-table td {
                    padding: 8px 6px;
                    font-size: 0.85em;
                }
                
                .controls-container {
                    flex-direction: column;
                    width: 100%;
                    margin-left: 0;
                }
                
                .control-btn {
                    width: 100%;
                    margin-bottom: 5px;
                }
            }
            
            @media (max-width: 480px) {
                .stats-grid {
                    grid-template-columns: 1fr;
                }
                
                .header h1 {
                    font-size: 2em;
                }
                
                .container {
                    margin: 10px;
                    border-radius: 6px;
                }
                
                .nsg-rule {
                    padding: 10px;
                }
                
                .rule-name {
                    font-size: 0.9em;
                }
                
                .resolver-info {
                    flex-direction: column;
                    gap: 10px;
                }
                
                .expand-icon {
                    margin-top: 0;
                }
                
                .resolver-title h3 {
                    font-size: 1.2em;
                }
                
                .ruleset-title h4 {
                    font-size: 1.1em;
                }
            }
            
            /* Status badge enhancements for private endpoints */
            .status-badge {
                padding: 3px 8px;
                border-radius: 4px;
                font-size: 0.75em;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                display: inline-block;
            }
            
            .status-badge.status-success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .status-badge.status-pending {
                background: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            .status-badge.status-error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .status-badge.status-warning {
                background: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            .status-badge.status-unknown {
                background: #e2e3e5;
                color: #383d41;
                border: 1px solid #d6d8db;
            }
            
            /* Contains AI-generated edits. */
