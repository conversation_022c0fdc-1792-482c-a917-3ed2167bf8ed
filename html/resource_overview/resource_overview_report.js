/**
 * Resource Overview Report - Dynamic JavaScript Renderer
 * This script handles the dynamic rendering and interaction for Resource Overview reports
 */

class ResourceOverviewReport {
    constructor() {
        this.data = window.hierarchicalData;
        this.stats = window.summaryStats;
        this.filters = {
            subscription: '',
            resourceGroup: '',
            resourceType: '',
            search: ''
        };
        this.expandedItems = new Set();
        
        this.init();
    }
    
    init() {
        this.renderSummaryStats();
        this.populateFilters();
        this.renderHierarchy();
        this.setupEventListeners();
        this.updateFilteredCount();
    }
    
    renderSummaryStats() {
        const container = document.getElementById('summaryStats');
        
        const statCards = [
            {
                icon: '📊',
                number: this.stats.total_resources.toLocaleString(),
                label: 'Total Resources',
                color: 'blue'
            },
            {
                icon: '📁',
                number: this.stats.total_subscriptions.toLocaleString(),
                label: 'Subscriptions',
                color: 'green'
            },
            {
                icon: '📂',
                number: this.stats.total_resource_groups.toLocaleString(),
                label: 'Resource Groups',
                color: 'yellow'
            },
            {
                icon: '🏷️',
                number: this.stats.total_resource_types.toLocaleString(),
                label: 'Resource Types',
                color: 'purple'
            }
        ];
        
        container.innerHTML = statCards.map(card => `
            <div class="stat-card">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">${card.icon}</div>
                    <div>
                        <div class="stat-number text-${card.color}-400">${card.number}</div>
                        <div class="stat-label">${card.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    populateFilters() {
        const subscriptions = new Set();
        const resourceGroups = new Set();
        const resourceTypes = new Set();
        
        this.data.forEach(sub => {
            subscriptions.add(sub.subscription_name);
            
            sub.resource_groups.forEach(rg => {
                resourceGroups.add(rg.name);
                
                rg.resource_types.forEach(rt => {
                    resourceTypes.add(rt.type);
                });
            });
        });
        
        this.populateSelect('subscriptionFilter', Array.from(subscriptions).sort());
        this.populateSelect('resourceGroupFilter', Array.from(resourceGroups).sort());
        this.populateSelect('resourceTypeFilter', Array.from(resourceTypes).sort());
    }
    
    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        const currentOptions = Array.from(select.querySelectorAll('option:not(:first-child)'));
        currentOptions.forEach(option => option.remove());
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });
    }
    
    renderHierarchy() {
        const container = document.getElementById('resourceHierarchy');
        const filteredData = this.getFilteredData();
        
        if (filteredData.length === 0) {
            container.innerHTML = `
                <div class="text-center py-12 text-gray-500">
                    <div class="text-4xl mb-4">🔍</div>
                    <h3 class="text-lg font-medium mb-2 text-gray-700">No resources found</h3>
                    <p class="text-gray-500">Try adjusting your filters to see more resources.</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = filteredData.map(subscription => 
            this.renderSubscription(subscription)
        ).join('');
    }
    
    renderSubscription(subscription) {
        const subId = `sub-${subscription.subscription_id}`;
        const isExpanded = this.expandedItems.has(subId);
        
        return `
            <div class="mb-4">
                <div class="hierarchy-header subscription" onclick="resourceReport.toggleExpand('${subId}')">
                    <div class="hierarchy-content">
                        <div class="hierarchy-left">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}" id="${subId}-icon">▶</span>
                            <span class="ml-3 text-lg font-semibold">📁 ${subscription.subscription_name}</span>
                        </div>
                        <div class="hierarchy-right">
                            <span class="count-badge large subscription">${subscription.resource_count.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                <div id="${subId}-content" class="${isExpanded ? '' : 'hidden'} ml-4">
                    ${subscription.resource_groups.map(rg => this.renderResourceGroup(rg, subId)).join('')}
                </div>
            </div>
        `;
    }
    
    renderResourceGroup(resourceGroup, parentId) {
        const rgId = `${parentId}-rg-${resourceGroup.name}`;
        const isExpanded = this.expandedItems.has(rgId);
        
        return `
            <div class="mb-3">
                <div class="hierarchy-header resource-group" onclick="resourceReport.toggleExpand('${rgId}')">
                    <div class="hierarchy-content">
                        <div class="hierarchy-left">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}" id="${rgId}-icon">▶</span>
                            <span class="ml-3 font-medium">📂 ${resourceGroup.name}</span>
                        </div>
                        <div class="hierarchy-right">
                            <span class="count-badge resource-group">${resourceGroup.resource_count.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                <div id="${rgId}-content" class="${isExpanded ? '' : 'hidden'} ml-4">
                    ${resourceGroup.resource_types.map(rt => this.renderResourceType(rt, rgId)).join('')}
                </div>
            </div>
        `;
    }
    
    renderResourceType(resourceType, parentId) {
        const rtId = `${parentId}-rt-${resourceType.type.replace(/[^a-zA-Z0-9]/g, '_')}`;
        const isExpanded = this.expandedItems.has(rtId);
        
        return `
            <div class="mb-2">
                <div class="hierarchy-header resource-type" onclick="resourceReport.toggleExpand('${rtId}')">
                    <div class="hierarchy-content">
                        <div class="hierarchy-left">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}" id="${rtId}-icon">▶</span>
                            <span class="ml-3">📊 ${resourceType.type}</span>
                        </div>
                        <div class="hierarchy-right">
                            <span class="count-badge resource-type">${resourceType.resource_count.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                <div id="${rtId}-content" class="${isExpanded ? '' : 'hidden'} ml-4">
                    ${resourceType.resources.map(resource => this.renderResource(resource)).join('')}
                </div>
            </div>
        `;
    }
    
    renderResource(resource) {
        // Safely get resource properties with fallbacks
        const resourceName = resource.name || 'Unknown';
        const resourceLocation = resource.location || 'Unknown';
        const resourceId = resource.id || '';
        const resourceType = resource.type || 'Unknown';
        const resourceKind = resource.kind || '';
        const resourceSku = resource.sku ? (resource.sku.name || resource.sku.tier || JSON.stringify(resource.sku)) : '';
        const tags = resource.tags || {};
        
        // Extract resource group from ID if available
        const resourceGroupMatch = resourceId.match(/\/resourceGroups\/([^\/]+)/);
        const resourceGroup = resourceGroupMatch ? resourceGroupMatch[1] : (resource.resourceGroup || 'Unknown');
        
        // Create a short ID for display (last part of the full resource ID)
        const shortId = resourceId.split('/').pop() || resourceName;
        
        // Format tags for display
        const tagEntries = Object.entries(tags);
        const tagsDisplay = tagEntries.length > 0 ? 
            tagEntries.slice(0, 3).map(([key, value]) => `${key}:${value}`).join(', ') + 
            (tagEntries.length > 3 ? '...' : '') : 'None';
        
        return `
            <div class="resource-item bg-white border border-blue-200 rounded-lg p-3 mb-2 hover:bg-blue-50 transition-colors shadow-sm">
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2">
                            <span class="mr-2 text-blue-500">📦</span>
                            <span class="font-medium text-gray-900 truncate">${resourceName}</span>
                            ${resourceKind ? `<span class="ml-2 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">${resourceKind}</span>` : ''}
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-600">
                            <div class="flex items-center">
                                <span class="w-16 text-gray-500 flex-shrink-0">Type:</span>
                                <span class="font-mono text-gray-700 truncate">${resourceType}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-16 text-gray-500 flex-shrink-0">Location:</span>
                                <span class="text-gray-700">${resourceLocation}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-16 text-gray-500 flex-shrink-0">Group:</span>
                                <span class="text-gray-700 truncate">${resourceGroup}</span>
                            </div>
                            ${resourceSku ? `
                            <div class="flex items-center">
                                <span class="w-16 text-gray-500 flex-shrink-0">SKU:</span>
                                <span class="text-gray-700 truncate">${resourceSku}</span>
                            </div>
                            ` : ''}
                            <div class="flex items-center col-span-1 md:col-span-2">
                                <span class="w-16 text-gray-500 flex-shrink-0">Tags:</span>
                                <span class="text-gray-700 truncate">${tagsDisplay}</span>
                            </div>
                            ${resourceId ? `
                            <div class="flex items-start col-span-1 md:col-span-2">
                                <span class="w-16 text-gray-500 flex-shrink-0">ID:</span>
                                <span class="font-mono text-xs text-gray-600 break-all">${resourceId}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="ml-4 flex-shrink-0 text-right">
                        <div class="text-xs text-gray-500 mb-1">
                            ${shortId}
                        </div>
                        ${resourceKind || resourceSku ? `
                        <div class="text-xs text-gray-500">
                            ${[resourceKind, resourceSku].filter(Boolean).join(' · ')}
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }
    
    getFilteredData() {
        const searchTerm = this.filters.search ? this.filters.search.toLowerCase() : '';
        const result = [];
        
        for (const subscription of this.data) {
            // Filter by subscription
            if (this.filters.subscription && subscription.subscription_name !== this.filters.subscription) {
                continue;
            }
            
            const filteredSubscription = {
                ...subscription,
                resource_groups: [],
                resource_count: 0
            };
            
            for (const rg of subscription.resource_groups) {
                // Filter by resource group
                if (this.filters.resourceGroup && rg.name !== this.filters.resourceGroup) {
                    continue;
                }
                
                const filteredResourceGroup = {
                    ...rg,
                    resource_types: [],
                    resource_count: 0
                };
                
                for (const rt of rg.resource_types) {
                    // Filter by resource type
                    if (this.filters.resourceType && rt.type !== this.filters.resourceType) {
                        continue;
                    }
                    
                    const filteredResourceType = {
                        ...rt,
                        resources: [],
                        resource_count: 0
                    };
                    
                    // Apply search filter
                    const filteredResources = rt.resources.filter(resource => {
                        if (!searchTerm) return true;
                        
                        const searchableFields = [
                            resource.name,
                            resource.type,
                            resource.id,
                            resource.location,
                            resource.kind,
                            resource.resourceGroup,
                            ...(resource.tags ? Object.values(resource.tags) : [])
                        ].filter(Boolean).map(field => field.toLowerCase());
                        
                        return searchableFields.some(field => field.includes(searchTerm));
                    });
                    
                    if (filteredResources.length > 0) {
                        filteredResourceType.resources = filteredResources;
                        filteredResourceType.resource_count = filteredResources.length;
                        filteredResourceGroup.resource_types.push(filteredResourceType);
                        filteredResourceGroup.resource_count += filteredResources.length;
                    }
                }
                
                if (filteredResourceGroup.resource_types.length > 0) {
                    filteredSubscription.resource_groups.push(filteredResourceGroup);
                    filteredSubscription.resource_count += filteredResourceGroup.resource_count;
                }
            }
            
            if (filteredSubscription.resource_groups.length > 0) {
                result.push(filteredSubscription);
            }
        }
        
        return result;
    }
    
    updateFilteredCount() {
        const filteredData = this.getFilteredData();
        const totalResources = filteredData.reduce((sum, sub) => sum + sub.resource_count, 0);
        document.getElementById('filteredCount').textContent = totalResources.toLocaleString();
    }
    
    toggleExpand(itemId) {
        if (this.expandedItems.has(itemId)) {
            this.expandedItems.delete(itemId);
        } else {
            this.expandedItems.add(itemId);
        }
        
        const content = document.getElementById(`${itemId}-content`);
        const icon = document.getElementById(`${itemId}-icon`);
        
        if (content && icon) {
            content.classList.toggle('hidden');
            icon.classList.toggle('expanded');
        }
    }
    
    setupEventListeners() {
        // Filter event listeners
        document.getElementById('subscriptionFilter').addEventListener('change', (e) => {
            this.filters.subscription = e.target.value;
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        
        document.getElementById('resourceGroupFilter').addEventListener('change', (e) => {
            this.filters.resourceGroup = e.target.value;
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        
        document.getElementById('resourceTypeFilter').addEventListener('change', (e) => {
            this.filters.resourceType = e.target.value;
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        
        // Search box event listener
        document.getElementById('resourceSearch').addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        
        // Clear filters
        document.getElementById('clearFilters').addEventListener('click', () => {
            document.getElementById('subscriptionFilter').value = '';
            document.getElementById('resourceGroupFilter').value = '';
            document.getElementById('resourceTypeFilter').value = '';
            document.getElementById('resourceSearch').value = '';
            
            this.filters = {
                subscription: '',
                resourceGroup: '',
                resourceType: '',
                search: ''
            };
            
            this.renderHierarchy();
            this.updateFilteredCount();
        });
        
        // Expand/Collapse all
        document.getElementById('expandAll').addEventListener('click', () => this.expandAll());
        document.getElementById('collapseAll').addEventListener('click', () => this.collapseAll());
    }
    
    applyFilter(filterType, value) {
        // Set the filter value (empty string for "All" options)
        this.filters[filterType] = value;
        
        // When a filter is applied, re-populate dependent filters if needed
        if (filterType === 'subscription') {
            // Reset dependent filters when subscription changes
            this.filters.resourceGroup = '';
            this.filters.resourceType = '';
            document.getElementById('resourceGroupFilter').value = '';
            document.getElementById('resourceTypeFilter').value = '';
        }
        
        // Re-render everything with new filters
        this.renderHierarchy();
        this.updateFilteredCount();
    }
    
    clearAllFilters() {
        // Reset filter state to empty values
        this.filters = { 
            subscription: '', 
            resourceGroup: '', 
            resourceType: '' 
        };
        
        // Reset dropdown values to "All" options
        document.getElementById('subscriptionFilter').value = '';
        document.getElementById('resourceGroupFilter').value = '';
        document.getElementById('resourceTypeFilter').value = '';
        
        // Force re-population of filter dropdowns with full data
        this.populateFilters();
        
        // Re-render hierarchy with original unfiltered data
        this.renderHierarchy();
        this.updateFilteredCount();
    }
    
    matchesSearch(item) {
        if (!this.filters.search) return true;
        
        const searchTerm = this.filters.search.toLowerCase();
        const nameMatch = item.name.toLowerCase().includes(searchTerm);
        const typeMatch = item.type?.toLowerCase().includes(searchTerm);
        const idMatch = item.id?.toLowerCase().includes(searchTerm);
        
        return nameMatch || typeMatch || idMatch;
    }
    
    matchesFilters(item) {
        return (
            this.matchesSubscription(item) &&
            this.matchesResourceGroup(item) &&
            this.matchesResourceType(item) &&
            this.matchesSearch(item)
        );
    }
    
    expandAll() {
        const allItems = document.querySelectorAll('[id$="-content"]');
        allItems.forEach(item => {
            const itemId = item.id.replace('-content', '');
            this.expandedItems.add(itemId);
            item.classList.remove('hidden');
            
            const icon = document.getElementById(`${itemId}-icon`);
            if (icon) {
                icon.classList.add('expanded');
            }
        });
    }
    
    collapseAll() {
        this.expandedItems.clear();
        const allItems = document.querySelectorAll('[id$="-content"]');
        allItems.forEach(item => {
            item.classList.add('hidden');
        });
        
        const allIcons = document.querySelectorAll('.expand-icon');
        allIcons.forEach(icon => {
            icon.classList.remove('expanded');
        });
    }
}

// Initialize the report
let resourceReport;
document.addEventListener('DOMContentLoaded', () => {
    resourceReport = new ResourceOverviewReport();
});

/* Contains AI-generated edits. */
