/* Azure Resource Overview - CSS Styles */

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #bfdbfe;
}

.stat-number {
    font-size: 1.875rem;
    font-weight: bold;
    color: #2563eb;
}

.stat-label {
    font-size: 0.875rem;
    color: #4b5563;
    margin-top: 0.25rem;
}

.hierarchy-item {
    border-left: 2px solid #bfdbfe;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

.hierarchy-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
    border: 1px solid #bfdbfe;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    min-height: 60px;
    padding-left: 20px;
    padding-right: 24px;
}

.hierarchy-header:hover {
    background: #dbeafe;
}

.hierarchy-header.subscription {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hierarchy-header.subscription:hover {
    background: #bfdbfe;
}

.hierarchy-header.resource-group {
    background: #dcfce7;
    border-left: 4px solid #22c55e;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hierarchy-header.resource-group:hover {
    background: #bbf7d0;
}

.hierarchy-header.resource-type {
    background: #fed7aa;
    border-left: 4px solid #f97316;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hierarchy-header.resource-type:hover {
    background: #fdba74;
}

.hierarchy-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-shrink: 0;
    min-width: 120px;
    margin-left: auto;
    padding-left: 32px;
}

.hierarchy-left {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    padding-right: 32px;
    max-width: calc(100% - 160px);
}

.hierarchy-content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 32px;
    justify-content: space-between;
}

.count-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 600;
    background: #2563eb;
    color: white;
    border: 1px solid #3b82f6;
    min-width: 4rem;
    justify-content: center;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 700;
    letter-spacing: 0.5px;
    margin-left: 16px;
    float: right;
}

.count-badge.large {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
    font-weight: bold;
    background: #1d4ed8;
    border-color: #2563eb;
    min-width: 5rem;
    font-size: 18px;
    margin-left: 24px;
}

.count-badge.subscription {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-color: #3b82f6;
    color: white;
}

.count-badge.resource-group {
    background: linear-gradient(135deg, #059669, #047857);
    border-color: #22c55e;
    color: white;
}

.count-badge.resource-type {
    background: linear-gradient(135deg, #ea580c, #c2410c);
    border-color: #f97316;
    color: white;
}

.resource-item {
    transition: all 0.2s;
    background: white;
    border: 1px solid #dbeafe;
    border-radius: 0.5rem;
}

.resource-item:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #93c5fd;
    background: #dbeafe;
}

.expand-icon {
    transition: transform 0.2s;
    color: #2563eb;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

.hidden {
    display: none !important;
}

.filtered-out {
    display: none !important;
}

/* Blue/white theme form controls */
select, input {
    background: white;
    border: 1px solid #93c5fd;
    color: #111827;
}

select:focus, input:focus {
    border-color: #3b82f6;
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Blue/white theme buttons */
button {
    background: #dbeafe;
    color: #1d4ed8;
    border: 1px solid #93c5fd;
}

button:hover {
    background: #bfdbfe;
    border-color: #60a5fa;
}

/* Search box styles */
.search-container {
    margin-bottom: 1rem;
    width: 100%;
}

.search-box {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #1f2937;
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: border-color 0.15s ease-in-out;
}

.search-box:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-box::placeholder {
    color: #9ca3af;
}

/* Contains AI-generated edits. */
