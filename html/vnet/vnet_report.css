body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1800px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #0078d4, #106ebe);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
}

.header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 30px;
    background: #fafafa;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #0078d4;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #0078d4;
    margin: 0;
}

.stat-label {
    color: #666;
    margin: 5px 0 0 0;
    font-size: 0.9em;
}

.table-container {
    padding: 30px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    table-layout: auto;
}

th {
    background: #0078d4;
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: 500;
    cursor: pointer;
    position: relative;
    user-select: none;
    transition: background-color 0.2s;
}

th:hover {
    background: #106ebe;
}

th.sortable:after {
    content: ' ↕';
    opacity: 0.5;
}

th.sort-asc:after {
    content: ' ↑';
    opacity: 1;
}

th.sort-desc:after {
    content: ' ↓';
    opacity: 1;
}

td {
    padding: 12px 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

tr:hover {
    background-color: #f8f9fa;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    text-transform: uppercase;
}

.status-succeeded {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-updating {
    background: #fff3cd;
    color: #856404;
}

.subnet-count {
    text-align: center;
    font-weight: bold;
    color: #0078d4;
}

.subnets-detail {
    width: auto;
    min-width: 450px;
    max-width: none;
}

.subnets-container {
    min-width: 450px;
    width: auto;
}

.subnet-item {
    margin-bottom: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #0078d4;
}

.subnet-prefix {
    display: block;
    font-size: 0.85em;
    color: #666;
    margin-top: 2px;
}

.nsg-badge, .rt-badge {
    display: inline-block;
    padding: 2px 6px;
    margin: 2px 2px 0 0;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: 500;
}

.nsg-badge {
    background: #e3f2fd;
    color: #1565c0;
}

.rt-badge {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* NSG Rules Styles */
.nsg-rules-container {
    margin-top: 8px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    background: #ffffff;
}

.nsg-rules-header {
    font-weight: 600;
    font-size: 0.8em;
    color: #333;
    padding: 10px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    border-bottom: 1px solid #dee2e6;
}

.nsg-rules-header:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.toggle-icon {
    font-size: 0.8em;
    transition: transform 0.3s ease;
    color: #0078d4;
    font-weight: bold;
}

.nsg-rules-list {
    padding: 12px;
    background: #ffffff;
    border-radius: 0 0 6px 6px;
    overflow: visible;
}

.direction-header {
    font-weight: 700;
    font-size: 0.75em;
    color: #0078d4;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 8px 0 6px 0;
    padding: 4px 8px;
    background: rgba(0,120,212,0.1);
    border-radius: 4px;
    border-left: 3px solid #0078d4;
}

.direction-separator {
    height: 1px;
    background: linear-gradient(to right, transparent, #dee2e6, transparent);
    margin: 12px 0;
}

.nsg-rule {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8em;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.nsg-rule:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.nsg-rule.custom-rule {
    background: linear-gradient(135deg, #fff8e1, #ffecb3);
    border-left: 4px solid #ff9800;
}

.nsg-rule.default-rule {
    background: linear-gradient(135deg, #f5f5f5, #eeeeee);
    border-left: 4px solid #757575;
}

.rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.rule-main-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.rule-name {
    font-weight: 700;
    color: #333;
    font-size: 1em;
}

.rule-priority {
    font-size: 0.8em;
    color: #666;
    background: rgba(0,0,0,0.05);
    padding: 2px 6px;
    border-radius: 10px;
    display: inline-block;
}

.rule-status {
    display: flex;
    gap: 8px;
    align-items: center;
}

.rule-access {
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rule-access.allow {
    background: #4caf50;
    color: white;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.rule-access.deny {
    background: #f44336;
    color: white;
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.rule-direction {
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 12px;
    background: #e3f2fd;
    color: #1565c0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.rule-flow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    margin-top: 4px;
}

.flow-section {
    flex: 1;
    text-align: center;
    padding: 6px 8px;
    background: rgba(0,120,212,0.05);
    border-radius: 4px;
    border: 1px solid rgba(0,120,212,0.1);
}

.flow-label {
    font-size: 0.7em;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.flow-value {
    font-weight: 700;
    color: #333;
    font-size: 0.9em;
    margin-bottom: 2px;
    word-break: break-all;
}

.flow-port {
    font-size: 0.75em;
    color: #666;
    font-weight: 500;
}

.flow-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    min-width: 50px;
}

.protocol-badge {
    background: #6f42c1;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.arrow-line {
    width: 40px;
    height: 2px;
    background: #6f42c1;
    position: relative;
}

.arrow-line::after {
    content: '';
    position: absolute;
    right: -6px;
    top: -3px;
    width: 0;
    height: 0;
    border-left: 8px solid #6f42c1;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

.subnet-main {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.tags-container {
    max-width: 200px;
}

.tag {
    display: inline-block;
    padding: 2px 6px;
    margin: 2px 2px 2px 0;
    background: #e9ecef;
    border-radius: 3px;
    font-size: 0.75em;
    color: #495057;
}

.search-container {
    padding: 20px 30px;
    background: #fafafa;
    border-bottom: 1px solid #eee;
}

.search-box {
    width: 100%;
    max-width: 400px;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-box:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.no-results {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rule-flow {
        flex-direction: column;
        gap: 8px;
    }
    
    .flow-section {
        width: 100%;
        margin: 0;
    }
    
    .flow-arrow {
        min-width: auto;
        width: 100%;
    }
    
    .arrow-line {
        width: 100%;
        transform: rotate(90deg);
        margin: 4px 0;
    }
    
    .arrow-line::after {
        transform: rotate(90deg);
        right: calc(50% - 4px);
        top: -6px;
    }
    
    .rule-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .rule-status {
        width: 100%;
        justify-content: space-between;
    }
    
    .subnets-container {
        max-width: none;
        min-width: auto;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-container {
        padding: 15px;
    }
    
    th, td {
        padding: 8px 6px;
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .container {
        margin: 10px;
        border-radius: 6px;
    }
    
    .nsg-rule {
        padding: 10px;
    }
    
    .rule-name {
        font-size: 0.9em;
    }
}

/* Contains AI-generated edits. */
