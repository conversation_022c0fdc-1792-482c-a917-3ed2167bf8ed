/**
 * Resource Directory Account Report - Hierarchical Tree Renderer
 */
class ResourceAccountReport {
    constructor() {
        this.tree = window.resourceAccountTree;
        this.stats = window.resourceAccountStats;
        this.expandedFolders = new Set();
        this.init();
    }

    init() {
        this.renderSummaryStats();
        this.renderTree();
    }

    renderSummaryStats() {
        const container = document.getElementById('summaryStats');
        const statCards = [
            { icon: '👥', number: this.stats.total_accounts.toLocaleString(), label: 'Total Accounts', color: 'orange' },
            { icon: '✅', number: this.stats.active_accounts.toLocaleString(), label: 'Active Accounts', color: 'green' },
            { icon: '⏸️', number: this.stats.suspended_accounts.toLocaleString(), label: 'Suspended Accounts', color: 'red' },
            { icon: '📁', number: this.stats.total_folders.toLocaleString(), label: 'Total Folders', color: 'blue' }
        ];
        container.innerHTML = statCards.map(card => `
            <div class="stat-card">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">${card.icon}</div>
                    <div>
                        <div class="stat-number text-${card.color}-400">${card.number}</div>
                        <div class="stat-label">${card.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderTree() {
        const container = document.getElementById('accountTree');
        let html = `
            <div class="mb-4 flex gap-4">
                <input id="accountSearch" type="text" placeholder="Search accounts and folders..." class="border py-2 px-4 rounded flex-1" />
                <button id="expandAll" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">Expand All</button>
                <button id="collapseAll" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Collapse All</button>
            </div>
            <div class="tree-container bg-white rounded shadow">`;

        html += this.renderTreeNodes(this.tree, 0);
        html += `</div>`;

        container.innerHTML = html;
        this.attachEventListeners();
    }

    renderTreeNodes(nodes, depth) {
        let html = '';
        nodes.forEach(node => {
            html += this.renderTreeNode(node, depth);
        });
        return html;
    }

    renderTreeNode(node, depth) {
        const hasChildren = node.children && node.children.length > 0;
        const hasAccounts = node.accounts && node.accounts.length > 0;
        const isExpanded = this.expandedFolders.has(node.folder_id);
        const indentClass = `ml-${depth * 4}`;

        let html = `
            <div class="tree-node">
                <div class="folder-header ${indentClass} py-2 px-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer flex items-center"
                     data-folder-id="${node.folder_id}" onclick="resourceAccountReport.toggleFolder('${node.folder_id}')">
                    ${hasChildren || hasAccounts ?
                        `<span class="expand-icon mr-2 text-gray-500">${isExpanded ? '▼' : '▶'}</span>` :
                        '<span class="mr-4"></span>'
                    }
                    <span class="folder-icon mr-2">📁</span>
                    <span class="folder-name font-medium">${node.folder_name}</span>
                    <span class="ml-auto text-sm text-gray-500">
                        ${node.accounts.length} accounts
                        ${hasChildren ? `, ${node.children.length} subfolders` : ''}
                    </span>
                </div>`;

        if (isExpanded) {
            // Render accounts in this folder
            if (hasAccounts) {
                html += `<div class="accounts-container ${indentClass} ml-4">`;
                node.accounts.forEach(account => {
                    html += this.renderAccount(account, depth + 1);
                });
                html += `</div>`;
            }

            // Render child folders
            if (hasChildren) {
                html += this.renderTreeNodes(node.children, depth + 1);
            }
        }

        html += `</div>`;
        return html;
    }

    renderAccount(account, depth) {
        const indentClass = `ml-${depth * 4}`;
        return `
            <div class="account-card ${indentClass} p-4 mb-2 border rounded shadow bg-white">
                <div class="flex items-center mb-2">
                    <span class="account-icon mr-2">👤</span>
                    <span class="font-medium text-lg">${account.name || 'Unknown'}</span>
                </div>
                <div class="text-gray-600"><strong>Account ID:</strong> ${account.id || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Account Name:</strong> ${account.account_name || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Status:</strong> <span class="status-badge ${this.getStatusClass(account.status)}">${account.status || 'Unknown'}</span></div>
                <div class="text-gray-600"><strong>Type:</strong> ${account.type || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Join Method:</strong> ${account.join_method || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Join Time:</strong> ${account.join_time || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Modify Time:</strong> ${account.modify_time || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Folder ID:</strong> ${account.folder_id || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Resource Dir ID:</strong> ${account.resource_directory_id || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Identity Info:</strong> ${account.identity_information || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Resource Path:</strong> ${account.resource_directory_path || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Tags:</strong> ${(account.tags || []).map(tag => `${tag.Key}:${tag.Value}`).join(', ')}</div>
                <div class="text-gray-600"><strong>Location:</strong> ${account.location || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Email Status:</strong> ${account.email_status || 'Unknown'}</div>
                <div class="text-gray-600"><strong>Secure Mobile:</strong> ${account.has_secure_mobile_phone}</div>
                <div class="text-gray-600"><strong>Request ID:</strong> ${account.request_id || 'Unknown'}</div>
            </div>`;
    }

    getStatusClass(status) {
        const statusLower = (status || '').toLowerCase();
        if (statusLower.includes('success') || statusLower.includes('active')) {
            return 'text-green-600 bg-green-100 px-2 py-1 rounded';
        } else if (statusLower.includes('suspended')) {
            return 'text-red-600 bg-red-100 px-2 py-1 rounded';
        } else {
            return 'text-gray-600 bg-gray-100 px-2 py-1 rounded';
        }
    }

    toggleFolder(folderId) {
        if (this.expandedFolders.has(folderId)) {
            this.expandedFolders.delete(folderId);
        } else {
            this.expandedFolders.add(folderId);
        }
        this.renderTree();
    }

    expandAll() {
        this.collectAllFolderIds(this.tree).forEach(id => {
            this.expandedFolders.add(id);
        });
        this.renderTree();
    }

    collapseAll() {
        this.expandedFolders.clear();
        this.renderTree();
    }

    collectAllFolderIds(nodes) {
        let ids = [];
        nodes.forEach(node => {
            ids.push(node.folder_id);
            if (node.children) {
                ids = ids.concat(this.collectAllFolderIds(node.children));
            }
        });
        return ids;
    }

    attachEventListeners() {
        const searchInput = document.getElementById('accountSearch');
        const expandAllBtn = document.getElementById('expandAll');
        const collapseAllBtn = document.getElementById('collapseAll');

        searchInput.addEventListener('input', () => {
            this.filterTree(searchInput.value.toLowerCase());
        });

        expandAllBtn.addEventListener('click', () => {
            this.expandAll();
        });

        collapseAllBtn.addEventListener('click', () => {
            this.collapseAll();
        });
    }

    filterTree(searchTerm) {
        const treeNodes = document.querySelectorAll('.tree-node');
        treeNodes.forEach(node => {
            const text = node.textContent.toLowerCase();
            const shouldShow = !searchTerm || text.includes(searchTerm);
            node.style.display = shouldShow ? 'block' : 'none';
        });
    }
}

let resourceAccountReport;
document.addEventListener('DOMContentLoaded', () => {
    resourceAccountReport = new ResourceAccountReport();
});
